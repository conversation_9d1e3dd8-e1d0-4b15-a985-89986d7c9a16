<?php


namespace app\common\service\room;


use app\common\returnConfig\Code;
use app\common\service\admin\ShopAdminService;
use app\common\service\distribution\DistributionBillService;
use app\common\service\distribution\DistributionUserService;
use app\common\service\finance\SysAccountService;
use app\common\service\finance\SysCashJournalService;
use app\common\service\hook\RoomHookService;
use app\common\service\intermediary\IntermediaryRoomPriceService;
use app\common\service\intermediary\IntermediaryRoomPriceServiceService;
use app\common\service\lose\LoseBillService;
use app\common\service\message\MessageUtil;
use app\common\service\rent\UserRentService;
use app\common\service\room\team\RoomBillPriceDateUserService;
use app\common\service\shop\ShopService;
use app\common\service\shopStatement\HandoverRecordService;
use app\common\service\shopStatement\HandoverService;
use app\common\service\shopStatement\ShopStatementDetailService;
use app\common\service\shopStatement\StatementNameService;
use app\common\service\store\StoreBillDetailService;
use app\common\service\store\StoreBillService;
use app\common\service\store\StoreGoodsService;
use app\common\service\sysSetting\BillSourceService;
use app\common\service\sysSetting\ShopDateService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\sysSetting\UserSourceService;
use app\common\service\thridSystem\ThirdSystemApiService;
use app\common\service\thridSystem\ThirdSystemService;
use app\common\service\user\MemberRightInterestService;
use app\common\service\user\MemberUtilService;
use app\common\service\user\UserCommonService;
use app\common\service\user\UserWxService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\BillType;
use app\common\utils\dataDictionary\CacheTag;
use app\common\utils\dataDictionary\MemberRightInterest;
use app\common\utils\dataDictionary\MessageType;
use app\common\utils\dataDictionary\RoomBillDetailType;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomLogLevel;
use app\common\utils\dataDictionary\RoomLogType;
use app\common\utils\dataDictionary\RoomRecordStatus;
use app\common\utils\dataDictionary\RoomSaleType;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\StatementName;
use app\common\utils\dataDictionary\StoreBillStatus;
use app\common\utils\dataDictionary\ThirdSystem;
use app\common\utils\dataDictionary\ThirdSystemApi;
use app\common\utils\dataDictionary\UserSource;
use app\common\utils\DebugUtil;
use app\common\utils\request\RequestUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;
use think\Db;
use think\Exception;

class RoomBillUtil
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    /**
     * 获取离店时间
     * @param $shopId
     * @param $enterTime
     * @param $roomSaleTypeId
     * @param $grade
     * @param $times
     * @return array
     */
    public function getBillLeaveTime($shopId, $enterTime, $roomSaleTypeId, $grade, $times)
    {
        $date = date('Y-m-d H:i:s', $enterTime);
//        DebugUtil::log($date);
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
        $memberRightInterest = MemberRightInterestService::getInstance()->getMemberRightInterest($grade, MemberRightInterest::YCTF);
        if ($memberRightInterest && $memberRightInterest->status == 1) {
            $delay_time = $memberRightInterest->value;//会员延迟特权
        } else {
            $delay_time = '';//会员延迟特权
        }
        $sign = $roomSaleType->sign;
        $leaveTimeStr = $this->getLeaveTime($shopId);
        if (empty($delay_time)) {
            $delay_time = $leaveTimeStr;
        }
        if ($sign == RoomSaleType::HOUR) {
            //钟点房
            $stay_time = $roomSaleType->stay_time;
            $leaveTimePlan = $enterTime + $stay_time * 3600;
        } elseif ($sign == RoomSaleType::STANDARD) {
            //标准日租
            //凌晨6点前入住，算前一天的入住
            $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
            $settingEnterHour = $enterTimeSetting->property['value'];
            $enterDate = date('Y-m-d', $enterTime);
            $settingEnterHourTime = strtotime("$enterDate $settingEnterHour");
            if ($enterTime < $settingEnterHourTime) {
                $date = TimeUtil::getInstance()->getDate($date, -1);
            }
            $tempDate = TimeUtil::getInstance()->getDate($date, $times, 'Y-m-d') . " $delay_time";
            $leaveTimePlan = strtotime($tempDate);
        } elseif ($sign == RoomSaleType::LONG_STANDARD) {
            //长租
            //凌晨6点前入住，算前一天的入住
            $tempDate = TimeUtil::getInstance()->getNextMonthDay($date, $times) . " $delay_time";
            $leaveTimePlan = strtotime($tempDate);
        } else {
            //会议室
            $stay_time = $roomSaleType->stay_time;
            $leaveTimePlan = $enterTime + $stay_time * 3600;
        }
        $endTime = $leaveTimePlan - 1;
        $leaveTimePlan = $leaveTimePlan - 1;
        return array($endTime, $leaveTimePlan);
    }

    /**
     * 修改入住时间
     * @param $billId
     * @param $enterTime
     */
    public function updateEnterTime($billId, $enterTime)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'enter_time' => $enterTime]);
    }

    /**
     * 离店时间
     * @param $billId
     * @param $leaveTime
     * @param $closeAdminId
     * @param $remark
     */
    public function updateLeaveTime($billId, $leaveTime, $closeAdminId, $remark)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billAmount = $bill->bill_amount;
        $amount_reduction = $bill->amount_reduction;
        $add_reduce_money = $bill->add_reduce_money;
        $finishAmount = $billAmount - $amount_reduction - $add_reduce_money;
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'leave_time' => $leaveTime, 'admin_id_close' => $closeAdminId, 'finish_memo' => $remark, 'finish_amount' => $finishAmount]);
    }

    /**
     * 关闭订单
     * @param $billId
     * @param $adminId
     * @param $remark
     */
    public function closeBill($billId, $adminId, $remark)
    {
        //修改订单状态
        $this->updateBillStatus($billId, RoomBillStatus::FINISHED);
        //更新离店时间，退房管理员、退房备注
        $this->updateLeaveTime($billId, time(), $adminId, $remark);
        //更新主房
        RoomBillConnectService::getInstance()->updateIsMain($billId);
        $finishAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 1, null);
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'finish_amount' => $finishAmount]);

    }

    public function noClearCloseBill($billId, $adminId, $remark)
    {
        //修改订单状态
        $this->updateBillStatus($billId, RoomBillStatus::NO_CLEARING);
        //更新离店时间，退房管理员、退房备注
        $this->updateLeaveTime($billId, time(), $adminId, $remark);
        //更新主房
        RoomBillConnectService::getInstance()->updateIsMain($billId);
        $finishAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 1, null);
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'finish_amount' => $finishAmount]);
    }

    /**
     * 修改订单房间
     * @param $billId
     * @param $roomTypeId
     * @param $roomId
     */
    public function updateRoom($billId, $roomTypeId, $roomId)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'room_type_id' => $roomTypeId, 'room_id' => $roomId]);
        RoomFundUtil::getInstance()->updateFundRoomId($billId, $roomId);
    }

    public function updateRoomId($billId, $roomId)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'room_id' => $roomId]);
    }

    /**
     * 修改订单状态
     * @param $billId
     * @param $billStatus
     */
    public function updateBillStatus($billId, $billStatus)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'bill_status' => $billStatus]);
    }

    /**
     * 更新已支付押金
     * @param $billId
     * @param $alreadyPayCashPledge
     */
    public function updateAlreadyPayCashPledge($billId, $alreadyPayCashPledge)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'already_pay_cash_pledge' => $alreadyPayCashPledge]);
    }

    /**
     * 更新入住人数
     * @param $billId
     * @param $userCount
     */
    public function updateUserCount($billId, $userCount)
    {
        DebugUtil::log("更新用户数量：$billId -  $userCount");
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'user_count' => $userCount]);
    }

    /**
     * 修改入住时长
     * @param $billId
     * @param $stayTime
     * @param $leaveTimePlan
     */
    public function updateStayTime($billId, $stayTime, $leaveTimePlan)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'stay_time' => $stayTime, 'leave_time_plan' => $leaveTimePlan]);
    }

    /**
     * 评论状态
     * @param $billId
     * @param $status
     */
    public function updateEvaluateStatus($billId, $status)
    {
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'evaluate_status' => $status]);
    }


    /**
     * 预定转入住
     * @param $billId
     * @param $roomTypeId
     * @param $roomId
     */
    public function bookToStay($billId, $roomTypeId, $roomId)
    {
        //更新订单状态
        //更新入住时间
        //更新订单房间
        RoomBillService::getInstance()->editRoomBill([
            'id' => $billId,
            'bill_status' => RoomBillStatus::STAYING,
            'room_type_id' => $roomTypeId,
            'room_id' => $roomId,
            'enter_time' => \time(),
        ]);
        RoomFundUtil::getInstance()->updateFundRoomId($billId, $roomId);
    }

    /**
     * 检查是否是最后一个订单
     * @param $billId
     * @return bool
     */
    public function checkLastBill($billId)
    {
        $unFinish = [
            RoomBillStatus::TO_BE_CONFIRMED,
            RoomBillStatus::TO_BE_STAY,
            RoomBillStatus::STAYING,
            RoomBillStatus::TO_CHECK_OUT,
        ];
        $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        if (empty($connectBill)) {
            return true;
        } else {
            $connectCode = $connectBill->connect_code;
            $list = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, $unFinish);
            return count($list) == 1;
        }
    }

    /**
     * @param $shopId
     * @param $roomId
     * @param $billId
     * @param $tempBillStatus
     * @param $roomNumber
     * @param $roomTypeName
     * @param $roomStatusList
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getRoomCurrentRecordStatus($shopId, $roomId, $billId, $tempBillStatus, $roomNumber, $roomTypeName, $roomStatusList)
    {
        if ($tempBillStatus == RoomBillStatus::TO_CHECK_OUT) {
            $oldStatus = RoomRecordStatus::TO_CHECK_OUT;
        } elseif ($tempBillStatus == RoomBillStatus::TO_BE_STAY) {
            $oldStatus = RoomRecordStatus::BOOK;
        } elseif (RoomBillStatus::STAYING) {
            $oldStatus = RoomRecordStatus::STAYING;
        } else {
            $oldStatus = RoomRecordStatus::TO_CHECK_OUT;
        }
        $record = RoomStatusRecordService::getInstance()->getRoomBillStatusRecord($shopId, $billId, $roomId, $oldStatus);
        if ($record) {
//			$roomStatus = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, $record->status_id);
            foreach ($roomStatusList as $roomStatus) {
                if ($roomStatus->id == $record->status_id) {
                    $record->color = $roomStatus->color;
                    $record->room_status_name = $roomStatus->status_name;
                    break;
                }
            }
            $record->room_number = $roomNumber;
            $record->room_type_name = $roomTypeName;
        }
        return $record;
    }

    public function getRoomCurrentRecordStatusSimple($shopId, $billId, $roomId, $oldStatus)
    {
        $record = RoomStatusRecordService::getInstance()->getRoomBillStatusRecord($shopId, $billId, $roomId, $oldStatus);
    }


    /**
     * 查询订单欠费情况
     * @param $connectCode
     * @return array
     */
    public function getConnectBillBalance($connectCode)
    {
        $key = "getConnectBillBalance_key_$connectCode";
        if (CacheManager::has($key)) {
            return CacheManager::getCacheValue($key);
        } else {
            $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, null);
            $billIds = [];
            foreach ($connectBill as $itemConnectBill) {
                $tempBillId = $itemConnectBill->bill_id;
                array_push($billIds, $tempBillId);
            }
            $totalConsumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billIds, 1, '');
            $totalPaymentAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billIds, 2, '');
            $totalBillBalance = floatval(sprintf('%.2f', $totalPaymentAmount - $totalConsumeAmount));
            $res = array($totalBillBalance, $totalConsumeAmount, $totalPaymentAmount);
            CacheManager::setCacheValue($key, $res, null, null, 5);
            return $res;
        }
    }

    /**
     * 联房订单信息
     * @param $billId
     * @return array
     */
    public function getConnectBill($billId)
    {
        $totalBillBalance = 0;
        $isMain = 0;
        $connectBillList = [];
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billConnect = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        $totalPaymentAmount = 0;
        $totalConsumeAmount = 0;
        if (empty($billConnect)) {
            $tempRoomId = $bill->room_id;
            $tempRoomTypeId = $bill->room_type_id;
            $tempRoom = RoomService::getInstance()->getRoomById($tempRoomId);
            if (!empty($tempRoom)) {
                $tempRoomNumber = $tempRoom->room_number;
                $tempRoomTypeName = $tempRoom->room_type_name;
            } else {
                $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($tempRoomTypeId);
                $tempRoomTypeName = $tempRoomType->name;
                $tempRoomNumber = '';
            }
            $consumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 1, '');
            $paymentAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 2, '');
            DebugUtil::log($consumeAmount);
            DebugUtil::log($paymentAmount);
            $totalConsumeAmount += $consumeAmount;
            $totalPaymentAmount += $paymentAmount;
            $bill_balance = floatval(sprintf('%.2f', $paymentAmount - $consumeAmount));
            $totalBillBalance += $bill_balance;
            $connectBill = ['bill_id' => $billId, 'already_pay' => $bill->already_pay, 'bill_amount' => $bill->bill_amount,
                'connect_code' => null, 'bill_status' => $bill->bill_status, 'amount_reduction' => $bill->amount_reduction,
                'room_id' => $bill->room_id, 'room_number' => $tempRoomNumber, 'room_type_name' => $tempRoomTypeName, 'main_room' => 1, 'bill_balance' => $bill_balance];
            array_push($connectBillList, $connectBill);
        } else {
            $connectCode = $billConnect->connect_code;
            $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, '');
            foreach ($connectBill as $itemConnectBill) {
                if ($itemConnectBill->bill_id == $billId) {
                    $isMain = $itemConnectBill->is_main;
                }
                $tempBillId = $itemConnectBill->bill_id;
                $tempBill = RoomBillService::getInstance()->getBillById($tempBillId);
                $tempRoomId = $tempBill->room_id;
                $tempRoomTypeId = $tempBill->room_type_id;
                $tempRoom = RoomService::getInstance()->getRoomById($tempRoomId);
                if (!empty($tempRoom)) {
                    $tempRoomNumber = $tempRoom->room_number;
                    $tempRoomTypeName = $tempRoom->room_type_name;
                } else {
                    $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($tempRoomTypeId);
                    $tempRoomTypeName = $tempRoomType->name;
                    $tempRoomNumber = '';
                }
                $consumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempBillId, 1, '');
                $totalConsumeAmount += $consumeAmount;
                $paymentAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempBillId, 2, '');
                $totalPaymentAmount += $paymentAmount;
                $bill_balance = floatval(sprintf('%.2f', $paymentAmount - $consumeAmount));
                $totalBillBalance += $bill_balance;
                $connectBill = ['bill_id' => $tempBillId, 'already_pay' => $tempBill->already_pay, 'bill_amount' => $tempBill->bill_amount,
                    'connect_code' => null, 'bill_status' => $tempBill->bill_status, 'amount_reduction' => $tempBill->amount_reduction,
                    'room_id' => $tempBill->room_id, 'room_number' => $tempRoomNumber, 'room_type_name' => $tempRoomTypeName,
                    'main_room' => $itemConnectBill->is_main, 'bill_balance' => $bill_balance];
                array_push($connectBillList, $connectBill);
            }
        }
        $totalBillBalance = floatval(sprintf('%.2f', $totalBillBalance));
        $totalConsumeAmount = floatval(sprintf('%.2f', $totalConsumeAmount));
        $totalPaymentAmount = floatval(sprintf('%.2f', $totalPaymentAmount));
        return array($connectBillList, $totalBillBalance, $isMain, $totalConsumeAmount, $totalPaymentAmount);
    }

    /**
     * 查询未完成联房订单
     * @param $billId
     * @return array|false|\PDOStatement|string|\think\Collection
     */
    public function getUnfinishedConnectBills($billId)
    {
        $billConnect = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        if (empty($billConnect)) {
            return [];
        } else {
            $connectCode = $billConnect->connect_code;
            return RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
        }
    }

    /**
     * 已完成转为走结
     * @param $billId
     * @throws MyException
     */
    public function finishBillToNoClear($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::FINISHED) {
            throw new MyException(Code::FAIL, "已完成订单才能转为走结");
        }
        $this->updateBillStatus($billId, RoomBillStatus::NO_CLEARING);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "完成转为走结", '走结', RoomLogType::OTHER, $billId);
    }


    public function clearingBillCheckOut($shopIdZong, $billId, $adminId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        $billSourceId = $bill->bill_source;
        $shopId = $bill->shop_id;
        if ($billStatus != RoomBillStatus::NO_CLEARING) {
            throw new MyException(Code::FAIL, "走结订单才能转为已完成");
        }
        $this->updateBillStatus($billId, RoomBillStatus::FINISHED);
        $lastBill = RoomBillUtil::getInstance()->checkLastBill($billId);
        $differenceAmount = RoomFundUtil::getInstance()->checkFundIsEquals($billId, true);
        if ($differenceAmount != 0) {
            throw new MyException(Code::FAIL, "未平账");
        }
        if (!$lastBill) {
            RoomFundUtil::getInstance()->checkPreAuthDetail($shopIdZong, $shopId, $billId, $billSourceId, false);
        } else {
            RoomFundUtil::getInstance()->checkPreAuthDetail($shopIdZong, $shopId, $billId, $billSourceId, true);
        }
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "结账", '结账', RoomLogType::OTHER, $billId);
        UserRentService::getInstance()->checkUserRent($shopId, $billId);
        LoseBillService::getInstance()->checkLoseBill($shopId, $billId);
        RoomHookService::getInstance()->adminCheckOut($billId, $adminId);
    }

    /**
     * 检查当前是否能办理入住
     * @param $enterTime
     * @param $roomSaleTypeId
     * @param $roomSaleTypeSign
     * @throws MyException
     */
    public function checkStayTimeLimit($enterTime, $roomSaleTypeId, $roomSaleTypeSign)
    {
        $enterDate = date('Y-m-d', $enterTime);
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
        $typeSign = $roomSaleType->sell_type_sign;
        if ($typeSign != $roomSaleTypeSign) {
            throw new MyException(Code::FAIL, "接口请求错误");
        }
        $startTimeLimitStr = $roomSaleType->start_time_limit;
        $startTimeLimit = strtotime("$enterDate $startTimeLimitStr");
        $endTimeLimitStr = $roomSaleType->end_time_limit;
        $endTimeLimit = strtotime("$enterDate $endTimeLimitStr");
        if ($startTimeLimit > $endTimeLimit) {
            $startTimeLimit -= 24 * 3600;
        }
        if (!$roomSaleType->sell_type_sign == RoomSaleType::LONG_STANDARD) {
            if ($enterTime < $startTimeLimit || $enterTime > $endTimeLimit) {
                $msg = "入住时间为 " . date("H:i:s", $startTimeLimit) . '到' . date("H:i:s", $endTimeLimit);
                throw new MyException(Code::FAIL, $msg);
            }
        }
    }

    /**
     * 验证接口是否请求正确
     * @param $billId
     * @param $roomSaleTypeSign
     * @throws MyException
     */
    public function validateSaleType($billId, $roomSaleTypeSign)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $sale_type_id = $bill->sale_type_id;
        $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($sale_type_id);
        $sign = $saleType->sell_type_sign;
        $sell_type_name = $saleType->sell_type_name;
        if ($sign != $roomSaleTypeSign) {
            throw new MyException(Code::FAIL, "请使用 $sell_type_name 接口");
        }
    }


    /**
     * 检查预定为到
     * @param $shopId
     */
    public function checkBookNotArrive($shopId, $cancel)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::TO_BE_STAY);
        foreach ($bills as $bill) {
            $tempBillId = $bill->id;
            $tempLeaveTimePlan = $bill->leave_time_plan;
            $roomId = $bill->room_id;
            if ($tempLeaveTimePlan < time()) {
                if ($cancel) {
                    $this->updateBillStatus($tempBillId, RoomBillStatus::BOOK_NOT_ARRIVE);
                    RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "预定为到", '预定未到', RoomLogType::OTHER, $tempBillId);
                } else {
                    $this->updateBillStatus($tempBillId, RoomBillStatus::CANCELED);
                    $recordStatus = RoomStatusRecordService::getInstance()->getCurrentRoomStatusRecord($tempBillId, $roomId);
                    if (!empty($recordStatus)) {
                        RoomStatusRecordService::getInstance()->delRoomStatus($recordStatus->id);
                    }
                    RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "预定为到取消订单", '预定未到', RoomLogType::OTHER, $tempBillId);
                }
            }
        }
    }

    /**
     * 检查超时续房一小时
     * @param $shopIdZong
     * @param $shopId
     * @throws MyException
     */
    public function checkOverTime($shopIdZong, $shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::STAYING);
        $shopDate = ShopDateService::getInstance()->getCurrentShopDate($shopId);
        $date = $shopDate->date;
        $shopDateStartTime = $shopDate->start_time;
        $now = \time();
        foreach ($bills as $bill) {
            $tempBillId = $bill->id;
            $tempRoomId = $bill->room_id;
            $tempRoomTypeId = $bill->room_type_id;
            $tempLeaveTime = $bill->leave_time_plan;
            $roomSaleTypeId = $bill->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $saleTypeSign = $saleType->sign;
            if ($saleTypeSign == RoomSaleType::CONFERENCE_ROOM && $shopDateStartTime > $tempLeaveTime) {
//                DebugUtil::log("over 1");
                continue;
            }
            $hours = $saleType->hours;
            $extendList = RoomBillExtendRecordService::getInstance()->getRoomBillExtendRecordList($tempBillId, $date, 1);
            $totalSecond = 0;
            foreach ($extendList as $itemExtend) {
                $totalSecond += $itemExtend->time_length * 3600;
            }
            if ($tempLeaveTime + $totalSecond > $now) {
//                DebugUtil::log("over 2");
                continue;
            }
            $extendHours = 0;
            $stayTime = $bill->stay_time;
            $extendRecords = RoomBillExtendRecordService::getInstance()->getRoomBillExtendRecordList($tempBillId, $date, 1);
            foreach ($extendRecords as $extendRecord) {
                $extendHours += $extendRecord->time_length;
            }
            if ($extendHours >= $hours) {
//                DebugUtil::log("over 3");
                //续房小时封顶后不再续房，在夜审是续房一天
                continue;
            }
//            DebugUtil::log("over 4");
            RoomBillExtendRecordService::getInstance()->addRoomBillExtendRecord($tempBillId, 1, 1, $date, 0);
            //修改房间收费记录
            RoomFundUtil::getInstance()->extendRoomForHour($shopIdZong, 0, $tempBillId, 1);
            //修改订单预离时间、入住天数
            $newLeaveTimePlan = $bill->leave_time_plan + 3600;
            $logContent = "加收一小时超时房费 至" . date('Y-m-d H:i:s', $newLeaveTimePlan);
            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '续房', RoomLogType::STAY, $tempBillId, '', $shopId);
        }
    }

    public function fixBillPrice($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::STAYING);
        $shopDate = ShopDateService::getInstance()->getCurrentShopDate($shopId);
        $date = $shopDate->date;
        $dateTime = strtotime($date);
        foreach ($bills as $bill) {
            $tempBillId = $bill->id;
            Db::startTrans();
            try {
                $this->checkRoomPrice($tempBillId, $date);
                Db::commit();
            } catch (Exception $exception) {
                Db::rollback();
            }
        }
    }


    /**
     * 夜审处理续房
     * @param $shopId
     * @param $must
     * @param $shopDate 旧的营业日
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function checkOverTimeExtendOneDay($shopId, $must, $shopDate)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::STAYING);
        foreach ($bills as $bill) {
            $tempBillId = $bill->id;
            $tempRoomId = $bill->room_id;
            $tempBillCode = $bill->bill_code;
            try {
                $tempRoomTypeId = $bill->room_type_id;
                $tempLeaveTime = $bill->leave_time_plan;
                $enterTimePlan = $bill->enter_time_plan;
                $enterDate = date('Y-m-d', $enterTimePlan);
                if (strtotime($shopDate) < strtotime($enterDate)) {
                    continue;
                }
                $roomSaleTypeId = $bill->sale_type_id;
                $stayTime = $bill->stay_time;
                $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
                $saleTypSign = $roomSaleType->sell_type_sign;
                //会议室
                if ($saleTypSign == RoomSaleType::CONFERENCE_ROOM) {
                    continue;
                }
                if ($saleTypSign == RoomSaleType::HOUR) {
                    //钟点房 自动转为全日房
                    $this->billHourToAllDay($tempBillId);
                }
                if ($tempLeaveTime > time()) {
                    //正常订单，记下一天消费
                    $itemRoomDate = RoomBillPriceDateService::getInstance()->getRoomBillPriceDate($tempBillId, $shopDate);
                    /*
                     * 28好夜审失败之后办理了29到30号的订单 到了29号晚上 补上夜审，29号办理的订单跳过不用重复添加消费
                     */
                    if (empty($itemRoomDate) || $itemRoomDate->status == 1) {
                        continue;
                    }
                    DebugUtil::log("$tempBillId 夜审自动续费一天");
                    RoomFundUtil::getInstance()->addConsumeRoomFund($shopId, $tempBillId, $shopDate, "夜审自动续费一天", 0);
                } else {
                    $nextDay = TimeUtil::getInstance()->getDate($shopDate, 1);
                    //超时，续一天
                    $leaveTimeStr = $this->getLeaveTime($shopId);

                    $nextTime = "$nextDay $leaveTimeStr";
                    //修改订单预离时间、入住天数
                    $newLeaveTimePlan = strtotime($nextTime);
                    $newStayTime = $stayTime + 1;
                    $this->updateStayTime($tempBillId, $newStayTime, $newLeaveTimePlan);
                    //修改房间收费记录
                    RoomBillPriceDateService::getInstance()->extendStandardBill($tempBillId, $shopDate, 0);
                    RoomFundUtil::getInstance()->addConsumeRoomFund($shopId, $tempBillId, $shopDate, "夜审自动超时续费一天", 0);
                    //修改房态
                    RoomStatusRecordService::getInstance()->checkRoom($shopId, $tempBillId, $tempRoomTypeId, $tempRoomId, $tempLeaveTime, $newLeaveTimePlan, true);
                    RoomStatusRecordService::getInstance()->updateOverTimeBillLeaveTimePlan($tempBillId, $tempRoomId, $newLeaveTimePlan);
                    $logContent = "夜审续房 1 天 至" . date('Y-m-d H:i:s', $newLeaveTimePlan);
                    RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '续房', RoomLogType::STAY, $tempBillId, '', $shopId);
                }
            } catch (Exception $exception) {
                if ($exception instanceof MyException) {
                    $msg = $exception->msg;
                    $errCode = $exception->code;
                } else {
                    DebugUtil::log($exception->getMessage(), 'error');
                    DebugUtil::log($exception->getTraceAsString(), 'error');
                    $room = RoomService::getInstance()->getRoomById($tempRoomId);
                    if ($room) {
                        $roomNumber = $room->room_number;
                        $msg = "$roomNumber 自动续费失败，请检查是否房间号冲突";
                    } else {
                        $msg = "$tempBillCode 自动续费失败，请检查是否房间号冲突";
                    }
                    $errCode = Code::FAIL;
                }
                if (!$must) {
                    throw new MyException($errCode, $msg);
                }
            }
        }
    }

    public function checkRoomPrice($billId, $shopDate)
    {
        $shopDateTime = strtotime($shopDate);
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $enterTimePlan = $bill->enter_time;
        $leaveTimePlan = $bill->leave_time_plan;
        $roomSaleTypeId = $bill->sale_type_id;
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
        $saleTypSign = $roomSaleType->sell_type_sign;
        //会议室
        if ($saleTypSign == RoomSaleType::CONFERENCE_ROOM) {
            return;
        }
        $lastDayPrice = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateLastDay($billId);
        $lastDayPriceDate = $lastDayPrice->date;
        if (strtotime($lastDayPriceDate) < $shopDateTime - 24 * 3600) {
            $days = TimeUtil::getInstance()->getTimeDifference($lastDayPriceDate, $shopDate, 1);
            for ($i = 1; $i <= $days; $i++) {
                $itemDate = TimeUtil::getInstance()->getDate($lastDayPriceDate, $i);
                RoomBillPriceDateService::getInstance()->extendStandardBill($billId, $itemDate, '');
            }
        }
        $flag = true;
        while ($flag) {
            $dayPriceList = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($billId);
            foreach ($dayPriceList as $item) {
                $tempDate = $item->date;
                $status = $item->status;
                if (strtotime($tempDate) <= $shopDateTime - 24 * 3600 && $status == 0) {
                    $priceDataId = $item->id;
                    $priceRecord = RoomBillDetailService::getInstance()->getDetailByPriceId($priceDataId);
                    if (empty($priceRecord)) {
                        RoomFundUtil::getInstance()->addConsumeRoomFund($shopId, $billId, $tempDate, "夜审自动续费一天", 0);
                    }
                } else {
                    break;
                }
            }
            $flag = false;
        }
    }


    /**
     * 钟点房超时未退
     * @param $billId
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function billHourToAllDay($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $commonCode = $bill->common_code;
        $priceProject = $bill->price_project;
        $billAmount = $bill->bill_amount;
        $saleTypeId = $bill->sale_type_id;//to_sale_type_id
        $roomTypeId = $bill->room_type_id;
        $intermediary_id = $bill->intermediary_id;
        $intermediaryId = $bill->intermediary_id;
        $user = UserCommonService::getInstance()->getUserCommonByCommonCode($commonCode);
        $shopIdZong = $user->shop_id_zong;
        $salType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($saleTypeId);
        $sell_type_sign = $salType->sell_type_sign;
        if ($sell_type_sign != RoomSaleType::HOUR) {
            return;
        }
        $to_sale_type_id = $salType->to_sale_type_id;
        if (!$to_sale_type_id) {
            return;
        }
        $newSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($to_sale_type_id);
        $newSaleTypeName = $newSaleType->name;
        //转下一售卖方式全日房
        $shopDate = ShopDateService::getInstance()->getCurrentShopDate($shopId);
        $date = $shopDate->date;
        $memberGradeId = MemberUtilService::getInstance()->getRoomPriceGrade($shopIdZong, $commonCode, $priceProject);
        $res = RoomPriceUtil::getInstance()->getDayRoomPriceArr($memberGradeId, $to_sale_type_id, $roomTypeId, $intermediaryId, 1, $date);
        $priceList = $res['room_price'][0];
        $roomPrice = $priceList['room_price'];
        $roomService = $res['room_service'][0];
        $originalRoomPrice = $res['original_room_price'];
        $room_service_id = $roomService->id;
        $servicePrice = $roomService->price;
        $tempRoomPrice = $roomPrice + $servicePrice;
        $overTimePrice = $res['over_time_price'];
        $priceDate = RoomBillPriceDateService::getInstance()->getRoomBillPriceDate($billId, $date);
        $newRoomPrice = $roomPrice;
        $billAmount += $newRoomPrice;
        $priceArr = ['date' => $date, 'room_price' => $newRoomPrice, 'original_room_price' => $originalRoomPrice,
            'temp_room_price' => $tempRoomPrice, 'room_service_price' => $servicePrice, 'intermediary_id' => $intermediary_id];
        if (!empty($priceDate)) {
            RoomBillPriceDateService::getInstance()->updateStatus($priceDate->id, 0);
            $oldPrice = $priceDate->room_price;
            $billAmount -= $oldPrice;
        }
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::OVER_TIME);
        $detailTypeId = $detailType->id;
        $overTimeList = RoomBillDetailService::getInstance()->getRoomBillDetails($billId, 1, $detailTypeId, 1);
        foreach ($overTimeList as $overTimeBill) {
            $detailId = $overTimeBill->id;
            $detailEditAmount = $overTimeBill->edit_amount;
            $detailAmount = $overTimeBill->amount;
            $billAmount -= ($detailAmount - $detailEditAmount);
            if ($detailEditAmount > 0) {
                $children = RoomBillDetailService::getInstance()->getRoomBillDetailsByPid($detailId);
                foreach ($children as $child) {
                    RoomBillDetailService::getInstance()->updateStatus($child->id, 0);
                }
            }
            RoomBillDetailService::getInstance()->updateStatus($detailId, 0);
        }
        RoomFundUtil::getInstance()->updateBillMoney($billId, $billAmount);
        RoomBillExtendRecordService::getInstance()->delRecord($billId, 1);
        RoomBillPriceDateService::getInstance()->addRoomBillPrice($billId, [$priceArr], $overTimePrice, $room_service_id);
        RoomBillService::getInstance()->hourToStandard($billId, $room_service_id, $saleTypeId, $to_sale_type_id);
        $logContent = "钟点房超时未退 自动转为 $newSaleTypeName";
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '续房', RoomLogType::STAY, $billId, '', $shopId);
    }


    /**
     * 重新排房查询信息
     * @param $billId
     * @param $connectCode
     * @return array
     */
    public function getConnectBillInfo($billId)
    {
        $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        $list = [];
        if (empty($connectBill)) {
            $clear_color = null;
            $clear_status_name = null;
            $item = $this->setConnectBillInfo($billId);
            $billUser = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
            $shopId = $item->shop_id;
            $item->bill_user = $billUser;
            $roomId = $item->room_id;
            $itemRoom = RoomService::getInstance()->getRoomById($roomId);
            if ($itemRoom) {
                $clearStatus = $itemRoom->clean_status;
                $clearStatusModel = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $clearStatus);
                $clear_color = $clearStatusModel->color;
                $clear_status_name = $clearStatusModel->status_name;
                $itemRoom->clear_color = $clear_color;
                $itemRoom->clear_status_name = $clear_status_name;
            }
            $item->room = $itemRoom;
            array_push($list, $item);
        } else {
            $connectCode = $connectBill->connect_code;
            $connectBills = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, "");
            foreach ($connectBills as $tempConnectBill) {
                $tempId = $tempConnectBill->bill_id;
                $itemRoomBill = $this->setConnectBillInfo($tempId);
                $shopId = $itemRoomBill->shop_id;
                $item = $this->setConnectBillInfo($tempId);
                $billUser = RoomBillUserService::getInstance()->getRoomBillUserList($tempId);
                $item->bill_user = $billUser;
                $consumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempId, 1, '');
                $paymentAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempId, 2, '');
                $item->bill_balance = floatval(sprintf('%.2f', $paymentAmount - $consumeAmount));
                $roomId = $itemRoomBill->room_id;
                $itemRoom = RoomService::getInstance()->getRoomById($roomId);
                if ($itemRoom) {
                    $clearStatus = $itemRoom->clean_status;
                    $clearStatusModel = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $clearStatus);
                    $clear_color = $clearStatusModel->color;
                    $clear_status_name = $clearStatusModel->status_name;
                    $itemRoom->clear_color = $clear_color;
                    $itemRoom->clear_status_name = $clear_status_name;
                }
                $item->room = $itemRoom;
                array_push($list, $item);
            }
        }
        return $list;
    }


    public function setConnectBillInfo($tempId)
    {
        $item = RoomBillService::getInstance()->getBillById($tempId);
        $tempShopId = $item->shop_id;
        $tempShop = ShopService::getInstance()->getShopById($tempShopId);
        $item->shop_name = $tempShop->shop_name;
        $tempRoomNumber = '';
        $tempRoomTypeName = '';
        $tempRoomId = $item->room_id;
        $tempRoomTypeId = $item->room_type_id;
        $tempCommonCode = $item->common_code;
        $intermediary_id = $item->intermediary_id;
        $tempRoom = RoomService::getInstance()->getRoomById($tempRoomId);
        if (!empty($tempRoom)) {
            $tempRoomNumber = $tempRoom->room_number;
            $tempRoomTypeName = $tempRoom->room_type_name;
        } else {
            $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($tempRoomTypeId);
            $tempRoomTypeName = $tempRoomType->name;
        }
        $item->room_number = $tempRoomNumber;
        $item->room_type_name = $tempRoomTypeName;
        $billConnect = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($tempId);
        $connectCode = null;
        $is_main = 0;
        if (!empty($billConnect)) {
            $connectCode = $billConnect->connect_code;
            $is_main = $billConnect->is_main;
        }
        $item->connect_code = $connectCode;
        $billSource = BillSourceService::getInstance()->getBillSourceById($item->bill_source);
        $item->bill_source_name = $billSource->source_name;
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($item->sale_type_id);
        $item->room_sale_type_name = $roomSaleType->name;
        $item->room_sale_type_sign = $roomSaleType->sign;
        $item->room_sale_type_unit = $roomSaleType->unit;
        $serviceList = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($tempId);
        $item->room_date_price = $serviceList;
        $tempRoomServiceId = $serviceList[0]->room_service_id;
        if ($intermediary_id) {
            $tempRoomService = IntermediaryRoomPriceServiceService::getInstance()->getIntermediaryRoomPriceServiceById($tempRoomServiceId);
            $priceId = $tempRoomService->room_price_id;
            $tempRoomServiceList = IntermediaryRoomPriceServiceService::getInstance()->getIntermediaryRoomPriceServiceList($priceId);
            $roomPrice = IntermediaryRoomPriceService::getInstance()->getRoomPriceById($priceId);
        } else {
            $tempRoomService = RoomPriceServiceService::getInstance()->getRoomPriceServiceById($tempRoomServiceId);
            $priceId = $tempRoomService->room_price_id;
            $tempRoomServiceList = RoomPriceServiceService::getInstance()->getRoomPriceServiceList($priceId, 1, $tempShopId);
            $roomPrice = RoomPriceService::getInstance()->getRoomPriceById($priceId);
        }
        $roomPrice->room_service_list = $tempRoomServiceList;
        $item->room_service = $tempRoomService;
        $item->room_price = $roomPrice;
        $item->is_main = $is_main;
        return $item;
    }


    /**
     * 修改房价
     * @param $priceId
     * @param $roomPrice
     * @param $memo
     */
    public function updateRoomPrice($priceId, $roomPrice, $memo)
    {
        $roomPriceDate = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateById($priceId);
        $date = $roomPriceDate->date;
        $billId = $roomPriceDate->bill_id;
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        if ($billStatus == RoomBillStatus::FINISHED || $billStatus == RoomBillStatus::CANCELED) {
            throw new MyException(Code::FAIL, "不允许修改");
        }
        $bllAmount = $bill->bill_amount;
        $shopId = $bill->shop_id;
        $oldPrice = $roomPriceDate->room_price;
        $tempPrice = $roomPrice - $oldPrice;
        $bllAmount += $tempPrice;
        $currShopDate = ShopDateService::getInstance()->getCurrentShopDate($shopId);
        $shopDate = $currShopDate->date;
        $priceDate = $roomPriceDate->date;
        $setting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::MODIFY_LAST_FUND);
        if ($setting->property['status'] == 1 && strtotime($shopDate) > strtotime($priceDate)) {
            throw new MyException(Code::FAIL, "夜审后不允许修改");
        }
        $logContent = "修改 $date 房价 $oldPrice 改为 $roomPrice";
        if ($memo) {
            $logContent .= " 原因：$memo";
        }
        $logContent .= " ;";
        $roomPriceDetail = RoomBillDetailService::getInstance()->getDetailByPriceId($priceId);
        if ($roomPriceDetail) {
            $roomPriceDetailId = $roomPriceDetail->id;
            $editAmount = $roomPriceDetail->edit_amount;
//            $amount = $roomPriceDetail->amount;
            if ($editAmount > $roomPrice) {
                throw new MyException(Code::FAIL, "修改后房费不能小于冲调后的房费");
            }
            RoomBillDetailService::getInstance()->updateAmount($roomPriceDetailId, $roomPrice);
            $shopStatementDetail = ShopStatementDetailService::getInstance()->getByItemBillId($shopId, $billId, $roomPriceDetailId);
            if (!empty($shopStatementDetail)) {
                $shopStatementDetailId = $shopStatementDetail->id;
                $ShopStatementDetailAmount = $shopStatementDetail->amount;
                $shopStatementDetailMemo = $shopStatementDetail->memo . ';' . $logContent;
                if ($ShopStatementDetailAmount != $roomPrice) {
                    ShopStatementDetailService::getInstance()->editShopStatementDetail(['id' => $shopStatementDetailId, 'amount' => $roomPrice, 'memo' => $shopStatementDetailMemo]);
                    //处理账单
                    $roomUserName = $shopStatementDetail->room_username;
                    $this->updateShopStatement($shopId, $billId, $roomPriceDetailId, $date, $oldPrice, $roomPrice, $roomUserName, $memo);
                }
            }
        }
        $tempRoomPrice = $roomPrice + $roomPriceDate->room_service_price;
        RoomBillPriceDateService::getInstance()->updateRoomPrice($priceId, $roomPrice, $tempRoomPrice);

        $roomBillPriceList = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateSimpleList($billId);
        $billAmount = 0;
        foreach ($roomBillPriceList as $item) {
            $temp_room_price = $item->room_price;
            $temp_room_service_price = $item->room_service_price;
            $billAmount += $temp_room_price + $temp_room_service_price;
        }
        //夜审之后不允许修改
        RoomFundUtil::getInstance()->updateBillMoney($billId, $billAmount);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '修改房价', RoomLogType::STAY, $billId);
        RoomHookService::getInstance()->updateRoomPrice($billId, 0);
    }

    private function updateShopStatement($shopId, $billId, $roomPriceDetailId, $date, $oldPrice, $roomPrice, $roomUserName, $memo)
    {
        $logContent = "修改 $roomUserName $date 房价 $oldPrice 改为 $roomPrice";
        if ($memo) {
            $logContent .= " 原因：$memo";
        }
        $diffAmount = ($roomPrice - $oldPrice) * -1;
        $shopStatementDetail = ShopStatementDetailService::getInstance()->getByItemBillId($shopId, $billId, $roomPriceDetailId);
        if (!empty($shopStatementDetail)) {
            $shopStatementDetailId = $shopStatementDetail->id;
            $ShopStatementDetailAmount = $shopStatementDetail->amount;
            $shopStatementDetailMemo = $shopStatementDetail->memo . ';' . $logContent;
            if ($oldPrice != $roomPrice) {
                ShopStatementDetailService::getInstance()->editShopStatementDetail(['id' => $shopStatementDetailId, 'amount' => $roomPrice, 'memo' => $shopStatementDetailMemo]);
                $handoverVersion = $shopStatementDetail->handover_version;
                $handover = HandoverService::getInstance()->getHandoverByVersion($shopId, $handoverVersion);
                if ($handover) {
                    $handoverId = $handover->id;
                    $handoverRecord = HandoverRecordService::getInstance()->getHandoverRecordByHandoverId($handoverId);
                    if ($handoverRecord) {
                        $recordData = $handoverRecord['data'];
                        $consume = $recordData['consume'];
                        $consumeList = $consume['list'];
                        foreach ($consumeList as $consumeIndex => $itemConsume) {
                            $itemConsumeSign = $itemConsume['sign'];
                            if ($itemConsumeSign == "room_consume") {
                                $roomConsumeList = $itemConsume['list'];
                                foreach ($roomConsumeList as $roomListIndex => $itemRoomConsume) {
                                    $itemRoomConsumeSign = $itemRoomConsume['sign'];
                                    if ($itemRoomConsumeSign == "BOOK_ROOM") {
                                        $itemRoomConsumeAmount = $itemRoomConsume['amount'] + $diffAmount;
//                                        $recordData['consume']['list'][$consumeIndex]['list'][$roomListIndex]['amount'] = $itemRoomConsumeAmount;
                                        if (isset($recordData['consume']['list'][$consumeIndex]['list'][$roomListIndex]['modify_amount'])) {
                                            $modifyAmount = $recordData['consume']['list'][$consumeIndex]['list'][$roomListIndex]['modify_amount'];
                                            $recordData['consume']['list'][$consumeIndex]['list'][$roomListIndex]['modify_amount'] = $modifyAmount + $diffAmount;
                                        } else {
                                            $recordData['consume']['list'][$consumeIndex]['list'][$roomListIndex]['modify_amount'] = $diffAmount;
                                        }
                                        if (isset($recordData['consume']['list'][$consumeIndex]['modify_amount'])) {
                                            $totalAmount = $recordData['consume']['list'][$consumeIndex]['modify_amount'];
                                            $recordData['consume']['list'][$consumeIndex]['modify_amount'] = $totalAmount + $diffAmount;
                                        } else {
                                            $recordData['consume']['list'][$consumeIndex]['modify_amount'] = $diffAmount;
                                        }
                                        if (isset($recordData['consume']['modify_amount'])) {
                                            $totalAmount = $recordData['consume']['modify_amount'];
                                            $recordData['consume']['modify_amount'] = $totalAmount + $diffAmount;
                                        } else {
                                            $recordData['consume']['modify_amount'] = $diffAmount;
                                        }
                                        break;
                                    }

                                }
                            }
                            break;
                        }
                        if (isset($data['remark'])) {
                            $recordData['remark'] .= $logContent;
                        } else {
                            $recordData['remark'] = $logContent;
                        }
                        HandoverRecordService::getInstance()->editHandoverRecord(['id' => $handoverRecord['id'], 'data' => $recordData]);
                    }
                }
            }
        }
    }

    /**
     * 设置主房
     * @param $billId
     * @throws MyException
     */
    public function setMainRoom($billId)
    {
        $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        if (!empty($connectBill)) {
            $connectCode = $connectBill->connect_code;
            if ($connectBill->is_main == 1) {
                $list = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, '');
                foreach ($list as $item) {
                    RoomBillConnectService::getInstance()->updateBillIsMain($item->id, 0);
                }
            }
            RoomBillConnectService::getInstance()->updateBillIsMain($connectBill->id, 1);
        }
        RoomHookService::getInstance()->updateRoomUserInfo($billId, null);
    }

    /**
     * 修改订单信息
     * @param $adminId
     * @param $billId
     * @param $linkMan
     * @param $linkPhone
     * @param $billSource
     * @param $otherBillCode
     * @param $memo
     * @throws MyException
     */
    public function updateBillInfo($adminId, $billId, $linkMan, $linkPhone, $billSource, $otherBillCode,
                                   $memo, $secrecy, $distribution_user_id)
    {
        $data = [];
        $logContent = "";
        $phoneChange = false;
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $oldDistributionUserId = $bill->distribution_user_id;
        $oldLinkMan = $bill->link_man;
        $billStatus = $bill->bill_status;
        if ($billStatus == RoomBillStatus::FINISHED){
            throw new MyException(Code::FAIL,"房间账单已结账，不能修改");
        }
        if (!StringUtil::checkEmpty($linkMan)) {
            $data['link_man'] = $linkMan;
            if ($linkMan != $oldLinkMan) {
                $logContent .= "修改联系人 $oldLinkMan 为 $linkMan; ";
            }
        }
        if (!StringUtil::checkEmpty($linkPhone)) {
            StringUtil::checkPhoneNumber($linkPhone);
            $data['link_phone'] = $linkPhone;
            $oldLinkPhone = $bill->link_phone;
            if ($linkPhone != $oldLinkPhone) {
                $logContent .= "修改联系电话 $oldLinkPhone 为 $linkPhone;";
                $phoneChange = true;
            }
        }
        if (!StringUtil::checkEmpty($billSource)) {
            $data['bill_source'] = $billSource;
            $oldBillSource = $bill->bill_source;
            if ($oldBillSource != $billSource) {
                $oldBillSource = BillSourceService::getInstance()->getBillSourceById($oldBillSource);
                $oldBillSourceName = $oldBillSource->source_name;
                $newBillSource = BillSourceService::getInstance()->getBillSourceById($billSource);
                $newBillSourceName = $newBillSource->source_name;
                $logContent .= "修改订单来源 $oldBillSourceName 为 $newBillSourceName; ";
            }
        }
        if (!StringUtil::checkEmpty($otherBillCode)) {
            $data['other_bill_code'] = $otherBillCode;
            $oldOtherBillCode = $bill->other_bill_code;
            if ($otherBillCode != $oldOtherBillCode) {
                $logContent .= "修改第三方订单号 $oldOtherBillCode 为 $otherBillCode; ";
            }
        }
        if (!StringUtil::checkEmpty($memo)) {
            $data['memo'] = $memo;
            $oldMemo = $bill->memo;
            if ($memo != $oldMemo) {
                $logContent .= "修改备注 $oldMemo 为 $memo";
            }
        }
        if (!StringUtil::checkEmpty($secrecy)) {
            $data['secrecy'] = $secrecy;
            $logContent .= "修改为：" . ($secrecy == 1 ? "私密" : "公开");
        }
        if (!StringUtil::checkEmpty($distribution_user_id)) {
            $oldDistributionUserName = null;
            $newDistributionUserName = null;
            if ($oldDistributionUserId) {
                $oldDistributionUser = DistributionUserService::getInstance()->getDistributionUserById($oldDistributionUserId);
                $oldDistributionUserName = $oldDistributionUser->name;
            }
            if ($distribution_user_id == 0) {
                $logContent .= "删除分销人" . $oldDistributionUserName;
                $data['distribution_user_id'] = null;
            } else {
                $newDistributionUser = DistributionUserService::getInstance()->getDistributionUserById($distribution_user_id);
                $newDistributionUserName = $newDistributionUser->name;
                $data['distribution_user_id'] = $distribution_user_id;
                $logContent .= "分销人 " . $oldDistributionUserName . " 修改为 " . $newDistributionUserName;
            }
        }
        if (!empty($data)) {
            //修改了预定电话，更换订单common_code
            $shopId = $bill->shop_id;
            $billCommonCode = $bill->common_code;
            $userCommon = UserCommonService::getInstance()->getUserCommonByCommonCode($billCommonCode);
            $shopIdZong = $userCommon->shop_id_zong;
            $newCommonCode = null;
            if ($phoneChange && strlen($linkPhone) == 11) {
                $updatedUser = UserCommonService::getInstance()->getUserByPhone($shopIdZong, $linkPhone);
                if ($updatedUser) {
                    $updatedUserCommonCode = $updatedUser->common_code;
                    if ($billCommonCode != $updatedUserCommonCode) {
                        $newCommonCode = $updatedUserCommonCode;
                    }
                } else {
                    $userSource = UserSourceService::getInstance()->getUserSourceBySign($shopIdZong, UserSource::BURU);
                    $userSourceId = $userSource->id;
                    $commonUser = MemberUtilService::getInstance()->addNewUser($shopIdZong, $shopId, $userSourceId,
                        $oldLinkMan, $linkPhone, '', null, 1);
                    $newCommonCode = $commonUser->common_code;
                }
            }
            if ($newCommonCode) {
                $data['common_code'] = $newCommonCode;
            }
//            DebugUtil::log($logContent);
            $data['id'] = $billId;
            RoomBillService::getInstance()->editRoomBill($data);
            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '修改订单信息', RoomLogType::OTHER, $billId);
            RoomHookService::getInstance()->updateLinkInfo($billId, 0);
        }
    }

    public function updateBillMemo($billId,$memo,$adminId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $data['id'] = $billId;
        $data['memo'] = $memo;
        $oldMemo = $bill->memo;
        $logContent = "修改备注 $oldMemo 为 $memo";
        RoomBillService::getInstance()->editRoomBill($data);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '修改订单信息', RoomLogType::OTHER, $billId);
    }

    public function delDistributionUserId($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $distribution_user_id = $bill['distribution_user_id'];
        $distribution_user = DistributionUserService::getInstance()->getDistributionUserById($distribution_user_id);
        $name = $distribution_user['name'];
        $shopId = $bill['shop_id'];
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::FINISHED){
            throw new Exception("订单未完成，不能删除");
        }
        $distributionBill = DistributionBillService::getInstance()->getDistributionBillByBillId($shopId, $billId, BillType::BOOK_ROOM);
        if ($distributionBill) {
            $distribution_amount = $distributionBill['distribution_amount'];
            DistributionBillService::getInstance()->delDistributionBillById($distributionBill['id']);
            DistributionUserService::getInstance()->updateUserAmount($distribution_user_id, $distribution_amount * -1);
        }
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'distribution_user_id' => 0]);
        $logContent = "删除分销人 $name";
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '修改订单信息', RoomLogType::OTHER, $billId);

    }


    /**
     * 查询离店时间
     * @param $shopId
     * @return string
     */
    public function getLeaveTime($shopId)
    {
        $setting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::LEAVE_TIME);
        $leaveTimeStr = $setting->property['value'];
        $leaveTimeArr = explode(":", $leaveTimeStr);
        if (count($leaveTimeArr) == 0) {
            $leaveTimeStr = "12:00:00";
        } elseif (count($leaveTimeArr) == 1) {
            $leaveTimeStr = "$leaveTimeStr:00:00";
        } elseif (count($leaveTimeArr) == 2) {
            $leaveTimeStr = "$leaveTimeStr:00";
        } else {
            $leaveTimeStr = "12:00:00";
        }
        return $leaveTimeStr;
    }

    /**
     * 用户生成二维码
     * @param $billId
     * @param $userId
     * @param $type
     * @return mixed
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function userCreateRoomBillQrCode($billId, $userId, $type)
    {
        $wx = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wx->common_code;
        $billUser = RoomBillUserService::getInstance()->getRoomBillMainUser($billId);
        $billUserCommonCode = $billUser->common_code;
        if ($commonCode != $billUserCommonCode) {
            throw new MyException(Code::FAIL, "主入住人才能分享");
        }
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $teamId = $bill->team_id;
        $shop = ShopService::getInstance()->getShopById($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        $qr = RoomBillQrService::getInstance()->getRoomBillQrCode($billId, 0, $type);
        if (empty($qr)) {
            try {
                $id = RoomBillQrService::getInstance()->addRoomBillQrCode($shopIdZong, $shopId, $billId, $type, 0, $teamId, $commonCode);
                $qr = RoomBillQrService::getInstance()->getRoomBillQrById($id);
                //1详情入住码，2同住人，3访客，4快速入住
                $userName = $billUser->name;
                $userPhone = $billUser->phone;
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "分享 $userName $userPhone", '分享二维码', RoomLogType::STAY, $billId);
            } catch (Exception $exception) {
                DebugUtil::log($exception->getMessage(), 'error');
                DebugUtil::log($exception->getTraceAsString(), 'error');
                throw new MyException(Code::FAIL, "二维码生成失败");
            }
        }
        return $qr;
    }


    /**
     * 管理员生成二维码
     * @param $billId
     * @return mixed
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function adminCreateRoomBillQrCode($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $shop = ShopService::getInstance()->getShopById($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        return RoomBillQrService::getInstance()->addRoomBillQrCode($shopIdZong, $shopId, $billId, 1, 0, 0);
    }


    /**
     * 查询预定未到订单
     * @param $shopId
     * @return array
     */
    public function getBookNotArriveBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::TO_BE_STAY);
        $list = [];
        foreach ($bills as $bill) {
            $tempBillId = $bill->id;
            $tempLeaveTimePlan = $bill->leave_time_plan;
            if ($tempLeaveTimePlan < time()) {
                array_push($list, $bill);
            }
        }
        return $list;
    }

    /**
     * 超时未退
     * @param $shopId
     * @return array
     */
    public function getOverTimeBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::STAYING]);
        $list = [];
        foreach ($bills as $bill) {
            if ($bill->leave_time_plan < \time()) {
                array_push($list, $bill);
            }
        }
        return $list;
    }

    /**
     * 申请退房
     * @param $shopId
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getToBeCheckOutBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::TO_CHECK_OUT]);

        return $bills;
    }

    /**
     * 待确认
     * @param $shopId
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getToBeConfirmedBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::TO_BE_CONFIRMED]);

        return $bills;
    }

    /**
     * 待取消
     * @param $shopId
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getToBeCanceledBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::TO_BE_CANCELED]);

        return $bills;
    }

    /**
     * 走结
     * @param $shopId
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getNoClearingBill($shopId)
    {
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::NO_CLEARING]);

        return $bills;
    }

    public function getNoClearingBillListByTime($shopId, $startTime, $endTime)
    {
        $bills = RoomBillService::getInstance()->getNoClearBill($shopId, $startTime, $endTime);
        return $bills;
    }


    /**
     * 订房消费
     * @param $shopId
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getDayRoomConsumeAmount($shopId, $startTime, $endTime)
    {
        //订房
        $totalAmount = 0;
        $roomBillDetailTypes = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeList($shopId, '', 1, '', '');
//		$roomBillModifyDetailTypes = RoomBillDetailTypeService::getInstance()->getRoomBillDetailModifyTypeList($shopId, '', 1, '', '');
//		$roomBillDetailTypes = array_merge($roomBillDetailTypes,$roomBillModifyDetailTypes);
        $roomBillDetailAmountList = [];
        $otherAmount = 0;
        foreach ($roomBillDetailTypes as $billDetailType) {
            $detailTypeSign = $billDetailType->sign;
            $detailTypeId = $billDetailType->id;
            $sys_setting = $billDetailType->sys_setting;
            $tempAmount = RoomBillDetailService::getInstance()->getRoomBillDetailAmountAboutTime(null, $detailTypeId, 1, $startTime, $endTime);
//			$tempEditAmount = RoomBillDetailService::getInstance()->getRoomBillDetailEditAmountAboutTime(null,$detailTypeId, 1, $startTime, $endTime);
            $tempRefundAmount = RoomBillDetailService::getInstance()->getRoomBillDetailRefundAmountAboutTime($detailTypeId, 1, $startTime, $endTime);
            $detailTypeName = $billDetailType->name;
            $tempTotalAmount = $tempAmount - $tempRefundAmount;
            if ($tempTotalAmount == 0 && $sys_setting == 0) continue;
            if ($sys_setting == 0) {
                $otherAmount += $tempTotalAmount;
            } else {
                $roomBillDetailAmountList[] = ['name' => $detailTypeName, 'sign' => $detailTypeSign,
                    'amount' => floatval(sprintf('%.2f', $tempTotalAmount))];
            }
            $totalAmount += $tempTotalAmount;
        }
        $roomBillDetailAmountList[] = ['name' => "其他", 'sign' => "other", 'amount' => floatval(sprintf('%.2f', $otherAmount))];
        return ['list' => $roomBillDetailAmountList, 'name' => '订房模块', 'total_amount' => floatval(sprintf('%.2f', $totalAmount)), 'sign' => "room_consume"];
    }

    /**
     * 酒店超市
     * @param $shopId
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getShopStoreConsumeAmount($shopId, $startTime, $endTime)
    {
        $totalAmount = 0;
        $storeBillAmountList = [];
        $billStatus = [StoreBillStatus::CANCELED, StoreBillStatus::TO_BE_CONFIRMED];
        $list = SysCashJournalService::getInstance()->getAccountStoreAmountAboutTimeList($shopId, null, $startTime, $endTime, null, null, $billStatus, false, null, null);
        foreach ($list as $item) {
            $itemAmount = $item->change;
            $totalAmount += $itemAmount;
        }
        array_push($storeBillAmountList, ['name' => "超市消费", 'sign' => "store_consume",
            'amount' => floatval(sprintf('%.2f', $totalAmount))]);
        return ['list' => $storeBillAmountList, 'name' => '超市模块', 'total_amount' => floatval(sprintf('%.2f', $totalAmount)), 'sign' => "store_consume"];
    }


    public function getDayStoreConsumeAmountDetailList($shopId, $startTime, $endTime, $handoverVersion)
    {
        $billStatus = [StoreBillStatus::CANCELED, StoreBillStatus::TO_BE_CONFIRMED];
        $fundList = SysCashJournalService::getInstance()->getAccountStoreAmountAboutTimeList($shopId, null, $startTime, $endTime, null, null, $billStatus, false, null, null);
        $total = 0;
        $list = [];
        foreach ($fundList as $item) {
            $tempDataList = [];
            $totalAmount = 0;
            $itemAmount = $item->change;
            $totalAmount += $itemAmount;
            $tempBillId = $item->bill_id;
            $goodsDetails = StoreBillDetailService::getInstance()->getStoreBillDetailList($tempBillId, null, null);
            $goodsBill = StoreBillService::getInstance()->getStoreBillById($tempBillId);
            $billSourceId = $goodsBill->bill_source;
            $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
            $sourceName = $billSource->source_name;
            $tempDetailTypeName = "";
            $count = count($goodsDetails);
            $commonCode = $goodsBill->common_code;
            $billCode = $goodsBill->bill_code;
            $room_username = "";
            $adminId = $goodsBill->admin_id;
            if ($adminId == 0) {
                $tempAdminName = "系统";
            } else {
                $admin = ShopAdminService::getInstance()->getAdminById($adminId);
                $adminNickname = $admin->nickname;
                $adminName = $admin->name;
                $tempAdminName = $adminNickname . "[$adminName]";
            }
            if (!empty($commonCode)) {
                $user = UserCommonService::getInstance()->getUserCommonByCommonCode($commonCode);
                $room_username = $user->name;
            }
            $memo = "";
            $createTime = $item->create_time;
            foreach ($goodsDetails as $index => $goodsDetail) {
                $goodsId = $goodsDetail->goods_id;
                $goods = StoreGoodsService::getInstance()->getStoreGoodsById($goodsId);
                $goodsName = $goods->name;
                $tempDetailTypeName .= $goodsName;

                array_push($tempDataList, ['name' => $goodsName, 'amount' => floatval(sprintf('%.2f', $itemAmount)),
                    'bill_source_name' => $sourceName, 'room_username' => $room_username, 'create_time' => $createTime,
                    'admin_name' => $tempAdminName, 'handover_version' => $handoverVersion, 'bill_id' => $tempBillId, 'sign' => "store_consume", "memo" => $memo]);
            }
            //项目名称 项目金额 客源 房号姓名 操作时间 管理员 班次
            if ($totalAmount > 0) {
                $total += floatval(sprintf('%.2f', $totalAmount));
                array_push($list, ['name' => $billCode, 'total_amount' => floatval(sprintf('%.2f', $totalAmount)), 'sign' => "store_consume", 'list' => $tempDataList]);
            }
        }
        DebugUtil::log(json_encode($list));
        return ['total_amount' => floatval(sprintf('%.2f', $total)), 'list' => $list, 'sign' => "store_consume", 'name' => "超市消费"];
    }

    /**
     * 房间消费明细
     * @param $shopId
     * @param $sign
     * @param $startTime
     * @param $endTime
     * @param $handoverVersion
     * @return array
     */
    public function getDayRoomConsumeAmountDetailList($shopId, $sign, $startTime, $endTime, $handoverVersion)
    {
        $totalAmount = 0;
//		$name = null;
        $list = [];
        if ($sign == 'room_consume') {
            $allRoomBillDetailTypes = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeList($shopId, '', 1, '', '');
            $roomBillDetailTypes = [];
            foreach ($allRoomBillDetailTypes as $itemBillDetailType) {
                array_push($roomBillDetailTypes, $itemBillDetailType);
//				if ($itemBillDetailType->sys_setting == 1) {
//					array_push($roomBillDetailTypes, $itemBillDetailType);
//				}
            }
            $name = "订房模块";
        } elseif ($sign == 'other') {
            $allRoomBillDetailTypes = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeList($shopId, '', 1, '', '');
            $roomBillDetailTypes = [];
            foreach ($allRoomBillDetailTypes as $itemBillDetailType) {
                if ($itemBillDetailType->sys_setting == 0) {
                    array_push($roomBillDetailTypes, $itemBillDetailType);
                }
            }
            $name = "其他";
        } elseif ($sign == "store_consume") {
            $name = "超市消费";
        } else {
            $roomBillDetailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, $sign);
            $name = $roomBillDetailType->name;
            $roomBillDetailTypes = [$roomBillDetailType];
        }
        $total = 0;
        foreach ($roomBillDetailTypes as $billDetailType) {
            $detailTypeSign = $billDetailType->sign;
            $sys_setting = $billDetailType->sys_setting;
            $detailTypeName = $billDetailType->name;
            $detailTypeId = $billDetailType->id;
            $tempList = RoomBillDetailService::getInstance()->getRoomBillDetailAboutTime($shopId, '', $detailTypeId, 1, $startTime, $endTime);
            $tempDataList = [];
            $tempTotalAmount = 0;
            foreach ($tempList as $item) {
                $memo = $item->memo;
                $tempAmount = $item->amount;
                $tempEditAmount = $item->edit_amount;
                $tempRefundAmount = $item->refund_amount;
                $createTime = $item->create_time;
                $tempTotal = $tempAmount - $tempEditAmount - $tempRefundAmount;
                $totalAmount += $tempTotal;
                if ($tempTotal == 0 && $sys_setting == 0) continue;
                $tempTotalAmount += $tempTotal;
                $tempBillId = $item->bill_id;
                $tempBill = RoomBillService::getInstance()->getBillById($tempBillId);
                $billSourceId = $tempBill->bill_source;
                $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
                $sourceName = $billSource->source_name;
                $roomId = $tempBill->room_id;
                $roomTypeId = $tempBill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $roomTypeName = $roomType->name;
                if ($roomId == 0) {
                    $roomNumber = $roomTypeName;
                } else {
                    $room = RoomService::getInstance()->getRoomById($roomId);
                    $roomNumber = $room->room_number;
                }
                $linkName = $tempBill->link_man;
                $adminId = $item->admin_id;
                if ($adminId == 0) {
                    $tempAdminName = "系统";
                } else {
                    $admin = ShopAdminService::getInstance()->getAdminById($adminId);
                    $adminNickname = $admin->nickname;
                    $adminName = $admin->name;
                    $tempAdminName = $adminNickname . "[$adminName]";
                }
                //项目名称 项目金额 客源 房号姓名 操作时间 管理员 班次
                array_push($tempDataList, ['name' => $detailTypeName, 'amount' => floatval(sprintf('%.2f', $tempTotal)),
                    'bill_source_name' => $sourceName, 'room_username' => "$roomTypeName $roomNumber [$linkName]", 'create_time' => $createTime,
                    'admin_name' => $tempAdminName, 'handover_version' => $handoverVersion, 'bill_id' => $tempBillId, 'sign' => $detailTypeSign, "memo" => $memo]);
            }
            if ($tempTotalAmount > 0) {
                $total += floatval(sprintf('%.2f', $tempTotalAmount));
                array_push($list, ['name' => $detailTypeName, 'total_amount' => floatval(sprintf('%.2f', $tempTotalAmount)), 'sign' => $detailTypeSign, 'list' => $tempDataList]);
            }
        }
        return ['total_amount' => floatval(sprintf('%.2f', $total)), 'list' => $list, 'sign' => $sign, 'name' => $name];
    }

    /**
     * 免费房
     * @param $shopId
     * @param $sign
     * @param $startTime
     * @param $endTime
     * @param $handoverVersion
     * @return array
     */
    public function getFreeCouponPaymentDetail($shopId, $startTime, $endTime, $handoverVersion)
    {
        $list = [];
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::FREE_ROOM_COUPON);
        $detailTypeId = $detailType->id;
        $detailTypeName = $detailType->name;
        $sign = $detailType->sign;
        $tempList = RoomBillDetailService::getInstance()->getRoomBillDetailAboutTime($shopId, '', $detailTypeId, 2, $startTime, $endTime);
        $totalAmount = 0;
        $tempDataList = [];
        $tempTotalAmount = 0;
        foreach ($tempList as $item) {
            $tempAmount = $item->amount;
            $tempEditAmount = $item->edit_amount;
            $tempRefundAmount = $item->refund_amount;
            $createTime = $item->create_time;
            $tempTotal = $tempAmount - $tempEditAmount - $tempRefundAmount;
            $totalAmount += $tempTotal;
            if ($tempTotal == 0) continue;
            $tempTotalAmount += $tempTotal;
            $tempBillId = $item->bill_id;
            $tempBill = RoomBillService::getInstance()->getBillById($tempBillId);
            $billSourceId = $tempBill->bill_source;
            $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
            $sourceName = $billSource->source_name;
            $roomId = $tempBill->room_id;
            if ($roomId == 0) {
                $roomTypeId = $tempBill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $roomNumber = $roomType->name;
            } else {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $roomNumber = $room->room_number;
            }
            $linkName = $tempBill->link_man;
            $adminId = $item->admin_id;
            if ($adminId == 0) {
                $tempAdminName = "系统";
            } else {
                $admin = ShopAdminService::getInstance()->getAdminById($adminId);
                $adminNickname = $admin->nickname;
                $adminName = $admin->name;
                $tempAdminName = $adminNickname . "[$adminName]";
            }
            //项目名称 项目金额 客源 房号姓名 操作时间 管理员 班次
            array_push($tempDataList, ['name' => $detailTypeName, 'amount' => floatval(sprintf('%.2f', $tempTotal)),
                'bill_source_id' => $billSourceId, 'bill_source_name' => $sourceName, 'room_username' => $roomNumber . "[$linkName]", 'create_time' => $createTime,
                'admin_id' => $adminId, 'admin_name' => $tempAdminName, 'handover_version' => $handoverVersion, 'bill_id' => $tempBillId, 'sign' => $sign,
                'bill_type_id' => BillType::BOOK_ROOM, 'bill_item_id' => $item->id]);
        }
        return ['name' => "免费房代金券", 'total_amount' => floatval(sprintf('%.2f', $tempTotalAmount)), 'sign' => $sign, 'list' => $tempDataList];
    }

    /**
     * 免费房
     * @param $shopId
     * @param $sign
     * @param $startTime
     * @param $endTime
     * @param $handoverVersion
     * @return array
     */
    public function getSelfCouponPaymentDetail($shopId, $startTime, $endTime, $handoverVersion)
    {
        $list = [];
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::SELF_ROOM_COUPON);
        $detailTypeId = $detailType->id;
        $detailTypeName = $detailType->name;
        $sign = $detailType->sign;
        $tempList = RoomBillDetailService::getInstance()->getRoomBillDetailAboutTime($shopId, '', $detailTypeId, 2, $startTime, $endTime);
        $totalAmount = 0;
        $tempDataList = [];
        $tempTotalAmount = 0;
        foreach ($tempList as $item) {
            $tempAmount = $item->amount;
            $tempEditAmount = $item->edit_amount;
            $tempRefundAmount = $item->refund_amount;
            $createTime = $item->create_time;
            $tempTotal = $tempAmount - $tempEditAmount - $tempRefundAmount;
            $totalAmount += $tempTotal;
            if ($tempTotal == 0) continue;
            $tempTotalAmount += $tempTotal;
            $tempBillId = $item->bill_id;
            $tempBill = RoomBillService::getInstance()->getBillById($tempBillId);
            $billSourceId = $tempBill->bill_source;
            $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
            $sourceName = $billSource->source_name;
            $roomId = $tempBill->room_id;
            if ($roomId == 0) {
                $roomTypeId = $tempBill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $roomNumber = $roomType->name;
            } else {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $roomNumber = $room->room_number;
            }
            $linkName = $tempBill->link_man;
            $adminId = $item->admin_id;
            if ($adminId == 0) {
                $tempAdminName = "系统";
            } else {
                $admin = ShopAdminService::getInstance()->getAdminById($adminId);
                $adminNickname = $admin->nickname;
                $adminName = $admin->name;
                $tempAdminName = $adminNickname . "[$adminName]";
            }
            //项目名称 项目金额 客源 房号姓名 操作时间 管理员 班次
            array_push($tempDataList, ['name' => $detailTypeName, 'amount' => floatval(sprintf('%.2f', $tempTotal)),
                'bill_source_id' => $billSourceId, 'bill_source_name' => $sourceName, 'room_username' => $roomNumber . "[$linkName]", 'create_time' => $createTime,
                'admin_id' => $adminId, 'admin_name' => $tempAdminName, 'handover_version' => $handoverVersion, 'bill_id' => $tempBillId, 'sign' => $sign,
                'bill_type_id' => BillType::BOOK_ROOM, 'bill_item_id' => $item->id]);
        }
        return ['name' => "自用房代金券", 'total_amount' => floatval(sprintf('%.2f', $tempTotalAmount)), 'sign' => $sign, 'list' => $tempDataList];
    }

    /**
     * 订房模块信息提醒
     * @param $shopId
     * @return array
     */
    public function getMessageData($shopId)
    {
        //待确认
        $to_be_confirmed_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::TO_BE_CONFIRMED);
        foreach ($to_be_confirmed_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }
        //预定未到
        $book_not_arrive_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::BOOK_NOT_ARRIVE);
        foreach ($book_not_arrive_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }
        //走结
        $no_clearing_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::NO_CLEARING);
        foreach ($no_clearing_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }
        //待取消
        $to_be_canceled_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::TO_BE_CANCELED);
        foreach ($to_be_canceled_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }
        //申请取消
        $to_check_out_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::TO_CHECK_OUT);
        foreach ($to_check_out_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }


        $staying_bills = RoomBillService::getInstance()->getBillList($shopId, RoomBillStatus::STAYING);
        foreach ($staying_bills as $item) {
            $roomTypeId = $item->room_type_id;
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $item->room_type_name = $roomType->name;
            $roomSaleTypeId = $item->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
            $item->sale_type_name = $saleType->name;
        }
        //未上传公安
        $unUploadUserCount = RoomBillUserService::getInstance()->getCount($shopId, null, 0, 1, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
        //超时未离
        $over_time_bills = [];
        //即将退房
        $close_over_time_bills = [];
        $now = \time();
        foreach ($staying_bills as $itemBill) {
            if ($itemBill->leave_time_plan < $now) {
                array_push($over_time_bills, $itemBill);
            } else {
                if ($itemBill->leave_time_plan - $now < 3600 * 2) {
                    array_push($close_over_time_bills, $itemBill);
                }
            }
        }
        $autoConfirmList = [];
        $key = "auto_confirms_$shopId";
        $autoConfirmData = CacheManager::getCacheValue($key, CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId);
        if ($autoConfirmData) {
            DebugUtil::log($autoConfirmData);
            CacheManager::clearAllCacheValue(CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId);
            foreach ($autoConfirmData as $autoId) {
                $tempBill = RoomBillService::getInstance()->getBillInfo($autoId);
                if ($tempBill) {
                    $autoConfirmList[] = $tempBill;
                }
            }
        }
        $to_be_confirmed_count = count($to_be_confirmed_bills);
        $book_not_arrive_count = count($book_not_arrive_bills);
        $no_clearing_count = count($no_clearing_bills);
        $to_be_canceled = count($to_be_canceled_bills);
        $to_check_out_count = count($to_check_out_bills);
        $close_over_time_count = count($close_over_time_bills);
        $over_time = count($over_time_bills);
        $auto_confirms_count = count($autoConfirmList);
        return [
            'to_be_confirmed' => [
                'list' => $to_be_confirmed_bills,
                'count' => $to_be_confirmed_count,
            ],
            'book_not_arrive' => [
                'list' => $book_not_arrive_bills,
                'count' => $book_not_arrive_count,
            ],
            'no_clearing' => [
                'list' => $no_clearing_bills,
                'count' => $no_clearing_count,
            ],
            'to_be_canceled' => [
                'list' => $to_be_canceled_bills,
                'count' => $to_be_canceled,
            ],
            'to_check_out' => [
                'list' => $to_check_out_bills,
                'count' => $to_check_out_count,
            ],
            'close_over_time' => [
                'list' => $close_over_time_bills,
                'count' => $close_over_time_count,
            ],
            'over_time' => [
                'list' => $over_time_bills,
                'count' => $over_time,
            ],
            'auto_confirms' => [
                'list' => $autoConfirmList,
                'count' => $auto_confirms_count,
            ],
            'not_upload_user' => [
                'count' => $unUploadUserCount,
            ],
        ];
    }

    /**
     * 查询间夜数
     * @param $shopId
     * @param $date
     * @return int
     */
    public function getRoomNight($shopId, $date)
    {
        $roomDates = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateListByDate($shopId, $date);
        $total = 0;
        foreach ($roomDates as $roomDate) {
            $itemBillId = $roomDate->bill_id;
            $itemBill = RoomBillService::getInstance()->getBillById($itemBillId);
            $saleTypeId = $itemBill->sale_type_id;
            $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($saleTypeId);
            $sign = $saleType->sell_type_sign;
            if ($sign == RoomSaleType::LONG_STANDARD || $sign == RoomSaleType::STANDARD) {
                $total++;
            }
        }
        return $total;
    }


    /**
     * 修改预定时间
     * @param $shopIdZong
     * @param $shopId
     * @param $billId
     * @param $enterTimePlan
     * @param $times
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function updateBillBookTime($shopIdZong, $shopId, $billId, $enterTimePlan, $times)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::TO_BE_STAY) {
            throw new MyException(Code::FAIL, "预定单才能修改时间");
        }
        $roomId = $bill->room_id;
        $commonCode = $bill->common_code;
        $roomTypeId = $bill->room_type_id;
        $priceProject = $bill->price_project;
        $roomSaleTypeId = $bill->sale_type_id;
        $intermediariesId = $bill->intermediary_id;
        $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
        $saleTypeSign = $saleType->sell_type_sign;
        $priceDate = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($billId, '');
        $roomServiceId = $priceDate[0]->room_service_id;
        $temp_grade = $bill->temp_grade;
        list($endTime, $leaveTimePlan) = RoomBillUtil::getInstance()->getBillLeaveTime($shopId, $enterTimePlan, $roomSaleTypeId, $temp_grade, $times);
        if ($saleTypeSign == RoomSaleType::STANDARD) {
            $enterTimePlanDate = date('Y-m-d H:i:s', $enterTimePlan);
            $leaveTimePlanDate = TimeUtil::getInstance()->getDate($enterTimePlanDate, $times);
            $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
            $settingEnterHour = $enterTimeSetting->property['value'];
            $enterDate = date('Y-m-d', $enterTimePlan);
            $settingEnterHourTime = strtotime("$enterDate $settingEnterHour");
            if ($enterTimePlan < $settingEnterHourTime) {
                $enterTimePlanDate = TimeUtil::getInstance()->getDate($enterTimePlanDate, -1);
            }
            $days = $times;
            list($roomTypeName, $billAmount, $commissionAmount, $overTimePrice, $originalCashPladge, $cashPledge, $priceArr, $grade, $servicePrice, $serviceName) =
                RoomPriceUtil::getInstance()->getStandardRoomPriceByDate($shopIdZong, $commonCode, $enterTimePlan, $priceProject,
                    $roomTypeId, $roomSaleTypeId, $intermediariesId, [], $days, $roomServiceId);
        } elseif ($saleTypeSign == RoomSaleType::LONG_STANDARD) {
            list($roomTypeName, $billAmount, $commissionAmount, $overTimePrice, $originalCashPladge, $cashPledge, $priceArr, $grade, $servicePrice, $serviceName) =
                RoomPriceUtil::getInstance()->getLongStandardRoomPriceByDate($shopIdZong, $commonCode, $enterTimePlan,
                    $priceProject, $roomTypeId, $roomSaleTypeId, $intermediariesId, [], $times, $roomServiceId);
        } elseif ($saleTypeSign == RoomSaleType::HOUR) {
            $times = 1;
            list($roomTypeName, $billAmount, $commissionAmount, $overTimePrice, $originalCashPladge, $cashPledge, $priceArr, $grade, $servicePrice, $serviceName) =
                RoomPriceUtil::getInstance()->getHourRoomPriceByDate($shopIdZong, $commonCode, $enterTimePlan, $priceProject, $roomTypeId, $roomSaleTypeId, $intermediariesId, [], $roomServiceId);
        } else {
            $times = 1;
            list($roomTypeName, $billAmount, $commissionAmount, $overTimePrice, $originalCashPladge, $cashPledge, $priceArr, $grade, $servicePrice, $serviceName) =
                RoomPriceUtil::getInstance()->getConferenceRoomPriceByDate($shopIdZong, $commonCode, $enterTimePlan, $priceProject, $roomTypeId, $roomSaleTypeId, $intermediariesId, [], $roomServiceId);
        }
        RoomStatusRecordService::getInstance()->checkRoom($shopId, $billId, $roomTypeId, $roomId, $enterTimePlan, $leaveTimePlan, true);
        if ($roomId > 0) {
            $record = RoomStatusRecordService::getInstance()->getCurrentRoomStatusRecord($billId, $roomId);
            $recordId = $record->id;
            RoomStatusRecordService::getInstance()->updateStartTimePlanAndEndTimePlan($recordId, $enterTimePlan, $leaveTimePlan);
        }
        RoomBillService::getInstance()->updateBillAmount($billId, $billAmount);
        RoomBillService::getInstance()->editRoomBill(
            ['id' => $billId, 'stay_time' => $times, 'enter_time_plan' => $enterTimePlan, 'leave_time_plan' => $leaveTimePlan]
        );
        RoomBillPriceDateService::getInstance()->delByRoomBillId($billId);
        //添加房费记录
        RoomBillPriceDateService::getInstance()->addRoomBillPrice($billId, $priceArr, $overTimePrice, $roomServiceId);
        $startTimePlanStr = date('Y-m-d H:i:s', $enterTimePlan);
        $logContent = "修改订单入住日期 $times $startTimePlanStr 至" . date('Y-m-d H:i:s', $leaveTimePlan);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '修改预定时间', RoomLogType::STAY, $billId);
        RoomHookService::getInstance()->updateLinkInfo($billId, 0);
    }


    /**
     * 更新消费信息
     * @param $billId
     */
    public function updateActualData($billId)
    {
        $consumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 1, '');
        RoomBillService::getInstance()->updateBillAmount($billId, $consumeAmount);
        $stayTimes = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateCount($billId, 1);
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'stay_time' => $stayTimes]);
    }

    /**
     * 检查订单状态
     * @param $billStatus
     * @param $type
     * @throws MyException
     */
    public function checkBillStatus($billStatus, $type)
    {
        switch ($type) {
            case "check_out":
                if ($billStatus != RoomBillStatus::STAYING && $billStatus != RoomBillStatus::TO_CHECK_OUT) {
                    throw new MyException(Code::FAIL, "入住中、待退房订单才能退房");
                }
                break;
            case "no_clear_check_out":
                if ($billStatus != RoomBillStatus::STAYING && $billStatus != RoomBillStatus::TO_CHECK_OUT) {
                    throw new MyException(Code::FAIL, "入住中、待退房订单才能走结");
                }
                break;
            default:
                break;
        }
    }


    /**
     * 自助入住选择用户人数为
     * @param $userId
     * @param $billId
     * @param $selectUserCount
     */
    public function updateSelectUserCount($userId, $billId, $selectUserCount)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $userCommon = UserCommonService::getInstance()->getUserCommonByCommonCode($commonCode);
        $userPhone = $userCommon->phone;
        $userName = $userCommon->name;
        RoomBillService::getInstance()->updateBillCommonCode($billId, $commonCode, null, null);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "$userName  $userPhone 自助入住选择用户人数为: $selectUserCount",
            "自助入住选择用户人数", RoomLogType::STAY, $billId, 0, $shopId);
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'select_user_count' => $selectUserCount]);
    }

    public function userCheckOut($billId, $billUserId, $adminId)
    {
//		$userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId, 1);
//		if (count($userList) == 1) {
//			throw new MyException(Code::FAIL, "请使用退房");
//		}
        RoomBillUserService::getInstance()->updateUserStatus($billUserId, 2);
        RoomHookService::getInstance()->roomUserLeave($billId, $billUserId, $adminId);
    }

    /**
     * 预定直接完成
     * 作为预定系统，订单在其他系统办理入住
     * @param $shopIdZong
     * @param $billId
     * @param $adminId
     * @param $remark
     * @throws MyException
     */
    public function bookingToFinish($shopIdZong, $billId, $adminId, $remark)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billId = $bill->id;
        $shopId = $bill->shop_id;
        $bookingType = $bill->booking_type;
        $userCommonCode = $bill->common_code;
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::TO_BE_STAY) {
            throw new MyException(Code::FAIL, "当前订单不是待入住订单");
        }
        $roomId = $bill->room_id;
        $logDate = date('Y-m-d H:i:s');
        if ($roomId) {
            RoomStatusRecordService::getInstance()->roomCheckOut($billId, $roomId);
        }
        //修改订单状态，退房时间
        //修改订单状态
        $this->updateBillStatus($billId, RoomBillStatus::FINISHED);
        //更新离店时间，退房管理员、退房备注
        $this->updateLeaveTime($billId, time(), $adminId, $remark);
        //更新主房
        RoomBillConnectService::getInstance()->updateIsMain($billId);
        $billAmount = $bill->bill_amount;
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'finish_amount' => $billAmount]);
        //更新离店时间，退房管理员、退房备注
        //更新主房
        $logContent = $logDate . "预定转完成 $remark";
        $billSourceId = $bill->bill_source;
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '退房', RoomLogType::STAY, $billId);
        RoomBillUserService::getInstance()->checkOut($billId);
        try {
            DistributionBillService::getInstance()->addRoomBill($shopIdZong, $shopId, $billId);
        }catch (MyException $e){
            DebugUtil::log($e->msg,"error");
        }
        RoomHookService::getInstance()->adminCheckOut($billId, $adminId);
    }

    public function noShowToFinish($shopIdZong, $billId, $adminId, $remark)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billId = $bill->id;
        $shopId = $bill->shop_id;
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::NO_SHOW) {
            throw new MyException(Code::FAIL, "当前订单不是待入住订单");
        }
        $roomId = $bill->room_id;
        $logDate = date('Y-m-d H:i:s');
        if ($roomId) {
            RoomStatusRecordService::getInstance()->roomCheckOut($billId, $roomId);
        }
        //修改订单状态，退房时间
        //修改订单状态
        $this->updateBillStatus($billId, RoomBillStatus::FINISHED);
        //更新离店时间，退房管理员、退房备注
        $this->updateLeaveTime($billId, time(), $adminId, $remark);
        //更新主房
        RoomBillConnectService::getInstance()->updateIsMain($billId);
        $billAmount = $bill->bill_amount;
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'finish_amount' => $billAmount]);
        //更新离店时间，退房管理员、退房备注
        //更新主房
        $logContent = $logDate . "noShow转完成 $remark";
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '退房', RoomLogType::STAY, $billId);
        RoomBillUserService::getInstance()->checkOut($billId);
        try {
            DistributionBillService::getInstance()->addRoomBill($shopIdZong, $shopId, $billId);
        }catch (MyException $e){
            DebugUtil::log($e->msg,"error");
        }
        RoomHookService::getInstance()->adminCheckOut($billId, $adminId);
    }
    public function getStayingBills($shopId)
    {
        $key = $shopId . "_staying_bill_code";
        $bills = RoomBillService::getInstance()->getBillList($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
        $startKey = $key . "_start_index";
        if (CacheManager::has($startKey)) {
            $startIndex = CacheManager::getCacheValue($startKey);
        } else {
            $startIndex = 0;
        }
        if ($startIndex * 30 > count($bills)) {
            $startIndex = 0;
        }
        $count = count($bills);
        if (count($bills) > 30) {
            $newList = array_slice($bills, $startIndex * 30, 30);
            CacheManager::setCacheValue($startKey, $startIndex + 1, null, null, null);
        } else {
            $newList = $bills;
        }
        return $newList;
    }

    public function clearStayingBills($shopId)
    {
        $key = $shopId . "_staying_bill_code";
        CacheManager::clearCacheValue($key);
    }


    /**
     * 查询联房数量
     * @param $billId
     * @return int
     */
    public function getConnectBillCount($billId)
    {
        $tempConnectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        $connectCode = $tempConnectBill->connect_code;
        if (empty($connectCode)) {
            return 0;
        } else {
            $connectBills = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connectCode, '');
            return count($connectBills);
        }
    }

    public function getBillInfoByBillCode($billCode)
    {
        $bill = RoomBillService::getInstance()->getBillByBillCode($billCode);
        if (empty($bill)) {
            throw new MyException(Code::FAIL, "订单号错误");
        }
        $roomTypeId = $bill->room_type_id;
        $billSourceId = $bill->bill_source;
        $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
        $billSourceName = $billSource->source_name;
        $billSourceSign = $billSource->sign;
        $bill->bill_source_name = $billSourceName;
        $bill->bill_source_sign = $billSourceSign;
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $roomTypeName = $roomType->name;
        $roomTypeCode = $roomType->room_type_code;
        $bill->room_type_name = $roomTypeName;
        $bill->room_type_code = $roomTypeCode;
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($bill->sale_type_id);
        $bill->room_sale_type_name = $roomSaleType->name;
        $bill->room_sale_type_sign = $roomSaleType->sign;
        $bill->room_sale_type_unit = $roomSaleType->unit;
        $billId = $bill->id;
        $intermediaryId = $bill->intermediary_id;
        $roomId = $bill->room_id;
        $billCode = $bill->bill_code;
        $users = [];
        $usersList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
//		foreach ($usersList as $itemUser) {
//			if (!StringUtil::checkEmpty($itemUser->name) &&
//				!StringUtil::checkEmpty($itemUser->identification_image) &&
//				!StringUtil::checkEmpty($itemUser->name)) {
//				array_push($users, $itemUser);
//			}
//		}
        $bill->users = $usersList;
        $room = null;
        if ($roomId) {
            $room = RoomService::getInstance()->getRoomById($roomId);
        }
        $bill->room = $room;
        $details = [];
        $details = RoomBillDetailService::getInstance()->getDetailList($billId, null, null, 1, null, 0,null, null);
        foreach ($details as $item) {
            $room_bill_type_id = $item->room_bill_type_id;
            $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeById($room_bill_type_id);
            $item->room_bill_type_sign = $detailType->sign;
            $accountId = $item->account_id;
            if ($accountId) {
                $account = SysAccountService::getInstance()->getSysAccountById($item->account_id);
                $item->account_name = $account->account_name;
                $item->account_sign = $account->sign;
                $item->account_type = $account->type;
            } else {
                $item->account_name = "";
                $item->account_sign = "";
                $item->account_type = "";
            }
            $item->bill_code = $billCode;
        }
        $bill->details = $details;

        $roomDatePrice = [];
        $roomDatePrice = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($billId, null);
        foreach ($roomDatePrice as $price) {
            $breakfastCouponList = [];
            $roomPriceId = $price->room_service_id;
            if ($intermediaryId) {
                $service = IntermediaryRoomPriceServiceService::getInstance()->getIntermediaryRoomPriceServiceById($roomPriceId);
            } else {
                $service = RoomPriceServiceService::getInstance()->getRoomPriceServiceById($roomPriceId);
            }
            $serviceName = $service->service_name;
            $price->service_name = $serviceName;
            $breakfastCoupons = $service->breakfast_coupon;
            if ($breakfastCoupons) {
                foreach ($breakfastCoupons as $breakfastCouponRecord) {
                    $breakfastCoupon = BreakfastCouponService::getInstance()->getBreakfastCouponById($breakfastCouponRecord['coupon_id']);
                    array_push($breakfastCouponList, $breakfastCoupon);
                }
            }
            $price->breakfast_coupons = $breakfastCouponList;
        }
        $bill->price = $roomDatePrice;
        $car = [];
        $car = RoomBillCarService::getInstance()->getRoomBillCarByBillId($billId);
        $bill->car = $car;
        return $bill;
    }

    /**
     * 查询入住中的房间
     * @param $userId
     * @return array|false|\PDOStatement|string|\think\Model|null
     */
    public function getUserStayingRoom($userId)
    {
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $shops = ShopService::getInstance()->getAllSysShops(1);
        $room = null;
        foreach ($shops as $shop) {
            $shopId = $shop->id;
            $shopName = $shop->shop_name;
            $roomUser = RoomBillUserService::getInstance()->getStayingUser($shopId, $commonCode, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
            if (!empty($roomUser)) {
                $billId = $roomUser->bill_id;
                $bill = RoomBillService::getInstance()->getBillById($billId);
                $roomId = $bill->room_id;
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room->shop_name = $shopName;
                break;
            }
        }
        return $room;
    }

    /**
     * 入住人报表
     * @param $date
     * @param $shopId
     * @return array
     */
    public function getRoomBillUserReport($date, $shopId)
    {
        $shop = ShopService::getInstance()->getShopById($shopId);
        $shopName = $shop->shop_name;
        $shopDate = ShopDateService::getInstance()->getShopDateByDate($shopId, $date);
        $startTime = $shopDate->start_time;
        $endTime = $shopDate->end_time;
        $billStatus = [RoomBillStatus::STAYING, RoomBillStatus::FINISHED, RoomBillStatus::TO_CHECK_OUT];
        $bills = RoomBillService::getInstance()->getDateStayingBill($shopId, $billStatus, $startTime, $endTime, null, null);
        $userList = [];
        foreach ($bills as $bill) {
            $tempBillId = $bill->bill_id;
            $tempUsers = RoomBillUserService::getInstance()->getRoomBillUserList($tempBillId, [1, 2]);
            foreach ($tempUsers as $tempUser) {
                $tempUser->bill_code = $bill->bill_code;
                array_push($userList, $tempUser);
            }
        }
        $timeInfo = [
            'shop_name' => $shopName,
            'start_time' => $startTime,
            'end_time' => $endTime,
        ];
        $statementName = StatementNameService::getInstance()->getStatementNameBySign($shopId, StatementName::KFZKXXBB);
        $statementNameHeader = $statementName->header;
        $statementNameName = $statementName->name;
        $data = ['list' => $userList, 'sign' => StatementName::KFZKXXBB, 'name' => $statementNameName];
        return array('data' => $data, 'header' => $statementNameHeader, 'remark' => $statementName->remark, 'sign' => $statementName->sign, 'time_info' => $timeInfo);
    }

    /**
     * 添加小程序订房消息
     * @param $billId
     */
    public function addWxBillMessage($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $enterTimePlan = $bill->enter_time_plan;
        $leaveTimePlan = $bill->leave_time_plan;
        $enterTimePlanDate = date('Y-m-d H:i:s', $enterTimePlan);
        $leaveTimePlanDate = date('Y-m-d H:i:s', $leaveTimePlan);
        $billCode = $bill->bill_code;
        $title = "小程序订房";
        $content = "小程序订房预住时间 $enterTimePlanDate/$leaveTimePlanDate $billCode  请及时处理！";
        $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
        MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_BOOKING_ROOM);
    }


    /**
     * 退房前检查其他服务是否完成
     * @param $shopId
     * @param $billId
     * @return void
     * @throws MyException
     */
    public function checkOutCheckService($shopId, $billId)
    {
        //检查租借物品是否归还
        UserRentService::getInstance()->checkUserRent($shopId, $billId);
        //检查赔损物品
        LoseBillService::getInstance()->checkLoseBill($shopId, $billId);
        //检查超市订单
        StoreBillService::getInstance()->checkBill($shopId, $billId);
    }

    /**
     * 系统自动确认订单
     * @param $billId
     * @return void
     * @throws MyException
     */
    public function wxNotifyConfirmRoomBill($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $shopId = $bill->shop_id;
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::TO_BE_CONFIRMED) {
            return;
        }
        RoomBillUtil::getInstance()->updateBillStatus($billId, RoomBillStatus::TO_BE_STAY);
        $linkMan = $bill->link_man;
        $linkPhone = $bill->link_phone;
        $logContent = "系统确认订单 $linkMan $linkPhone";
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, $logContent, '确认订单', RoomLogType::STAY, $billId, 0, $shopId);
        RoomHookService::getInstance()->adminConfirmRoomBill($billId, 0);
    }

    public function updateRoomType($billId, $roomTypeId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        if ($billStatus != RoomBillStatus::TO_BE_STAY) {
            throw new MyException(Code::FAIL, "当前状态无法修改房型");
        }
        $roomId = $bill->room_id;
        if (!empty($roomId)) {
            throw new MyException(Code::FAIL, "请使用换房功能");
        }
        $shopId = $bill->shop_id;
        $oldRoomTypeId = $bill->room_type_id;
        $oldRoomType = RoomTypeService::getInstance()->getRoomTypeById($oldRoomTypeId);
        $oldRoomTypeName = $oldRoomType->name;
        $newRoomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $newRoomTypeName = $newRoomType->name;
        $logContent = "更换房型 $oldRoomTypeName => $newRoomTypeName";
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'room_type_id' => $roomTypeId]);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::EXCEPTION, $logContent, '更换房型', RoomLogType::STAY, $billId, 0, $shopId);
    }

    public function wxChangeRoom($userId, $changeBillId, $qrCode, $confirmStatus)
    {
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        if (empty($commonCode)) {
            throw new MyException(Code::SUCCESS, "请在个人中心授权手机号", 'no_login_bill');
        }
        $qr = RoomBillQrService::getInstance()->getRoomBillQrCodeByCode($changeBillId, $qrCode);
        if (empty($qr)) {
            throw new MyException(Code::DATA_ERROR, "二维码无效");
        }
        $qrType = $qr->type;
        if ($qrType != 6) {
            throw new MyException(Code::DATA_ERROR, "二维码无效");
        }
        $status = $qr->status;
        $qrCommonCode = $qr->common_code;
        if ($qrCommonCode == $commonCode) {
            return;
        }
        if ($status != 0) {
            throw new MyException(Code::DATA_ERROR, "二维码失效");
        }
        RoomBillQrService::getInstance()->updateRoomBillQrCodeStatus($qrCode, $confirmStatus);
        if ($confirmStatus == 1) {
            //入住人信息更换
            $userFromUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($changeBillId, $qrCommonCode);
            $userFromUserId = $userFromUser->id;
            $currentBill = RoomBillService::getInstance()->getStayingBillByCommonCode($commonCode);
            $currentBillId = $currentBill->id;
            $currentBillUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($currentBillId, $commonCode);
            $currentBillUserId = $currentBillUser->id;
            RoomBillService::getInstance()->editRoomBill(['id' => $currentBillUserId, 'common_code' => $qrCommonCode]);
            RoomBillService::getInstance()->editRoomBill(['id' => $userFromUserId, 'common_code' => $qrCommonCode]);
            //缴费信息更换
            $fromUserRoomFundList = RoomBillDetailService::getInstance()->getFundByCommonCode($changeBillId, $qrCommonCode, null, 2, null);
            foreach ($fromUserRoomFundList as $fromItem) {
                RoomBillDetailService::getInstance()->editRoomBillDetail(['id' => $fromItem->id, 'common_code' => $commonCode]);
            }
            $currentUserRoomFundList = RoomBillDetailService::getInstance()->getFundByCommonCode($currentBillId, $commonCode, null, 2, null);
            foreach ($currentUserRoomFundList as $currentItem) {
                RoomBillDetailService::getInstance()->editRoomBillDetail(['id' => $currentItem->id, 'common_code' => $qrCommonCode]);
            }
            //房价更换
            $fromUserDateFund = RoomBillPriceDateUserService::getInstance()->getRoomBillPriceDateUserByCommonCode($changeBillId, $qrCommonCode);
            foreach ($fromUserDateFund as $fromItemDate) {
                RoomBillPriceDateUserService::getInstance()->editRoomBillPriceDateUser(['id' => $fromItemDate->id, 'common_code' => $commonCode]);
            }
            $currentUserDateFund = RoomBillPriceDateUserService::getInstance()->getRoomBillPriceDateUserByCommonCode($currentBillId, $commonCode);
            foreach ($currentUserDateFund as $currentItemDate) {
                RoomBillPriceDateUserService::getInstance()->editRoomBillPriceDateUser(['id' => $currentItemDate->id, 'common_code' => $qrCommonCode]);
            }
            RoomBillService::getInstance()->updateAlreadyPayAmount($changeBillId);
            RoomBillService::getInstance()->updateAlreadyPayAmount($currentBillId);
        }
    }

    /**
     * 获取第三方可用房数
     * @param $shopId
     * @param $startTime
     * @param $endTime
     * @param $roomTypeId
     * @return int|mixed|null
     */
    public function getThirdRoomCount($shopId, $startTime, $endTime, $roomTypeId)
    {
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        if ($thirdSystem && $thirdSystem->sign != ThirdSystem::JINGFEI) {
            $thirdSystemId = $thirdSystem->id;
            $shop = ShopService::getInstance()->getShopById($shopId);
            $hotelCode = $shop->hotel_code;
            $api = ThirdSystemApiService::getInstance()->getThirdSystemApiBySign($thirdSystemId, ThirdSystemApi::GET_USABLE_ROOM_COUNT);
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $roomTypeCode = $roomType->room_type_code;
            $key = $api->key;
            $apiStatus = $api->status;
            if ($apiStatus == 0) {
                return null;
            }
            $url = $api->api_url;
            $requestType = $api->request_type;
            $params = [];
            $params['timestamp'] = time();
            $params['app_secret'] = $key;
            $params['hotel_code'] = $hotelCode;
            $params['enter_time_plan'] = $startTime;
            $params['leave_time_plan'] = $endTime;
            $params['room_type'] = $roomTypeCode;
            $res = RequestUtil::getInstance()->requestData($url, $requestType, $params, true);
            DebugUtil::log($res, 'debug');
            $result = json_decode($res, true);
            $roomCount = $result['data'];
            if (!isset($result['code']) || $result['code'] != 0) {
                return 0;
            }
            return $roomCount;
        }
        return -1;
    }


}