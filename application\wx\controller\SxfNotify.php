<?php

namespace app\wx\controller;

use app\common\returnConfig\Code;
use app\common\service\catering\CateringBillService;
use app\common\service\catering\CateringFundDetailService;
use app\common\service\catering\CateringFundService;
use app\common\service\common\AdminLogService;
use app\common\service\finance\SysAccountService;
use app\common\service\hardware\printer\PrintUtil;
use app\common\service\lose\LoseBillService;
use app\common\service\Pay\PayRecordService;
use app\common\service\Pay\WechatPayService;
use app\common\service\pointMall\PointMallBillFundDetailService;
use app\common\service\recharge\IndependentRechargeSettingService;
use app\common\service\recharge\RechargeFundService;
use app\common\service\room\BreakfastCouponRecordService;
use app\common\service\room\RoomBillDetailPreService;
use app\common\service\room\RoomFundUtil;
use app\common\service\room\team\TeamFundService;
use app\common\service\shop\ShopService;
use app\common\service\store\StoreBillService;
use app\common\service\store\StoreFundService;
use app\common\service\sxf\SxfLedgerService;
use app\common\service\sysSetting\MiniProgramConfigService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\sysSetting\WxPayConfService;
use app\common\service\user\MemberGradeBillService;
use app\common\service\user\UserShopBalanceService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\AdminLogType;
use app\common\utils\dataDictionary\BillType;
use app\common\utils\dataDictionary\CateringBillStatus;
use app\common\utils\dataDictionary\LogLevel;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\StoreBillStatus;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use think\Db;
use think\Exception;
use think\Request;

class SxfNotify
{


    /**
     * 支付码支付通知
     * @param Request $request
     * @return string[]
     */
    public function scanPayNotify(Request $request)
    {
        DebugUtil::log(json_encode($request->param()));
        return ['code' => 'success', 'msg' => "成功"];
    }

    /**
     * 退款通知
     * @param Request $request
     * @return string[]
     */
    public function refundNotify(Request $request)
    {
        DebugUtil::log(json_encode($request->param()));
        return ['code' => 'success', 'msg' => "成功"];
    }

    /**
     * 二维码支付通知
     * @param Request $request
     * @return string[]
     */
    public function activePlusScanNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr));
        $outTradeNo = $arr['ordNo'];
        if (CacheManager::has($outTradeNo)) {
            $sceneData = CacheManager::getCacheValue($outTradeNo);
            DebugUtil::log(json_encode($sceneData));
            $scene = $sceneData['scene'];
            if ($scene == 'catering') {
                CateringFundService::getInstance()->sxfQrPayNotify($sceneData, $arr);
            } elseif ($scene == 'lose') {
                LoseBillService::getInstance()->sxfQrPayNotify($sceneData, $arr);
            } elseif ($scene == 'independent_recharge') {
                RechargeFundService::getInstance()->sxfIndependentQrPayNotify($sceneData, $arr);
            } elseif ($scene == 'member_recharge') {
                RechargeFundService::getInstance()->sxfMemberQrPayNotify($sceneData, $arr);
            } elseif ($scene == 'book_room_pay') {
                RoomFundUtil::getInstance()->sxfRoomBookQrPayNotify($sceneData, $arr);
            } elseif ($scene == 'add_room_detail') {
                RoomFundUtil::getInstance()->sxfRoomAddDetail($sceneData, $arr);
            } elseif ($scene == 'store') {
                StoreFundService::getInstance()->sxfQrPayNotify($sceneData, $arr);
            }
        }
        return ['code' => 'success', 'msg' => "成功"];
    }

    /**
     * 微信小程序订房下单
     * @param Request $request
     * @throws WxPayException
     */
    public function bookRoomNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $shopId = $arr['extend'];
                $mno = $arr['mno'];
                $uuid = $arr['uuid'];
                $outTradeNo = $arr['ordNo'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        $billId = $payRecord->bill_id;
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        RoomBillDetailPreService::getInstance()->checkPreBills($billId, $outTradeNo, $uuid);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            Db::rollback();
            if ($exception instanceof MyException) {
                DebugUtil::log($exception->msg, 'error');
            } else {
                DebugUtil::log($exception->getMessage(), 'error');
            }
            DebugUtil::log($exception->getTraceAsString(), 'error');
//            $this->refundBill($shopId,$outTradeNo,$totalFee);
            return $this->returnData(2);
        }
    }

    public function breakfastNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $uuid = $arr['uuid'];
                $outTradeNo = $arr['ordNo'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                    BreakfastCouponRecordService::getInstance()->breakfastCouponNotify($outTradeNo, $uuid);
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            Db::rollback();
            if ($exception instanceof MyException) {
                DebugUtil::log($exception->msg, 'error');
            } else {
                DebugUtil::log($exception->getMessage(), 'error');
            }
            DebugUtil::log($exception->getTraceAsString(), 'error');
//            $this->refundBill($shopId,$outTradeNo,$totalFee);
            return $this->returnData(2);
        }
    }

    public function bookRoomNotifyFix(Request $request)
    {
        $arr = $request->param();
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $shopId = $arr['extend'];
                $uuid = $arr['uuid'];
                $outTradeNo = $arr['ordNo'];
                $this->outTradeNo = $outTradeNo;
                $mno = $arr['mno'];
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $totalFee = $arr['amt'];
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        $billId = $payRecord->bill_id;
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        RoomBillDetailPreService::getInstance()->checkPreBills($billId, $outTradeNo, $uuid);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            Db::rollback();
            if ($exception instanceof MyException) {
                DebugUtil::log($exception->msg, 'error');
            } else {
                DebugUtil::log($exception->getMessage(), 'error');
            }
            DebugUtil::log($exception->getTraceAsString(), 'error');
            return $this->returnData(2);
        }
    }

    /**
     * 团队入住选房回调
     * @param Request $request
     * @throws WxPayException
     */
    public function teamPayAmountNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $extend = $arr['extend'];
        $extendList = explode("_", $extend);
        $shopId = $extendList[0];
        $fundId = $extendList[1];
        $userCount = $extendList[2];
        $totalFee = $arr['amt'];
        $mno = $arr['mno'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    $teamId = $payRecord->team_id;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }

                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        TeamFundService::getInstance()->notifyUpdatePayStatus($fundId, 1, $userCount);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }

    /**
     * 微信小程序购买会员
     * @param Request $request
     * @throws WxPayException
     */
    public function buyMemberGrade(Request $request)
    {
        $arr = $request->param();
        $shopId = $arr['extend'];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        DebugUtil::log(json_encode($arr), 'debug');
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $mno = $arr['mno'];
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        MemberGradeBillService::getInstance()->buyMemberGradeCheckPreBills($billId, $outTradeNo, $uuid);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }


    /**
     * 申请退房
     * @param Request $request
     * @throws WxPayException
     */
    public function applyCheckOut(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $totalFee = $arr['amt'];
        $shopId = $arr['extend'];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        RoomBillDetailPreService::getInstance()->applyCheckOut($billId, $outTradeNo, $uuid);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }


    /**
     * 会员充值回调
     * @param Request $request
     * @throws WxPayException
     */
    public function memberRecharge(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $extend = $arr['extend'];
        $extendList = explode("_", $extend);
        $shopId = $extendList[0];
        $settingId = null;
        if (count($extendList) == 1) {
            $settingId = $extendList[1];
        }
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        if ($arr['bizCode'] == '0000') {
            $this->outTradeNo = $outTradeNo;
            $mno = $arr['mno'];
            if ($this->checkRepetition($outTradeNo) == 1) {
                Db::commit();
                return $this->returnData(1);
            } else {
                $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                $payStatus = $payRecord->pay_status;
                if ($payStatus == 0) {
                    $payAmount = $payRecord->amount;
                    if ($totalFee != $payAmount) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                    }
                    PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                    $billId = $payRecord->bill_id;
                    RechargeFundService::getInstance()->updateMemberRechargeBillStatus($billId, $settingId);
                }
                Db::commit();
                return $this->returnData(1);
            }
        } else {
            Db::rollback();
            return $this->returnData(2);
        }

    }


    /**
     * 超市支付回调
     * @param Request $request
     * @throws WxPayException
     */
    public function storeBillNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['extend'];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $this->outTradeNo = $outTradeNo;
                $mno = $arr['mno'];
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        StoreBillService::getInstance()->notifyUpdateBillStatus($billId, $uuid, StoreBillStatus::TO_BE_CONFIRMED);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }


    /**
     * 点餐支付回调
     * @param Request $request
     * @throws WxPayException
     */
    public function cateringBillNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['extend'];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        DebugUtil::log("step 3");
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        CateringFundService::getInstance()->notifyUpdateBillStatus($billId, $uuid, CateringBillStatus::TO_BE_CONFIRMED, $outTradeNo);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }

    public function pointMallBillNotify(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['extend'];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        PointMallBillFundDetailService::getInstance()->notifyUpdateBillStatus($outTradeNo, $uuid, 1);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            return $this->returnData(2);
        }
    }


    public function wxSelfPayCheckIn(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $extend = $arr['extend'];
        $shopAndRoomId = explode('_', $extend);
        $shopId = $shopAndRoomId[0];
        $roomId = $shopAndRoomId[1];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        $record = PayRecordService::getInstance()->findByOutTradeNo($outTradeNo);
                        $commonCode = $record->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfCheckInPayNotify($billId, $outTradeNo, $uuid, $roomId, $commonCode);

                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            return $this->returnData(2);
        }
    }

    public function wxSelfPayCheckOut(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $extend = $arr['extend'];
        $shopAndRoomId = explode('_', $extend);
        $shopId = $shopAndRoomId[0];
        $days = $shopAndRoomId[0];
        $type = $shopAndRoomId[0];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        $record = PayRecordService::getInstance()->findByOutTradeNo($outTradeNo);
                        $commonCode = $record->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfExtendROomPayNotify($billId, $outTradeNo, $uuid, $days, $type, $commonCode);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            return $this->returnData(2);
        }
    }


    /**
     * 自助续房
     * @param Request $request
     * @throws WxPayException
     */
    public function wxSelfPayExtendRoom(Request $request)
    {
        $arr = $request->param();
        DebugUtil::log(json_encode($arr), 'debug');
        $extend = $arr['extend'];
        $shopAndRoomId = explode('_', $extend);
        $shopId = $shopAndRoomId[0];
        $days = $shopAndRoomId[1];
        $type = $shopAndRoomId[2];
        $uuid = $arr['uuid'];
        $outTradeNo = $arr['ordNo'];
        $totalFee = $arr['amt'];
        Db::startTrans();
        try {
            if ($arr['bizCode'] == '0000') {
                $mno = $arr['mno'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    return $this->returnData(1);
                } else {
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $uuid, 1, $mno);
                        $commonCode = $payRecord->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfExtendROomPayNotify($billId, $outTradeNo, $uuid, $days, $type, $commonCode);
                    }
                    Db::commit();
                    return $this->returnData(1);
                }
            } else {
                Db::rollback();
                return $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            return $this->returnData(2);
        }
    }

    public function sxfLaunchLedgerNotify(Request $request)
    {
        DebugUtil::log(json_encode($request->param()));
        return ['code' => 'success', 'msg' => "成功"];
    }

    public function ledgerCommitApplyNotify(Request $request)
    {
        DebugUtil::log(json_encode($request->param()), 'debug');
        $param = $request->param();
        $applyId = $param['id'];
        $applyStatus = $param['applyStatus'];
        $record = SxfLedgerService::getInstance()->getSxfLedgerByApplyId($applyId);
        if ($record) {
            SxfLedgerService::getInstance()->editSxfLedger(['id' => $record, 'result' => $applyStatus]);
        }
        return ['code' => 'success', 'msg' => "成功"];
    }


    /**
     * 检查接收参数
     * @param $shopId
     * @param $xml
     * @return bool
     * @throws MyException
     */
    private function checkSign($shopId, $xml)
    {

    }

    /**
     * 检查是否重复处理
     * 个别订单出现回调已处理，并且业务成功但是还是在第二次回调时处理了导致收款重复
     * 可能是因为定时任务导致堵塞，两个请求同时处理导致的
     * @param $outTradeNo
     * @return boolean
     */
    private function checkRepetition($outTradeNo)
    {
        $type = 2;
        if (CacheManager::has($outTradeNo)) {
            $type = CacheManager::getCacheValue($outTradeNo);
        }
        return $type;
    }

    /**
     * @param $type 1:success 2:fail
     * @param string $msg 返回信息
     * @return string[]
     */
    private function returnData($type)
    {
        if ($type == 1) {
            return ['code' => 'success', 'msg' => "成功"];
        } else {
            return ['code' => 'fail', 'msg' => "失败"];
        }
    }


}