<?php

namespace app\common\service\Pay\sxf;

use app\common\returnConfig\Code;
use app\common\service\finance\SxfLaunchLedgerService;
use app\common\service\sxf\SxfConfigService;
use app\common\service\sxf\SxfLedgerApplyService;
use app\common\service\sxf\SxfLedgerService;
use app\common\sysConfig\MyException;
use app\common\utils\DebugUtil;
use think\File;

require_once EXTEND_PATH . 'wechatpay_sdk/example/phpqrcode/phpqrcode.php';

class SxfPayService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        return self::$object;
    }

    public function __clone()
    {
    }

    /**
     * 扫支付码支付
     * @param $shopId
     * @param $outTradeNo
     * @param $authCode
     * @param $amount
     * @param $remark
     * @return mixed|void
     * @throws MyException
     */
    public function reverseScan($shopId, $outTradeNo, $authCode, $amount, $remark)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $ledgerRule = SxfLaunchLedgerService::getInstance()->getLedgerRule($mno, $amount);
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->reverseScan($mno, $outTradeNo, $amount, $authCode, $remark, $ledgerRule);
        $tranSts = $res['tranSts'];
        if ($tranSts == 'CLOSED' || $tranSts == 'CANCELED' || $tranSts == 'FAIL') {
            $bizMsg = $res['bizMsg'];
            throw new MyException(Code::FAIL, $bizMsg);
        } elseif ($tranSts == 'PAYING') {
            $flag = $this->checkScanPay($mno, $orgId, $privateKey, $outTradeNo);
            $result = $flag['result'];
            if (!$result) {
                throw new MyException(Code::FAIL, '支付失败');
            }
            return $res;
        } elseif ($tranSts == 'SUCCESS') {
            return $res;
        }
    }

    public function getSxfMnoInfo($shopId)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxfRequestUtil = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxfRequestUtil->merchantInfoQuery($mno);
        return $res;
    }

    /**
     * 轮询查询支付结果
     * @param $mno
     * @param $orgId
     * @param $privateKey
     * @param $outTradeNo
     * @throws MyException
     */
    public function checkScanPay($mno, $orgId, $privateKey, $outTradeNo): array
    {
        $resFlag = false;
        $count = 10;
        $flag = true;
        while ($flag) {
            $res = $this->tradeQuery($mno, $orgId, $privateKey, $outTradeNo);
            $tranSts = $res['tranSts'];
            if ($tranSts == 'SUCCESS') {
                $flag = false;
                $resFlag = true;
            }
            sleep(2);
            $count++;
            if ($count > 10) {
                $flag = false;
            }
        }
        return ['result' => $resFlag, 'transaction_id' => $res['transactionId'], 'uuid' => $res['uuid']];
    }

    public function tradeQuery($mno, $orgId, $privateKey, $outTradeNo)
    {
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->tradeQuery($mno, $outTradeNo);
        return $res;
    }

    /**
     * 小程序支付
     * @param $shopId
     * @param $ordNo
     * @param $amt
     * @param $subject
     * @param $userId
     * @return mixed
     * @throws MyException
     */
    public function jsapiScan($shopId, $subAppid, $ordNo, $amt, $subject, $userId, $notifyUrl, $extend)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $amt = round($amt, 2);
        $ledgerRule = SxfLaunchLedgerService::getInstance()->getLedgerRule($mno, $amt);
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        return $sxf->jsapiScan($mno, $subAppid, $ordNo, $amt, $subject, $userId, $notifyUrl, $extend, $ledgerRule);
    }

    public function appletScanPre($shopId, $ordNo, $amt, $subject, $notifyUrl, $extend)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $amt = round($amt, 2);
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        return $sxf->appletScanPre($mno, $ordNo, $amt, $subject, $notifyUrl, $extend);
    }

    /**
     * 退款
     * @param $shopId
     * @param $ordNo
     * @param $origOrderNo
     * @param $amt
     * @param $refundReason
     * @return void
     * @throws MyException
     */
    public function refund($shopId, $ordNo, $origOrderNo, $amt, $refundReason)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $amt = round($amt, 2);
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->refund($mno, $ordNo, $origOrderNo, $amt, $refundReason);
        $bizCode = $res['bizCode'];
        if ($bizCode !== '0000') {
            throw new MyException(Code::FAIL, $res['bizMsg']);
        }
    }

    /**
     * 二维码支付
     * @param $shopId
     * @param $ordNo
     * @param $amt
     * @param $payType
     * @param $subject
     * @return string
     * @throws MyException
     */
    public function activePlusScan($shopId, $ordNo, $amt, $payType, $subject)
    {
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $amt = round($amt, 2);
        $ledgerRule = SxfLaunchLedgerService::getInstance()->getLedgerRule($mno, $amt);
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->activePlusScan($mno, $ordNo, $amt, $payType, $subject, $ledgerRule);
        $payUrl = $res['payUrl'];
        return \QRcode::base64($payUrl, QR_ECLEVEL_H, 3);
    }


    public function getSignUrl($customerId, $mno, $signType)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $apply = SxfLedgerApplyService::getInstance()->getSxfLedgerApply($mno);
        if (empty($apply)) {
            $orgId = $config['org_id'];
            $privateKey = $config['private_key'];
            $sxf = new SxfRequestUtil($orgId, $privateKey);
            $res = $sxf->getSignUrl($mno, $signType);
            if ($res['bizCode'] != '0000') {
                throw new MyException(Code::FAIL, $res['bizMsg']);
            }
            $data = ['mno' => $mno, 'sign_type' => $signType];
            if ($signType == "00") {
                $data['sign_url'] = $res['retUrl'];
            }
            $data['status'] = 0;
            SxfLedgerApplyService::getInstance()->editSxfLedgerApply($data);
            $apply = SxfLedgerApplyService::getInstance()->getSxfLedgerApply($mno);
        }
        return $apply;
    }

    public function querySignContract($customerId, $mno)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->querySignContract($mno);
        $apply = SxfLedgerApplyService::getInstance()->getSxfLedgerApply($mno);
        if (!empty($apply)) {
            $data = ['id' => $apply->id, 'result' => $res['signResult']];
        } else {
            $data = ['mno' => $mno, 'result' => $res['signResult']];
        }
        SxfLedgerApplyService::getInstance()->editSxfLedgerApply($data);
        return SxfLedgerApplyService::getInstance()->getSxfLedgerApply($mno);
    }

    /**
     * 设置分账
     * @param $customerId
     * @param $mno
     * @param $mnoArray
     * @return mixed
     * @throws MyException
     */
    public function setMnoArray($customerId, $mno, $mnoArray)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
//        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->setMnoArray($mno, $mnoArray);
        if ($res['bizCode'] != '0000') {
            throw new MyException(Code::FAIL, $res['bizMsg']);
        }
        $resultMnoArray = $res['mnoArray'];
        $mnoArr = explode(',', $resultMnoArray);
        foreach ($mnoArr as $m) {
            $apply = SxfLedgerService::getInstance()->getSxfLedger($mno, $m, null);
            if (empty($apply)) {
                SxfLedgerService::getInstance()->addSxfLedger($mno,$m,0);
            }
        }
        return $res;
    }


    /**
     * @param $customerId
     * @param $mno
     * @param $ordNo
     * @param $uuid
     * @param $ledgerAccountFlag
     * @param $ledgerRule array 分账规则
     * @param $notifyAddress
     * @param $thirdPartyUuid string 卡交易分账时需通过上游订单号进行分账及交易金额查询
     * @return mixed
     * @throws MyException
     */
    public function launchLedger($customerId, $mno, $ordNo, $uuid, $ledgerAccountFlag, $ledgerRule, $notifyAddress, $thirdPartyUuid)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
//        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->launchLedger($mno, $ordNo, $uuid, $ledgerAccountFlag, $ledgerRule, $notifyAddress, $thirdPartyUuid);
        return $res;
    }

    public function uploadFilePic($customerId, File $file)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->uploadPicture(0,$file);
        return $res;
    }



}