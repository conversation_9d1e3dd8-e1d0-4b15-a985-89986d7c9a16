<?php


namespace app\common\service\room;

use app\common\model\room\RoomStatusRecord as RoomStatusRecordModel;
use app\common\returnConfig\Code;
use app\common\service\intermediary\IntermediaryRoomPriceService;
use app\common\service\intermediary\IntermediaryService;
use app\common\service\Pay\PayRecordService;
use app\common\service\sysSetting\BillSourceService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\thridSystem\ThirdSystemService;
use app\common\service\thridSystem\wlys\RoomBill;
use app\common\service\user\MemberAndRightInterestService;
use app\common\service\user\MemberGradeService;
use app\common\service\user\MemberUtilService;
use app\common\service\user\UserCommonService;
use app\common\sysConfig\MyException;
use app\common\utils\ArrayUtils;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomClearStatus;
use app\common\utils\dataDictionary\RoomRecordStatus;
use app\common\utils\dataDictionary\RoomSaleType;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\ThirdSystem;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;
use think\Exception;

/**
 * 房间查询参数对象
 */
class RoomQueryParams
{
    public $shopIdZong;
    public $shopId;
    public $roomTypeIds;
    public $roomSaleTypeId;
    public $memberGrade;
    public $intermediaries_id;
    public $recordStatus;
    public $roomNumber;
    public $floorId;
    public $buildingId;
    public $roomStatus;
    public $startTime;
    public $endTime;
    public $tag_type;
    public $userName;
    public $billSource;
    public $sellTypeId;

    public function __construct($shopIdZong, $shopId, $roomTypeIds, $roomSaleTypeId, $memberGrade, $intermediaries_id,
                                $recordStatus, $roomNumber, $floorId, $buildingId, $roomStatus, $startTime,
                                $endTime, $tag_type, $userName, $billSource, $sellTypeId)
    {
        $this->shopIdZong = $shopIdZong;
        $this->shopId = $shopId;
        $this->roomTypeIds = $roomTypeIds;
        $this->roomSaleTypeId = $roomSaleTypeId;
        $this->memberGrade = $memberGrade;
        $this->intermediaries_id = $intermediaries_id;
        $this->recordStatus = $recordStatus;
        $this->roomNumber = $roomNumber;
        $this->floorId = $floorId;
        $this->buildingId = $buildingId;
        $this->roomStatus = $roomStatus;
        $this->startTime = $startTime;
        $this->endTime = $endTime;
        $this->tag_type = $tag_type;
        $this->userName = $userName;
        $this->billSource = $billSource;
        $this->sellTypeId = $sellTypeId;
    }
}

class RoomStatusRecordService
{

    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }


    /*
     * 锁房、关房、维修
     */
    private $listWithOutBill = [RoomRecordStatus::CLOSED, RoomRecordStatus::LOCKED];

    /*
     * 在住、预定、预退
     */
    private $listWithBill = [RoomRecordStatus::STAYING, RoomRecordStatus::BOOK, RoomRecordStatus::TO_CHECK_OUT];

    public function getRoomStatusRecordList($shopId, $page, $limit)
    {
        $model = new RoomStatusRecordModel();
        $list = $model->getRoomStatusRecordList($shopId, $page, $limit);
        if ($page && $limit) {
            $count = $model->getRoomStatusRecordCount($shopId);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function editRoomStatusRecord($params)
    {
        $model = new RoomStatusRecordModel();
        if (isset($params['id'])) {
            $model->editRoomStatusRecord($params);
            $id = $params['id'];
        } else {
            $model->saveRoomStatusRecord($params);
            $id = $model->id;
        }
        return $id;
    }

    /**
     * 新增房态记录
     * @param $shopId
     * @param $billId
     * @param $roomId
     * @param $startTimePlan
     * @param $endTimePlan
     * @param $statusId
     * @param string $remark
     * @return mixed
     */
    public function saveRoomRecord($shopId, $billId, $roomId, $startTimePlan, $endTimePlan, $statusId, $remark = '')
    {
        $data['shop_id'] = $shopId;
        $data['bill_id'] = $billId;
        $data['room_id'] = $roomId;
        $data['start_time_plan'] = $startTimePlan;
        $data['end_time_plan'] = $endTimePlan;
        $data['status_id'] = $statusId;
        $data['remark'] = $remark;
        return $this->editRoomStatusRecord($data);
    }

    public function getRecordById($id)
    {
        $model = new RoomStatusRecordModel();
        return $model->getRecordById($id);
    }

    public function getRecordByBillId($billId)
    {
        $model = new RoomStatusRecordModel();
        return $model->getRecordByBillId($billId);
    }

    public function getRoomRecordStatusListByRoomIdInfo($shopId, $roomId, $startTime, $endTime, $roomStatus, $actual, $roomStatusList)
    {
        $model = new RoomStatusRecordModel();
        $list = $model->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, $roomStatus, $actual);
        foreach ($list as $item) {
            $itemStatusId = $item->status_id;
            foreach ($roomStatusList as $itemRoomStatus) {
                if ($itemRoomStatus->id == $itemStatusId) {
                    $item->room_status_name = $itemRoomStatus->status_name;
                    $item->color = $itemRoomStatus->color;
                    break;
                }
            }
        }
        return $list;
    }

    public function getRoomRecordStatusRoomIds($shopId, $startTime, $endTime, $roomStatus, $actual = false)
    {
        $model = new RoomStatusRecordModel();
        $list = $model->getRoomRecordStatusRoomIds($shopId, $startTime, $endTime, $roomStatus, $actual);
        $roomIds = [];
        foreach ($list as $item) {
            $roomIds[] = $item->room_id;
        }
        return $roomIds;
    }

    public function getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, $roomStatus, $simple = false, $actual = false)
    {
        $model = new RoomStatusRecordModel();
        $list = $model->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, $roomStatus, $actual);
        if ($simple) {
            return $list;
        }
        foreach ($list as $item) {
            $roomStatusModel = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, $item->status_id);
            $item->room_status_name = $roomStatusModel->status_name;
            $item->color = $roomStatusModel->color;
            $tempRoomId = $item->room_id;
            if ($tempRoomId) {
                $tempRoom = RoomService::getInstance()->getRoomById($item->room_id);
                $item->room_number = $tempRoom->room_number;
                $simpleRoomTypeName = $tempRoom->simple_room_type_name;
                if ($simpleRoomTypeName) {
                    $item->room_type_name = $simpleRoomTypeName;
                } else {
                    $item->room_type_name = $tempRoom->room_type_name;
                }
            } else {
                $item->room_number = '';
                $item->room_type_name = '';
            }

        }
        return $list;
    }

    public function getRoomRecordStatusCountByRoomIdAndSaleType($shopId, $roomId, $startTime, $endTime, $roomStatus, $saleTypeId)
    {
        $model = new RoomStatusRecordModel();
        return $model->getRoomRecordStatusCountByRoomIdAndSaleType($shopId, $roomId, $startTime, $endTime, $roomStatus, $saleTypeId);
    }


    public function getRoomRecordStatusCountByRoomIds($shopId, $roomIds, $startTime, $endTime, $roomStatus)
    {
        $model = new RoomStatusRecordModel();
        return $model->getRoomRecordStatusCountByRoomIds($shopId, $roomIds, $startTime, $endTime, $roomStatus);

    }


    public function getRoomBillStatusRecord($shopId, $billId, $roomId, $oldStatus)
    {
        $model = new RoomStatusRecordModel();
        return $model->getRoomBillStatusRecord($shopId, $billId, $roomId, $oldStatus);
    }


    /**
     * 修改房态开始结束时间
     * @param $id
     * @param $startTimePlan
     * @param $endTimePlan
     */
    public function updateStartTimePlanAndEndTimePlan($id, $startTimePlan, $endTimePlan)
    {
        $data['id'] = $id;
        $data['start_time_plan'] = $startTimePlan;
        $data['end_time_plan'] = $endTimePlan;
        $this->editRoomStatusRecord($data);
    }


    /**
     * 修改预离时间
     * @param $billId
     * @param $roomId
     * @param $leaveTimePlan
     */
    public function updateLeaveTimePlan($billId, $roomId, $leaveTimePlan)
    {
//		$bill = RoomBillService::getInstance()->getBillById($billId);
//		$tempLeaveTimePlan = $bill->leave_time_plan;
        $record = $this->getCurrentRoomStatusRecord($billId, $roomId);
        if ($record) {
            $recordId = $record->id;
            $this->editRoomStatusRecord(['id' => $recordId, 'end_time_plan' => $leaveTimePlan]);
        }
    }

    public function updateOverTimeBillLeaveTimePlan($billId, $roomId, $leaveTimePlan)
    {
        $record = $this->getCurrentRoomStatusRecord($billId, $roomId);
        if ($record) {
            $recordId = $record->id;
            $this->editRoomStatusRecord(['id' => $recordId, 'leave_time_plan' => $leaveTimePlan]);
        }
    }


    /**
     * 修改旧记录开始结束时间
     * @param $shopId
     * @param $roomId
     * @param $startTime
     * @param $endTime
     * @param $oldStartTime
     * @param $oldEndTime
     * @param $oldRecordId
     * @param $oldStatusId
     * @param $newStatusId
     */
    public function updateOldRecordStartTimeAndEndTime($shopId, $roomId, $startTime, $endTime, $oldStartTime, $oldEndTime, $oldRecordId, $oldStatusId, $newStatusId)
    {
        if ($newStatusId == $oldStatusId && in_array($newStatusId, [RoomRecordStatus::CLOSED, RoomRecordStatus::LOCKED])) {
            $this->delRoomStatus($oldRecordId);
            return;
        }
        //记录的结束时间小于修改的结束数据
        if ($startTime >= $oldStartTime && $startTime <= $oldEndTime && $endTime >= $oldEndTime) {
            //-------------------------|---------------------|------------------------------
            //old------------------|===================|------------------------------------
            $this->updateStartTimePlanAndEndTimePlan($oldRecordId, $oldStartTime, $startTime);
        } elseif ($startTime >= $oldStartTime && $endTime <= $oldEndTime) {
            //------------------------|---------------------|------------------------------
            //old------------------|===============================|------------------------
            $this->updateStartTimePlanAndEndTimePlan($oldRecordId, $oldStartTime, $startTime);
            $this->saveRoomRecord($shopId, 0, $roomId, $endTime, $oldEndTime, $oldStatusId);
        } elseif ($startTime <= $oldStartTime && $endTime >= $oldStartTime && $endTime <= $oldEndTime) {
            //----------------------|---------------------|------------------------------
            //old------------------------------|===================|------------------------
            $this->updateStartTimePlanAndEndTimePlan($oldRecordId, $endTime, $oldEndTime);
        } elseif ($startTime >= $oldStartTime && $startTime <= $oldEndTime && $endTime <= $oldEndTime) {
            //----------------------|---------------------|------------------------------
            //old--------------------------|=============|----------------------------------
            $this->delRoomStatus($oldRecordId);
        }
    }


    /**
     * 检查房间是否可以被预定
     * @param $shopId
     * @param $billId
     * @param $roomTypeId
     * @param $roomId
     * @param $startTime
     * @param $endTime
     * @param $admin 管理端办理
     * @throws MyException
     */
    public function checkRoom($shopId, $billId, $roomTypeId, $roomId, $startTime, $endTime, $admin = false)
    {
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        if ($thirdSystem->sign != ThirdSystem::JINGFEI) {
            return;
        }
        if ($startTime < time()) {
            $startTime = time();
        }
        $roomCount = $this->getBookUsableRoomCount($shopId, $roomTypeId, $startTime, $endTime, false);
        if ($billId) {
            $roomCount++;
        }
        if ($roomCount < 1) {
            throw new MyException(Code::FAIL, "房间不足");
        }
//        if ($roomId == 0) {
//        }
        if (!$roomId) {
            return;
        }
        $room = RoomService::getInstance()->getRoomById($roomId);
        $clean_status = $room->clean_status;
        if ($clean_status == RoomClearStatus::REPAIR) {
            throw new MyException(Code::FAIL, "房间维修中,不能办理");
        }
        $recordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, []);
        foreach ($recordList as $itemRecord) {
            $roomNumber = $itemRecord->room_number;
            $itemRecordBillId = $itemRecord->bill_id;
            $tempRoomId = $itemRecord->room_id;
            $tempBillStatus = $itemRecord->bill_status;
            if ($itemRecordBillId != 0) {
                if ($itemRecordBillId != $billId) {
                    if ($tempBillStatus == RoomBillStatus::BOOK_NOT_ARRIVE || $tempBillStatus == RoomBillStatus::FINISHED) {
                        return;
                    }
                    throw new MyException(Code::FAIL, "$roomNumber 房间已经被占用");
                }
            } else {
                $itemStatusId = $itemRecord->status_id;
                //管理端可以使用关房状态下的房间
                if (!$admin && $itemStatusId != RoomBillStatus::CANCELED) {
                    $statusName = RoomStatusService::getInstance()->getRoomStatusName($shopId, $itemStatusId);
                    throw new MyException(Code::FAIL, "$roomNumber $statusName 中");
                }
            }
        }
    }

    /**
     * 预定检测可用房数量
     * @param $shopId
     * @param $roomTypeId
     * @param $roomId
     * @param $startTime
     * @param $endTime
     * @param $admin
     * @return void
     * @throws MyException
     */
    public function bookCheckRoom($shopId, $roomTypeId, $roomId, $startTime, $endTime, $admin = false)
    {
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        if ($thirdSystem->sign != ThirdSystem::JINGFEI) {
            return;
        }
        $startTime = max($startTime, time());
        if (!$roomId) {
            $dates = TimeUtil::getInstance()->getAllTimes(date('Y-m-d H:i:s', $startTime), date('Y-m-d H:i:s', $endTime), false);
            $minCount = 0;
            foreach ($dates as $date) {
                $tempStartDate = $date['start_time'] . " 16:00:01";
                if ($date['start_time'] == date('Y-m-d') && strtotime($tempStartDate) < time()) {
                    $tempStartDate = date('Y-m-d H:i:s');
                }
                $tempStartTime = strtotime($tempStartDate);
                $tempEndDate = $date['end_time'] . " 11:59:00";
                $tempEndTime = strtotime($tempEndDate);
                $roomCount = $this->getBookUsableRoomCount($shopId, $roomTypeId, $tempStartTime, $tempEndTime, true);
                DebugUtil::log("roomCount:$roomCount");
                if ($minCount == 0) {
                    $minCount = $roomCount;
                } else if ($minCount > $roomCount) {
                    $minCount = $roomCount;
                }
                if ($minCount < 1) {
                    throw new MyException(Code::FAIL, "房间不足");
                }
            }
            return;
        }
        $room = RoomService::getInstance()->getRoomById($roomId);
        $clean_status = $room->clean_status;
        if ($clean_status == RoomClearStatus::REPAIR) {
            throw new MyException(Code::FAIL, "房间维修中,不能办理");
        }
        $recordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, []);
        DebugUtil::log("$startTime, $endTime");
        $saleTypeIds = $this->getSaleTypeId($shopId);
        foreach ($recordList as $itemRecord) {
            $roomNumber = $itemRecord->room_number;
            $itemRecordBillId = $itemRecord->bill_id;
            $tempBillStatus = $itemRecord->bill_status;
            $itemSaleTypeId = $itemRecord->sale_type_id;
            if ($itemRecordBillId != 0) {
                if ($tempBillStatus == RoomBillStatus::BOOK_NOT_ARRIVE || $tempBillStatus == RoomBillStatus::FINISHED) {
                    continue;
                } else {
                    if (in_array($itemSaleTypeId, $saleTypeIds)) {
                        throw new MyException(Code::FAIL, "$roomNumber 房间已经被占用");
                    }
                }
            } else {
                $itemStatusId = $itemRecord->status_id;
                //管理端可以使用关房状态下的房间
                if (!$admin && $itemStatusId != RoomBillStatus::CANCELED) {
                    $statusName = RoomStatusService::getInstance()->getRoomStatusName($shopId, $itemStatusId);
                    throw new MyException(Code::FAIL, "$roomNumber $statusName 中");
                }
            }
        }
    }


    /**
     * 添加订房房态记录
     * @param $shopId
     * @param $billId
     * @param $roomId
     * @param $startTime
     * @param $endTime
     * @param $newStatusId
     * @param string $remark
     * @throws MyException
     */
    public function addBillRoomStatusRecord($shopId, $billId, $roomId, $startTime, $endTime, $newStatusId, $remark = '')
    {
        $recordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, [], true, true);
        if (empty($recordList)) {
            $this->saveRoomRecord($shopId, $billId, $roomId, $startTime, $endTime, $newStatusId, $remark);
        } else {
            foreach ($recordList as $item) {
                $oldRecordId = $item->id;
                $recordStatusId = $item->status_id;
                $recordStartTimePlan = $item->start_time_plan;
                $recordEndTimePlan = $item->end_time_plan;
                $recordBillId = $item->bill_id;
                if (in_array($recordStatusId, $this->listWithOutBill)) {
                    //关房，锁房
                    $this->updateOldRecordStartTimeAndEndTime($shopId, $roomId, $startTime, $endTime, $recordStartTimePlan,
                        $recordEndTimePlan, $oldRecordId, $recordStatusId, $newStatusId);
                    if (in_array($newStatusId, $this->listWithOutBill)) {
                        $newId = $this->saveRoomRecord($shopId, 0, $roomId, $startTime, $endTime, $newStatusId, $remark);
                    }
//                    $this->saveRoomRecord($shopId, $billId, $roomId, $startTime, $endTime, $newStatusId, $remark);
                    break;
                } else {
                    if ($billId == $recordBillId) {
                        //在住、预定、预退
                        $this->onlyUpdateRoomStatus($oldRecordId, $newStatusId, $remark);
                    } else {
                        $this->saveRoomRecord($shopId, $billId, $roomId, $startTime, $endTime, $newStatusId, $remark);
                        break;
                    }
                }
            }
        }
    }


    /**
     * 修改订单房态
     * @param $shopId
     * @param $billId
     * @param $roomId
     * @param $statusId
     * @param $oldStatus
     */
    public function updateRoomStatusRecordStatus($shopId, $billId, $roomId, $statusId, $oldStatus)
    {
        $record = $this->getRoomBillStatusRecord($shopId, $billId, $roomId, $oldStatus);
        $id = $record->id;
        $this->editRoomStatusRecord(['id' => $id, 'status_id' => $statusId]);
    }

    /**
     * 排房
     * @param $shopId
     * @param $billId
     * @param $oldRoomId
     * @param $newRoomId
     * @param $startTime
     * @param $endTime
     * @throws MyException
     */
    public function arrangedRoom($shopId, $billId, $oldRoomId, $newRoomId, $startTime, $endTime)
    {
        if ($oldRoomId != 0) {
            $record = $this->getRoomBillStatusRecord($shopId, $billId, $oldRoomId, RoomRecordStatus::BOOK);
            $this->delRoomStatus($record->id);
        }
        $this->addBillRoomStatusRecord($shopId, $billId, $newRoomId, $startTime, $endTime, RoomRecordStatus::BOOK);
    }

    /**
     * 退房修改房态
     * @param $billId
     * @param $roomId
     */
    public function roomCheckOut($billId, $roomId)
    {
        //在不确定的情况下换房同时触发两次，造成有两条换房产生的房态记录。
        $list = $this->getRecordByBillId($billId);
        foreach ($list as $record) {
//            $record = $this->getCurrentRoomStatusRecord($billId, $roomId);
            $shopId = 0;
            $end_time_actual = $record->end_time_actual;
            if (!$end_time_actual) {
                $id = $record->id;
                $this->editRoomStatusRecord(['id' => $id, 'end_time_plan' => time(), 'end_time_actual' => time()]);
                $shopId = $record->shop_id;
            }
            $room = RoomService::getInstance()->getRoomById($roomId);
            if ($room) {
                $shopId = $room->shop_id;
                RoomService::getInstance()->updateRoomToDirty($roomId);
            }
            if ($shopId && $roomId) {
                RoomCleanRecordService::getInstance()->setRoomCleanRecord($shopId, $roomId, 2, $billId);
            }
        }
    }

    /**
     * 预定转入住
     * @param $shopId
     * @param $billId
     * @param $newRoomId
     * @param $oldRoomId
     * @param $startTime
     * @param $endTime
     * @param $statusId
     * @throws MyException
     */
    public function bookToStay($shopId, $billId, $newRoomId, $oldRoomId, $startTime, $endTime, $statusId)
    {
        $record = $this->getCurrentRoomStatusRecord($billId, $oldRoomId);
        if (!empty($record)) {
            $recordRoomId = $record->room_id;
            $recordStartTime = $record->start_time_plan;
            $recordEndTime = $record->end_time_plan;
            $recordStatusId = $record->status_id;
            if ($recordRoomId != $newRoomId ||
                $recordStartTime != $startTime ||
                $recordEndTime != $endTime) {
                if ($recordStatusId != $statusId) {
                    $this->editRoomStatusRecord(['id' => $record->id, 'status_id' => $statusId]);
                } else {
                    $this->delRoomStatus($record->id);
                }
            }
        } else {
            $this->addBillRoomStatusRecord($shopId, $billId, $newRoomId, $startTime, $endTime, $statusId, '');
        }
    }

    /**
     * 换房
     * @param $billId
     * @param $oldRoomId
     * @param $newRoomId
     * @throws MyException
     */
    public function changeRoom($billId, $oldRoomId, $newRoomId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billStatus = $bill->bill_status;
        $shopId = $bill->shop_id;
        $tempRoomTypeId = $bill->room_type_id;
        $endTime = $bill->leave_time_plan;
        $record = $this->getCurrentRoomStatusRecord($billId, $oldRoomId);
        $unFinish = [
//			RoomBillStatus::TO_BE_CONFIRMED,
//			RoomBillStatus::TO_BE_STAY,
            RoomBillStatus::STAYING,
            RoomBillStatus::TO_CHECK_OUT,
        ];
        if (in_array($billStatus, $unFinish)) {
            $newStatusId = RoomRecordStatus::STAYING;
            $startTime = time();
        } else {
            $newStatusId = RoomRecordStatus::BOOK;
            $startTime = $bill->enter_time_plan;
        }
        if (empty($record)) {
            $this->addBillRoomStatusRecord($shopId, $billId, $newRoomId, $startTime, $endTime, $newStatusId, "换房");
        } else {
            $id = $record->id;
            $statusId = $record->status_id;
            if ($newRoomId != 0) {
                $this->checkRoom($shopId, $billId, $tempRoomTypeId, $newRoomId, $startTime, $endTime, true);
                $updateData = ['id' => $id, 'room_id' => $newRoomId];
                if ($billStatus != RoomBillStatus::TO_BE_STAY && $billStatus != RoomBillStatus::TO_BE_CONFIRMED) {
                    $updateData['end_time_actual'] = time();
                    $updateData['end_time_plan'] = time();
                    $this->editRoomStatusRecord($updateData);
                    $this->addBillRoomStatusRecord($shopId, $billId, $newRoomId, $startTime, $endTime, $statusId, "换房");
                } else {
                    $this->editRoomStatusRecord($updateData);
                }
            } else {
                $this->delRoomStatus($id);
            }
        }
    }


    public function delRoomStatus($id)
    {
        $model = new RoomStatusRecordModel();
        return $model->delRoomStatus($id);
    }


    /**
     * 查询房间当前房态
     * @param $billId
     * @param $roomId
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getCurrentRoomStatusRecord($billId, $roomId)
    {
        $model = new RoomStatusRecordModel();
        return $model->getCurrentRoomStatusRecord($billId, $roomId);
    }

    public function getOutTimeRoomStatusRecord($billId, $roomId)
    {
        $model = new RoomStatusRecordModel();
        return $model->getOutTimeRoomStatusRecord($billId, $roomId);
    }

    /**
     * 查询房态详细信息
     * @param $billId
     * @param $roomId
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getCurrentRoomStatusRecordFullInfo($billId, $roomId)
    {
        $currentRecord = $this->getCurrentRoomStatusRecord($billId, $roomId);
        if ($currentRecord) {
            $shopId = $currentRecord->shop_id;
            $roomStatus = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, $currentRecord->status_id);
            $currentRecord->room_status_name = $roomStatus->status_name;
            $currentRecord->color = $roomStatus->color;
            $tempRoomId = $currentRecord->room_id;
            if ($tempRoomId) {
                $tempRoom = RoomService::getInstance()->getRoomById($currentRecord->room_id);
                $currentRecord->room_number = $tempRoom->room_number;
                $simpleRoomTypeName = $tempRoom->simple_room_type_name;
                if ($simpleRoomTypeName) {
                    $currentRecord->room_type_name = $simpleRoomTypeName;
                } else {
                    $currentRecord->room_type_name = $tempRoom->room_type_name;
                }
            } else {
                $currentRecord->room_number = '';
                $currentRecord->room_type_name = '';
            }
        } else {
            return $currentRecord;
        }
    }


    /**
     * 查询房间数量
     * @param $billId
     * @param $shopId
     * @param $roomTypeId
     * @param $startTime
     * @param $endTime
     * @param $wxxcx
     * @return int|string
     * @throws MyException
     */
    public function getBookUsableRoomCount($shopId, $roomTypeId, $startTime, $endTime, $bookRoom, $wxxcx = false)
    {
        //预定是否占用房间数
        $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeId, 1, '', '', '', [RoomClearStatus::CLEAR, RoomClearStatus::DIRTY, RoomClearStatus::CLEARING]);
        $totalCount = count($roomList);
        $roomIds = [];
        foreach ($roomList as $itemRoom) {
            $roomIds[] = $itemRoom->id;
        }
        $saleTypeIds = [];
        if ($bookRoom) {
            $saleTypeIds = $this->getSaleTypeId($shopId);
        }
//        DebugUtil::log($totalCount);
//        DebugUtil::log(date("Y-m-d H:i:s",$startTime)."--".date("Y-m-d H:i:s",$endTime));
        $maxNotUseRoomCount = 0;
        $tempRoomIds = [];
        $noRoomCount = 0;
        $bookingList = RoomBillService::getInstance()->getDateStayingBill($shopId, [RoomBillStatus::TO_BE_STAY,
            RoomBillStatus::TO_BE_CANCELED], $startTime, $endTime, $roomTypeId, $saleTypeIds);
//        DebugUtil::log("bookingLis:" . count($bookingList));
        foreach ($bookingList as $booking) {
            $bookingRoomId = $booking->room_id;
            if ($bookingRoomId) {
                if (!in_array($bookingRoomId, $tempRoomIds)) {
                    $tempRoomIds[] = $bookingRoomId;
                }
            } else {
                $noRoomCount++;
            }
        }
        $stayingList = RoomBillService::getInstance()->getDateStayingBill($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT],
            $startTime, $endTime, $roomTypeId, $saleTypeIds);
//        DebugUtil::log("stayingList:" . count($stayingList));
        foreach ($stayingList as $staying) {
            $stayingRoomId = $staying->room_id;
            if ($stayingRoomId && !in_array($stayingRoomId, $tempRoomIds)) {
                $tempRoomIds[] = $stayingRoomId;
            }
            if ($stayingRoomId) {
                if (!in_array($stayingRoomId, $tempRoomIds)) {
                    $tempRoomIds[] = $stayingRoomId;
                }
            } else {
                $noRoomCount++;
            }
        }
        $tempLockedRoomList = $this->getRoomRecordStatusListByRoomId($shopId, $roomIds, $startTime, $endTime, [RoomRecordStatus::LOCKED], true);
//        DebugUtil::log("tempLockedRoomList:" . count($tempLockedRoomList));
        foreach ($tempLockedRoomList as $itemLocked) {
            if (!in_array($itemLocked->room_id, $tempRoomIds)) {
                $tempRoomIds[] = $itemLocked->room_id;
            }
        }
        if ($wxxcx) {
            $tempClosedRoomList = $this->getRoomRecordStatusListByRoomId($shopId, $roomIds, $startTime, $endTime, [RoomRecordStatus::CLOSED], true);
//            DebugUtil::log("tempClosedRoomList:" . count($tempClosedRoomList));
            foreach ($tempClosedRoomList as $itemClosed) {
                if (!in_array($itemClosed->room_id, $tempRoomIds)) {
                    $tempRoomIds[] = $itemClosed->room_id;
                }
            }
        }
        $notUsableRoomCount = count($tempRoomIds) + $noRoomCount;
//        DebugUtil::log("notUsableRoomCount:$notUsableRoomCount");
        if ($totalCount <= $notUsableRoomCount) {
            return 0;
        }
        if ($maxNotUseRoomCount < $notUsableRoomCount) {
            $maxNotUseRoomCount = $notUsableRoomCount;
        }
//        DebugUtil::log("maxNotUseRoomCount:$maxNotUseRoomCount");
        return max($totalCount - $maxNotUseRoomCount, 0);
    }

    public function getSaleTypeId($shopId)
    {
        $hourRoomSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::HOUR_ROOM_COUNT);
        $hourRoomSettingStatus = $hourRoomSetting->property['status'];
        $saleTypeIds = [];
        $saleTypeList = RoomSaleTypeService::getInstance()->getRoomSaleTypeList($shopId, 1, null, 1, null, null);
        foreach ($saleTypeList as $itemSaleType) {
            $itemSaleTypeSign = $itemSaleType->sign;
            if ($hourRoomSettingStatus) {
                $saleTypeIds[] = $itemSaleType->id;
            } else {
                if ($itemSaleTypeSign != RoomSaleType::HOUR) {
                    $saleTypeIds[] = $itemSaleType->id;
                }
            }
        }
        return $saleTypeIds;
    }

    public function getUsableRoomCount($shopId, $roomTypeId, $startTime, $endTime, $wxxcx)
    {
        //预定是否占用房间数
        $setting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::BOOK_ARRANGED_ROOM);
        $bookingCountStatus = $setting->property['status'];
        $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeId, 1, '', '', '', [RoomClearStatus::CLEAR, RoomClearStatus::DIRTY, RoomClearStatus::CLEARING]);
        $totalCount = count($roomList);
        $roomIds = [];
        foreach ($roomList as $itemRoom) {
            $roomIds[] = $itemRoom->id;
        }
        $saleTypeIds = $this->getSaleTypeId($shopId);
//        DebugUtil::log($saleTypeIds);
        $maxNotUseRoomCount = 0;
        $noRoomCount = 0;
        $tempRoomIds = [];
        if ($bookingCountStatus) {
            $bookingList = RoomBillService::getInstance()->getDateStayingBill($shopId, [RoomBillStatus::TO_BE_STAY,
                RoomBillStatus::TO_BE_CANCELED], $startTime, $endTime, $roomTypeId, $saleTypeIds);
//                DebugUtil::log("bookingList:".count($bookingList));
            foreach ($bookingList as $booking) {
                $bookingRoomId = $booking->room_id;
                if ($bookingRoomId) {
                    if (!in_array($bookingRoomId, $tempRoomIds)) {
                        $tempRoomIds[] = $bookingRoomId;
                    }
                } else {
                    $noRoomCount++;
                }
            }
        }
        $stayingList = RoomBillService::getInstance()->getDateStayingBill($shopId, [RoomBillStatus::STAYING,
            RoomBillStatus::TO_CHECK_OUT], $startTime, $endTime, $roomTypeId, $saleTypeIds);
//            DebugUtil::log("stayingList:".count($stayingList));
        foreach ($stayingList as $staying) {
            $stayingRoomId = $staying->room_id;
            if ($stayingRoomId && !in_array($stayingRoomId, $tempRoomIds)) {
                $tempRoomIds[] = $stayingRoomId;
            }
            if ($stayingRoomId) {
                if (!in_array($stayingRoomId, $tempRoomIds)) {
                    $tempRoomIds[] = $stayingRoomId;
                }
            } else {
                $noRoomCount++;
            }
        }
        $tempLockedRoomList = $this->getRoomRecordStatusListByRoomId($shopId, $roomIds, $startTime, $endTime, [RoomRecordStatus::LOCKED], true);
//            DebugUtil::log("tempLockedRoomList:".count($tempLockedRoomList));
        foreach ($tempLockedRoomList as $itemLocked) {
            if (!in_array($itemLocked->room_id, $tempRoomIds)) {
                $tempRoomIds[] = $itemLocked->room_id;
            }
        }
        if ($wxxcx) {
            $tempClosedRoomList = $this->getRoomRecordStatusListByRoomId($shopId, $roomIds, $startTime, $endTime, [RoomRecordStatus::CLOSED], true);
//                DebugUtil::log("tempClosedRoomList:".count($tempClosedRoomList));
            foreach ($tempClosedRoomList as $itemClosed) {
                if (!in_array($itemClosed->room_id, $tempRoomIds)) {
                    $tempRoomIds[] = $itemClosed->room_id;
                }
            }
        }
        $notUsableRoomCount = count($tempRoomIds) + $noRoomCount;
//            DebugUtil::log("notUsableRoomCount:$notUsableRoomCount");
        if ($totalCount <= $notUsableRoomCount) {
            return 0;
        }
        if ($maxNotUseRoomCount < $notUsableRoomCount) {
            $maxNotUseRoomCount = $notUsableRoomCount;
        }
        return max($totalCount - $maxNotUseRoomCount, 0);
    }

    /**
     * 钟点房占用房量
     * @param $shopId
     * @param $roomTypeId
     * @param $startTime
     * @param $endTime
     * @return mixed
     */
    public function getHourRoomCount($shopId, $roomTypeId, $startTime, $endTime)
    {
        $saleTypeIds = RoomSaleTypeService::getInstance()->getRoomSaleTypeIdsBySign($shopId, RoomSaleType::HOUR);
        $bookCount = RoomBillService::getInstance()->getBillCount($shopId, $roomTypeId, [RoomBillStatus::TO_BE_STAY,
            RoomBillStatus::TO_BE_CANCELED], $startTime, $endTime, $saleTypeIds);
        $stayingCount = RoomBillService::getInstance()->getBillCount($shopId, $roomTypeId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT],
            $startTime, $endTime, $saleTypeIds);
        return max($bookCount + $stayingCount, 0);
    }


    /**
     * 更新房间业务状态
     * @param $recordId
     * @param $newStatusId
     * @param $remark
     */
    public function onlyUpdateRoomStatus($recordId, $newStatusId, $remark)
    {
        $this->editRoomStatusRecord(['id' => $recordId, 'status_id' => $newStatusId, 'remark' => $remark]);
    }


    private function getUsableRoomId($shopId, $tag_type, $userName, $billSource, $sellTypeId)
    {
        $todayStartTime = strtotime(date("Y-m-d 00:00:00"));
        $todayEndTime = strtotime(date("Y-m-d 23:59:59"));
        $roomIds = [];
        if ($tag_type == 1) {
            $todayBookingBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED], null, null, null, null, null,
                null, null, ['enter_time_plan' => ['start_time' => $todayStartTime, 'end_time' => $todayEndTime], 'arranged' => 1], null, null);
            foreach ($todayBookingBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }

        } elseif ($tag_type == 3) {
            $ndayBookingBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED], null, null, null, null, null,
                null, null, ['enter_time_plan' => ['start_time' => $todayStartTime], 'arranged' => 1], null, null);
            foreach ($ndayBookingBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 2) {
            $todayLeaveBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['leave_time_plan' => ['start_time' => $todayStartTime, 'end_time' => $todayEndTime], 'arranged' => 1], null, null);
            foreach ($todayLeaveBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 4) {
            $ndayLeaveBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['leave_time_plan' => ['start_time' => $todayStartTime], 'arranged' => 1], null, null);
            foreach ($ndayLeaveBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 5) {
            $priceProjectBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['price_project' => 1, 'arranged' => 1], null, null);
            foreach ($priceProjectBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 6) {
            $priceProjectBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['price_project' => 2, 'arranged' => 1], null, null);
            foreach ($priceProjectBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 7) {
            $priceProjectBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['price_project' => 3, 'arranged' => 1], null, null);
            foreach ($priceProjectBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        } elseif ($tag_type == 8) {
            $priceProjectBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null,
                null, null, ['price_project' => 4, 'arranged' => 1], null, null);
            foreach ($priceProjectBills as $item) {
                $itemRoomId = $item->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds[] = $itemRoomId;
                }
            }
        }
        $roomIds2 = [];
        if (!empty($userName) || !empty($billSource)) {
            $billSourceBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT],
                $userName, null, null, $billSource, null,
                null, null, null, null, null);
            foreach ($billSourceBills as $itemSourceBill) {
                $itemRoomId = $itemSourceBill->room_id;
                if (!in_array($itemRoomId, $roomIds) && $itemRoomId) {
                    $roomIds2[] = $itemRoomId;
                }
            }
            if (empty($roomIds)) {
                $roomIds = $roomIds2;
            } else {
                $roomIds = array_intersect($roomIds, $roomIds2);
            }
        }

        $roomIds3 = [];
        if (!empty($sellTypeId)) {
            $saleTypeIds = RoomSaleTypeService::getInstance()->getRoomSaleTypeIds($sellTypeId);
            $sellTypeBills = RoomBillService::getInstance()->adminRoomBillList($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT],
                null, null, null, null, null,
                $saleTypeIds, null, null, null, null);
            foreach ($sellTypeBills as $itemSell) {
                $itemRoomId = $itemSell->room_id;
                if (!in_array($itemRoomId, $roomIds3) && $itemRoomId) {
                    $roomIds3[] = $itemRoomId;
                }
            }
            if (empty($roomIds)) {
                $roomIds = $roomIds3;
            } else {
                $roomIds = array_intersect($roomIds, $roomIds3);
            }
        }
        return $roomIds;
    }

    /**
     * 查询房态
     * @param $shopIdZong
     * @param $shopId
     * @param $roomTypeIds
     * @param $roomSaleTypeId
     * @param $memberGrade
     * @param $intermediaries_id
     * @param $recordStatus
     * @param $roomNumber
     * @param $floorId
     * @param $buildingId
     * @param $roomStatus
     * @param $startTime
     * @param $endTime
     * @return array
     * @throws MyException
     */
    /**
     * 处理房态状态参数
     * @param array $recordStatus
     * @return array
     */
    private function processRecordStatus($recordStatus)
    {
        $mustEmpty = false;
        $hasEmptyStatus = false;
        $newRecordStatus = [];

        if (empty($recordStatus)) {
            $hasEmptyStatus = true;
        } else {
            if (count($recordStatus) == 1 && in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $mustEmpty = true;
            }
            if (in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $hasEmptyStatus = true;
                $newRecordStatus = [];
                foreach ($recordStatus as $itemRecordStatus) {
                    if (RoomRecordStatus::EMPTY_ROOM != $itemRecordStatus) {
                        array_push($newRecordStatus, $itemRecordStatus);
                    }
                }
            } else {
                $newRecordStatus = $recordStatus;
            }
        }

        return [
            'mustEmpty' => $mustEmpty,
            'hasEmptyStatus' => $hasEmptyStatus,
            'newRecordStatus' => $newRecordStatus
        ];
    }

    /**
     * 处理单个房间数据
     * @param object $itemRoom 房间对象
     * @param array $selectRoomIds 可选房间ID列表
     * @param RoomQueryParams $params 查询参数
     * @param array $contextData 上下文数据
     * @return object|null 处理后的房间对象，如果不符合条件返回null
     */
    private function processSingleRoom($itemRoom, $selectRoomIds, $params, $contextData)
    {
//        DebugUtil::log('processSingleRoom');
        $roomId = $itemRoom->id;
        $tempBillId = 0;

        // 检查房间是否在可选范围内
        if ($params->tag_type || $params->userName || $params->billSource || $params->sellTypeId) {
            if (empty($selectRoomIds) || !in_array($roomId, $selectRoomIds)) {
                DebugUtil::log('skip 0');
                return null;
            }
        }

        $tempRoomNumber = $itemRoom->room_number;
        $tempToomTypeName = $itemRoom->room_type_name;

        // 获取当前房态
        $currentRecord = $this->getCurrentRecord($roomId, $contextData['allRoomStatusRecordList'], $contextData['allRoomStatusRecordRoomIdList']);

        // 获取未来房态列表
        $tempFutureRecordList = $this->getFutureRecordList($roomId, $contextData['allFutureRecordList'], $contextData['allFutureRecordRoomIdList']);

        // 处理在住订单
        $stayBillResult = $this->processStayBills($roomId, $contextData['stayBills'], $contextData['mustEmpty'],
            $params->shopId, $tempRoomNumber, $tempToomTypeName, $contextData['roomStatusList']);
//        DebugUtil::log(json_encode($stayBillResult));
        if ($stayBillResult['skip']) {
            return null;
        }
        if ($stayBillResult['currentRecord']) {
            $currentRecord = $stayBillResult['currentRecord'];
        }

        // 处理未来房态记录
        $futureRecordList = $this->processFutureRecords($currentRecord, $tempFutureRecordList);

        // 检查是否只查询空房
        if ($contextData['mustEmpty'] && $currentRecord) {
            return null;
        }

        // 设置房间状态信息
        $roomStatusResult = $this->setRoomStatusInfo($itemRoom, $currentRecord, $contextData['hasEmptyStatus'],
            $contextData['newRecordStatus'], $contextData['emptyStatus']);
        if ($roomStatusResult['skip']) {
            return null;
        }

        $tempBillId = $roomStatusResult['tempBillId'];
        $start_time_plan = $roomStatusResult['start_time_plan'];
        $end_time_plan = $roomStatusResult['end_time_plan'];
        $item_room_status_record_id = $roomStatusResult['item_room_status_record_id'];

        // 设置房间其他属性
        $this->setRoomAdditionalInfo($itemRoom, $futureRecordList, $item_room_status_record_id, $tempBillId,
            $start_time_plan, $end_time_plan, $params->shopIdZong, $contextData['roomStatusColorsList']);

        return $itemRoom;
    }

    /**
     * 获取当前房态记录
     */
    private function getCurrentRecord($roomId, $allRoomStatusRecordList, $allRoomStatusRecordRoomIdList)
    {
        $currentRecord = null;
        if (in_array($roomId, $allRoomStatusRecordRoomIdList)) {
            foreach ($allRoomStatusRecordList as $itemRecord) {
                if ($itemRecord->room_id == $roomId) {
                    $currentRecord = $itemRecord;
                    break;
                }
            }
        }
        return $currentRecord;
    }

    /**
     * 获取未来房态记录列表
     */
    private function getFutureRecordList($roomId, $allFutureRecordList, $allFutureRecordRoomIdList)
    {
        $tempFutureRecordList = [];
        if (in_array($roomId, $allFutureRecordRoomIdList)) {
            foreach ($allFutureRecordList as $itemFutureRecord) {
                if ($itemFutureRecord->room_id == $roomId) {
                    array_push($tempFutureRecordList, $itemFutureRecord);
                }
            }
        }
        return $tempFutureRecordList;
    }

    /**
     * 处理在住订单
     */
    private function processStayBills($roomId, $stayBills, $mustEmpty, $shopId, $tempRoomNumber, $tempToomTypeName, $roomStatusList)
    {
        $result = ['skip' => false, 'currentRecord' => null];
        foreach ($stayBills as $itemBill) {
            if ($itemBill->room_id == $roomId) {
                if ($mustEmpty) {
                    $result['skip'] = true;
                    break;
                }
                $tempBillStatus = $itemBill->bill_status;
                $tempRecord = RoomBillUtil::getInstance()->getRoomCurrentRecordStatus($shopId, $roomId, $itemBill->id, null, $tempRoomNumber, $tempToomTypeName, $roomStatusList);
                if ($tempRecord) {
                    if ($tempRecord->end_time_actual) {
                        break;
                    }
                    $result['currentRecord'] = $tempRecord;
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * 处理未来房态记录
     */
    private function processFutureRecords($currentRecord, $tempFutureRecordList)
    {
        $futureRecordList = [];
        if ($currentRecord) {
            foreach ($tempFutureRecordList as $item) {
                if ($item->id != $currentRecord->id) {
                    array_push($futureRecordList, $item);
                }
            }
        } else {
            $futureRecordList = $tempFutureRecordList;
        }
        ArrayUtils::getInstance()->arraySort($futureRecordList, "start_time_plan", 0);
        return $futureRecordList;
    }

    /**
     * 设置房间状态信息
     */
    private function setRoomStatusInfo($itemRoom, $currentRecord, $hasEmptyStatus, $newRecordStatus, $emptyStatus)
    {
        $result = [
            'skip' => false,
            'tempBillId' => 0,
            'start_time_plan' => null,
            'end_time_plan' => null,
            'item_room_status_record_id' => RoomRecordStatus::EMPTY_ROOM
        ];

        if (!$currentRecord) {
            if (!$hasEmptyStatus) {
                $result['skip'] = true;
                return $result;
            } else {
                $itemRoom->room_status_name = $emptyStatus->status_name;
                $itemRoom->room_status_record_id = RoomRecordStatus::EMPTY_ROOM;
                $itemRoom->room_status_color = $emptyStatus->color;
            }
        } else {
            $tempStatusId = $currentRecord->status_id;
            if (!empty($newRecordStatus) && !in_array($tempStatusId, $newRecordStatus)) {
                $result['skip'] = true;
                return $result;
            }
            if ($currentRecord->bill_id) {
                $currentRecord->bill_status = $tempStatusId;
                $result['tempBillId'] = $currentRecord->bill_id;
            } else {
                $itemRoom->link_man = null;
            }
            $itemRoom->room_status_name = $currentRecord->room_status_name;
            $result['item_room_status_record_id'] = $tempStatusId;
            $itemRoom->room_status_color = $currentRecord->color;
            $result['start_time_plan'] = $currentRecord->start_time_plan;
            $result['end_time_plan'] = $currentRecord->end_time_plan;
        }

        return $result;
    }

    /**
     * 设置房间附加信息
     */
    private function setRoomAdditionalInfo($itemRoom, $futureRecordList, $item_room_status_record_id, $tempBillId,
                                           $start_time_plan, $end_time_plan, $shopIdZong, $roomStatusColorsList)
    {
        $itemRoom->room_status_record_future = $futureRecordList;
        $itemRoom->room_status_record_id = $item_room_status_record_id;

        foreach ($futureRecordList as $tempItem) {
            if ($tempItem->bill_id) {
                $tempItem->bill_info = $this->getRoomStatusRecordBillInfo($shopIdZong, $tempItem->bill_id, true);
            }
        }

        $tempCleanStatus = $itemRoom->clean_status;
        list($color, $colorName) = $this->getRoomStatusColor($roomStatusColorsList, $tempCleanStatus);
        $itemRoom->clear_color = $color;
        $itemRoom->clear_status_name = $colorName;
        $itemRoom->bill_id = $tempBillId;
        $itemRoom->start_time_plan = $start_time_plan;
        $itemRoom->end_time_plan = $end_time_plan;

        $billInfo = null;
        if ($tempBillId) {
            $billInfo = $this->getRoomStatusRecordBillInfo($shopIdZong, $tempBillId, false);
        }
        $itemRoom->bill_info = $billInfo;
    }

    /**
     * 组装最终返回数据
     * @param array $newRoomList 处理后的房间列表
     * @param int $stayCount 在住数量
     * @param int $totalRoomCount 总房间数量
     * @param RoomQueryParams $params 查询参数
     * @return array
     */
    private function assembleResultData($newRoomList, $stayCount, $totalRoomCount, $params)
    {
        $rooms = RoomService::getInstance()->groupByBuilding($newRoomList);

        // 计算入住率
        $stay_rate = $totalRoomCount == 0 ? 0 : sprintf('%.1f', $stayCount / $totalRoomCount * 100);

        // 基础数据
        $data = [
            'list' => $rooms,
            'stay_rate' => $stay_rate,
            'staying_count' => $stayCount,
            'total_count' => $totalRoomCount
        ];

        // 获取统计数据
        $countData = $this->getRoomCountData($params->shopIdZong, $params->shopId, $params->startTime, $params->endTime);

        // 合并统计数据
        $data['room_type_count'] = $countData['room_type_count'];
        $data['clean_status_count'] = $countData['clean_status_count'];
        $data['room_status_count'] = $countData['room_status_count'];
        $data['today_arrive_count'] = $countData['today_arrive_count'];
        $data['today_leave_count'] = $countData['today_leave_count'];
        $data['n_day_arrive'] = $countData['n_day_arrive'];
        $data['n_day_leave'] = $countData['n_day_leave'];
        $data['user_bill_count'] = $countData['user_bill_count'];
        $data['member_bill_count'] = $countData['member_bill_count'];
        $data['intermediary1_bill_count'] = $countData['intermediary1_bill_count'];
        $data['intermediary2_bill_count'] = $countData['intermediary2_bill_count'];
        $data['bill_source_data'] = $countData['bill_source_data'];
        $data['bill_sell_type_data'] = $countData['bill_sell_type_data'];

        return $data;
    }

    public function getUsableRoomList($shopIdZong, $shopId, $roomTypeIds, $roomSaleTypeId, $memberGrade, $intermediaries_id,
                                      $recordStatus, $roomNumber, $floorId, $buildingId, $roomStatus, $startTime,
                                      $endTime, $tag_type, $userName, $billSource, $sellTypeId)
    {
        // 创建参数对象
        $params = new RoomQueryParams($shopIdZong, $shopId, $roomTypeIds, $roomSaleTypeId, $memberGrade, $intermediaries_id,
            $recordStatus, $roomNumber, $floorId, $buildingId, $roomStatus, $startTime,
            $endTime, $tag_type, $userName, $billSource, $sellTypeId);

        $selectRoomIds = $this->getUsableRoomId($params->shopId, $params->tag_type, $params->userName, $params->billSource, $params->sellTypeId);
        if (empty($params->roomTypeIds)) {
            $params->roomTypeIds = RoomTypeService::getInstance()->getUsableRoomTypeIds($params->shopIdZong, $params->shopId, $params->roomSaleTypeId, $params->memberGrade, $params->intermediaries_id);
        }
        if (empty($params->roomTypeIds)) {
            return [];
        }
        // 处理房态状态参数
        $statusResult = $this->processRecordStatus($params->recordStatus);
        $mustEmpty = $statusResult['mustEmpty'];
        $hasEmptyStatus = $statusResult['hasEmptyStatus'];
        $newRecordStatus = $statusResult['newRecordStatus'];
        // 准备基础数据
        $newRoomList = [];
        //房态颜色
        $roomStatusColorsList = RoomClearStatusColorService::getInstance()->getRoomClearStatusColorList($params->shopId);
        //房态
        $roomStatusList = RoomStatusService::getInstance()->getRoomStatusList($params->shopId);
        //空房态
        $emptyStatus = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($params->shopId, RoomRecordStatus::EMPTY_ROOM);
        //所有房间列表
        $roomList = RoomService::getInstance()->getRoomList($params->shopId, $params->roomTypeIds, 1, $params->floorId, $params->buildingId, $params->roomNumber, $params->roomStatus);
        //在住订单
        $stayBills = RoomBillService::getInstance()->getBillByRoomId($params->shopId, null, $params->roomTypeIds, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
        $stayCount = count($stayBills);
        //未来房态
        $allFutureRecordList = $this->getRoomRecordStatusListByRoomIdInfo($params->shopId, null, $params->endTime, null, $newRecordStatus, true, $roomStatusList);
        //未来房态房间
        $allFutureRecordRoomIdList = $this->getRoomRecordStatusRoomIds($params->shopId, $params->endTime, null, $newRecordStatus, false);
        //所有房态记录列表
        $allRoomStatusRecordList = $this->getRoomRecordStatusListByRoomIdInfo($params->shopId, null, $params->startTime, $params->endTime, $newRecordStatus, true, $roomStatusList);
        $allRoomStatusRecordRoomIdList = $this->getRoomRecordStatusRoomIds($params->shopId, $params->startTime, $params->endTime, $newRecordStatus, true);
        $totalRoomCount = count($roomList);
        // 准备上下文数据
        $contextData = [
            'mustEmpty' => $mustEmpty,
            'hasEmptyStatus' => $hasEmptyStatus,
            'newRecordStatus' => $newRecordStatus,
            'roomStatusColorsList' => $roomStatusColorsList,
            'roomStatusList' => $roomStatusList,
            'emptyStatus' => $emptyStatus,
            'stayBills' => $stayBills,
            'allFutureRecordList' => $allFutureRecordList,
            'allFutureRecordRoomIdList' => $allFutureRecordRoomIdList,
            'allRoomStatusRecordList' => $allRoomStatusRecordList,
            'allRoomStatusRecordRoomIdList' => $allRoomStatusRecordRoomIdList
        ];
        // 处理每个房间
        foreach ($roomList as $itemRoom) {
            $processedRoom = $this->processSingleRoom($itemRoom, $selectRoomIds, $params, $contextData);
            if ($processedRoom !== null) {
                $newRoomList[] = $processedRoom;
            }
        }
        // 组装并返回最终数据
        return $this->assembleResultData($newRoomList, $stayCount, $totalRoomCount, $params);
    }


    /**
     * 查询房态
     * @param $shopIdZong
     * @param $shopId
     * @param $roomTypeIds
     * @param $roomSaleTypeId
     * @param $memberGrade
     * @param $intermediaries_id
     * @param $recordStatus
     * @param $roomNumber
     * @param $floorId
     * @param $buildingId
     * @param $roomStatus
     * @param $startTime
     * @param $endTime
     * @return array
     * @throws MyException
     */
    public function getRoomCountData($shopIdZong, $shopId, $startTime, $endTime)
    {
        $todayStartTime = strtotime(date("Y-m-d 00:00:00"));
        $todayEndTime = strtotime(date("Y-m-d 23:59:59"));
        $roomTypeIds = [];
        $roomTypeList = RoomTypeService::getInstance()->getRoomTypeList($shopId, 1, null, null, null);
        foreach ($roomTypeList as $itemRoomType) {
            $roomTypeIds[] = $itemRoomType->id;
        }
        $mustEmpty = false;
        $hasEmptyStatus = true;
        $newRecordStatus = [];

        $roomSellList = RoomSellTypeService::getInstance()->getRoomSellTypeList($shopId, 1);
        foreach ($roomSellList as $itemRoomSell) {
            $tempId = $itemRoomSell->id;
            $itemRoomSell['room_count'] = 0;
            $tempSaleIds = RoomSaleTypeService::getInstance()->getRoomSaleTypeIds($tempId);
            $itemRoomSell['sale_ids'] = $tempSaleIds;
        }

        $currentRoomTypeList = [];
        $roomStatusColorsList = RoomClearStatusColorService::getInstance()->getRoomClearStatusColorList($shopId);
        foreach ($roomStatusColorsList as $itemCleanStatus) {
            $itemCleanStatus['room_count'] = 0;
        }
        $roomStatusList = RoomStatusService::getInstance()->getRoomStatusList($shopId);
        foreach ($roomStatusList as $itemRoomStatus) {
            $itemRoomStatus['room_count'] = 0;
        }
        $user_bill_count = 0;
        $member_bill_count = 0;
        $intermediary1_bill_count = 0;
        $intermediary2_bill_count = 0;
        $billSourceList = BillSourceService::getInstance()->getBillSourceList(1, $shopId, null, null);
        foreach ($billSourceList as $itemBillSourceModel) {
            $itemBillSourceModel['bill_count'] = 0;
        }
        $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeIds, 1, null, null, null, null);
        $allBillS = RoomBillService::getInstance()->getBillByRoomId($shopId, null, $roomTypeIds, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT, RoomBillStatus::TO_BE_CANCELED]);
        foreach ($allBillS as $itemRoomBill) {
            $tempBillSourceId = $itemRoomBill->bill_source;
            $tempSaleTypeId = $itemRoomBill->sale_type_id;
            foreach ($billSourceList as $tempBillSource) {
                if ($tempBillSource->id == $tempBillSourceId) {
                    $tempBillSource['bill_count'] = $tempBillSource['bill_count'] + 1;
                    break;
                }
            }
            $tempRoomId = $itemRoomBill->room_id;
            if (!$tempRoomId) {
                continue;
            }
            $itemPriceProject = $itemRoomBill->price_project;
            if ($itemPriceProject == 1) {
                $user_bill_count++;
            } elseif ($itemPriceProject == 2) {
                $member_bill_count++;
            } elseif ($itemPriceProject == 3) {
                $intermediary1_bill_count++;
            } elseif ($itemPriceProject == 4) {
                $intermediary2_bill_count++;
            }
            foreach ($roomSellList as $itemRoomSell) {
                $tempSaleIds = $itemRoomSell['sale_ids'];
                if (in_array($tempSaleTypeId, $tempSaleIds)) {
                    $itemRoomSell['room_count'] = $itemRoomSell['room_count'] + 1;
                }
            }
        }
        $stayBills = RoomBillService::getInstance()->getBillByRoomId($shopId, null, $roomTypeIds, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT]);
        $todayBookingBillsCount = RoomBillService::getInstance()->adminGetRoomBillCount($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED], null, null, null, null, null, null,
            null, ['enter_time_plan' => ['start_time' => $todayStartTime, 'end_time' => $todayEndTime], 'arranged' => 1]);
        $ndayBookingBillsCount = RoomBillService::getInstance()->adminGetRoomBillCount($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED], null, null, null, null, null, null,
            null, ['enter_time_plan' => ['start_time' => $todayEndTime,], 'arranged' => 1]);
        $todayLeaveBillsCount = RoomBillService::getInstance()->adminGetRoomBillCount($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null, null,
            null, ['leave_time_plan' => ['start_time' => $todayStartTime, 'end_time' => $todayEndTime], 'arranged' => 1]);
        $ndayLeaveBillsCount = RoomBillService::getInstance()->adminGetRoomBillCount($shopId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], null, null, null, null, null, null,
            null, ['leave_time_plan' => ['start_time' => $todayEndTime,], 'arranged' => 1]);
        $allRoomStatusRecordList = $this->getRoomRecordStatusListByRoomIdInfo($shopId, null, $startTime, $endTime, $newRecordStatus, true, $roomStatusList);
        $allRoomStatusRecordRoomIdList = $this->getRoomRecordStatusRoomIds($shopId, $startTime, $endTime, $newRecordStatus, true);
        foreach ($roomList as $itemRoom) {
            $tempRoomTypeId = $itemRoom->room_type_id;
            $roomId = $itemRoom->id;
            $tempRoomNumber = $itemRoom->room_number;
            $tempToomTypeName = $itemRoom->room_type_name;
            //当前房态
            $currentRecord = null;
            if (in_array($roomId, $allRoomStatusRecordRoomIdList)) {
                foreach ($allRoomStatusRecordList as $itemRecord) {
                    if ($itemRecord->room_id == $roomId) {
                        $currentRecord = $itemRecord;
                        break;
                    }
                }
            }
            //如果未完成的房态不在查询的范围中，则加入
            //如果未完成的订单是在住状态或者待退房状态这置顶
            $flag = false;
            foreach ($stayBills as $itemBill) {
                if ($itemBill->room_id == $roomId) {
                    if ($mustEmpty) {
                        $flag = true;
                        break;
                    }
                    $tempBillStatus = $itemBill->bill_status;
                    $tempRecord = RoomBillUtil::getInstance()->getRoomCurrentRecordStatus($shopId, $roomId, $itemBill->id, $tempBillStatus, $tempRoomNumber, $tempToomTypeName, $roomStatusList);
                    if ($tempRecord) {
                        if ($tempRecord->end_time_actual) {
                            break;
                        }
                        $currentRecord = $tempRecord;
                        break;
                    }
                }
            }
            if ($flag) {
                //不符合只查询空房要求
                continue;
            }
            $futureRecordList = [];
            foreach ($roomStatusList as $itemRoomStatus) {
                if ($currentRecord) {
                    $statusId = $currentRecord->status_id;
                    if ($statusId == $itemRoomStatus->id) {
                        $itemRoomStatus['room_count'] += 1;
                        break;
                    }
                } else {
                    if ($itemRoomStatus->id == 5) {
                        $itemRoomStatus['room_count'] += 1;
                        break;
                    }
                }
            }
            //只查询空房
            if ($mustEmpty && $currentRecord) {
                continue;
            }
            if (!$currentRecord) {
                if (!$hasEmptyStatus) {
                    continue;
                }
            } else {
                $tempStatusId = $currentRecord->status_id;
                if (!empty($newRecordStatus) && !in_array($tempStatusId, $newRecordStatus)) {
                    continue;
                }
            }
            foreach ($futureRecordList as $tempItem) {
                if ($tempItem->bill_id) {
                    $tempItem->bill_info = $this->getRoomStatusRecordBillInfo($shopIdZong, $tempItem->bill_id, true);
                }
            }
            $tempCleanStatus = $itemRoom->clean_status;
            foreach ($roomStatusColorsList as $itemCleanStatus) {
                if ($itemCleanStatus->clear_id == $tempCleanStatus) {
                    $itemCleanStatus['room_count'] += 1;
                }
            }
            if (!isset($currentRoomTypeList[$tempRoomTypeId])) {
                if (empty($currentRecord)) {
                    $currentRoomTypeList[$tempRoomTypeId] = ['total_count' => 1, 'usable_count' => 1, 'room_type_name' => $tempToomTypeName, 'room_type_id' => $tempRoomTypeId, 'staying_count' => 0, 'booking_count' => 0];
                } else {
                    $currentRoomTypeList[$tempRoomTypeId] = ['total_count' => 1, 'usable_count' => 0, 'room_type_name' => $tempToomTypeName, 'room_type_id' => $tempRoomTypeId, 'staying_count' => 0, 'booking_count' => 0];
                }
            } else {
                $tempRoomTypeValue = $currentRoomTypeList[$tempRoomTypeId];
                if (empty($currentRecord)) {
                    $tempRoomTypeValue['total_count'] += 1;
                    $tempRoomTypeValue['usable_count'] += 1;
                    $currentRoomTypeList[$tempRoomTypeId] = $tempRoomTypeValue;
                } else {
                    $tempRoomTypeValue['total_count'] += 1;
                    $currentRoomTypeList[$tempRoomTypeId] = $tempRoomTypeValue;
                }
            }
        }
        $currentRoomTypeData = [];
        foreach ($roomTypeList as $itemRoomType) {
            $roomTypeName = $itemRoomType->name;
            $id = $itemRoomType->id;
            $room_count = RoomService::getInstance()->getRoomCount($shopId, $id, 1, null, null, null, null);
            foreach ($allBillS as $itemBill) {
                if ($itemBill->room_type_id == $id) {
//                    $itemBillId = $itemBill->id;
                    $itemBillCode = $itemBill->bill_code;
                    $itemBillStatus = $itemBill->bill_status;
                    $enterTimePlan = $itemBill->enter_time_plan;
//                    $leaveTimePlan = $itemBill->leave_time_plan;
//                    $enterTime = $itemBill->enter_time;
                    if (in_array($itemBillStatus, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::TO_BE_CANCELED])) {
                        if ($enterTimePlan < strtotime(date('Y-m-d')) + 24 * 60 * 60) {
                            $currentRoomTypeList[$id]['booking_count'] += 1;
                        }
                    } else {
                        $currentRoomTypeList[$id]['staying_count'] += 1;
                    }
                }
            }
            $inItem = false;
            foreach ($currentRoomTypeList as $item) {
                if ($item['room_type_id'] == $id) {
                    $item['total_count'] = $room_count;
                    $currentRoomTypeData[] = $item;
                    $inItem = true;
                    break;
                }
            }
            if (!$inItem) {
                $currentRoomTypeData[] = ['total_count' => $room_count, 'usable_count' => 0, 'room_type_name' => $roomTypeName, 'room_type_id' => $id, 'staying_count' => 0, 'booking_count' => 0];
            }
        }
        return [
            'room_type_count' => $currentRoomTypeData,
            "clean_status_count" => $roomStatusColorsList,
            'room_status_count' => $roomStatusList,
            'today_arrive_count' => $todayBookingBillsCount,//今日预抵
            'today_leave_count' => $todayLeaveBillsCount,//今日预离
            'n_day_arrive' => $ndayBookingBillsCount,//n日预抵
            'n_day_leave' => $ndayLeaveBillsCount,//n日预离
            'user_bill_count' => $user_bill_count,//散客
            'member_bill_count' => $member_bill_count,//会员
            'intermediary1_bill_count' => $intermediary1_bill_count,//单位
            'intermediary2_bill_count' => $intermediary2_bill_count,//中介
            'bill_source_data' => $billSourceList,//中介
            'bill_sell_type_data' => $roomSellList,//销售方式
        ];
    }

    private function getRoomStatusColor($roomStatusColorsList, $colorStatus)
    {
        foreach ($roomStatusColorsList as $item) {
            if ($item->clear_id == $colorStatus) {
                return array($item->color, $item->status_name);
            }
        }
        return array("#39b54a", "净");
    }


    public function getRoomStatusRecordBillInfo($shopIdZong, $tempBillId, $simple)
    {
        $roomBill = RoomBillService::getInstance()->getBillById($tempBillId);
        if (empty($roomBill)) {
            return null;
        }
        $shopId = $roomBill->shop_id;
        $secrecy = $roomBill->secrecy;
        $tempGrade = $roomBill->temp_grade;
        $priceProject = $roomBill->price_project;
        $linkPhone = $roomBill->link_phone;
        $intermediaryId = $roomBill->intermediary_id;
        $billSourceId = $roomBill->bill_source;
        $linkMan = $roomBill->link_man;
        $leave_time_plan = $roomBill->leave_time_plan;
        $enter_time_plan = $roomBill->enter_time_plan;
        $enter_time = $roomBill->enter_time;
        $memo = $roomBill->memo;
        $bill_status = $roomBill->bill_status;
        $sale_type_id = $roomBill->sale_type_id;
        $saleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeByIdFromCache($sale_type_id);
        $saleTypeName = $saleType->name;
        $sellTypeName = $saleType->sell_type_name;
        $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
        $billSourceName = $billSource->source_name;
        if ($simple) {
            $billInfo = [
                'link_man' => $linkMan,
                'leave_time_plan' => $leave_time_plan,
                'enter_time_plan' => $enter_time_plan,
                'enter_time' => $enter_time,
                'memo' => $memo,
                'bill_status' => $bill_status,
                'link_phone' => $linkPhone,
                'price_project' => $priceProject,
                'intermediary_id' => $intermediaryId,
                'secrecy' => $secrecy,
                'sale_type_name' => $saleTypeName,
                'sell_type_name' => $sellTypeName,
                'bill_source_name' => $billSourceName,
            ];
        } else {
            $billConnectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($tempBillId);
            if ($billConnectBill) {
                $connectCode = $billConnectBill->connect_code;
                $isMain = $billConnectBill->is_main;
            } else {
                $connectCode = "";
                $isMain = 1;
            }
            $intermediaryName = "";

            $consumeAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempBillId, 1, '');
            $paymentAmount = RoomBillDetailService::getInstance()->getDetailSumAmount($tempBillId, 2, '');
            if (empty($connectCode)) {
                $totalBillBalance = sprintf("%.2f", ($paymentAmount - $consumeAmount));
                $totalConsumeAmount = $consumeAmount;
                $totalPaymentAmount = $paymentAmount;
            } else {
                list($totalBillBalance, $totalConsumeAmount, $totalPaymentAmount) = RoomBillUtil::getInstance()->getConnectBillBalance($connectCode);
            }
            $tempGrade = MemberGradeService::getInstance()->getMemberGradeById($tempGrade);
            $tempGradeName = $tempGrade->grade_name;
            if ($intermediaryId) {
                $intermediary = IntermediaryService::getInstance()->getIntermediaryById($intermediaryId);
                $intermediaryName = $intermediary->intermediary_name;
            }
            $userList = [];
            $roomUserList = RoomBillUserService::getInstance()->getRoomBillUserList($tempBillId);
            foreach ($roomUserList as $itemUser) {
                $tempRoomUser = $itemUser->name;
                $userList[] = ['id' => $itemUser->id, 'name' => $tempRoomUser, 'gender' => $itemUser->gender, 'is_main' => $itemUser->is_main];
            }
            $billInfo = [
                'link_man' => $linkMan,
                'leave_time_plan' => $leave_time_plan,
                'enter_time_plan' => $enter_time_plan,
                'enter_time' => $enter_time,
                'memo' => $memo,
                'bill_status' => $bill_status,
                'link_phone' => $linkPhone,
                'bill_source_name' => $billSourceName,
                'connect_code' => $connectCode,
                'is_main' => $isMain,
                'room_user' => $userList,
                'price_project' => $priceProject,
                'intermediary_id' => $intermediaryId,
                'intermediary_name' => $intermediaryName,
                'tempGrade_name' => $tempGradeName,
                'consume_amount' => $consumeAmount,
                'payment_amount' => $paymentAmount,
                'connect_consume_amount' => $totalConsumeAmount,
                'connect_payment_amount' => $totalPaymentAmount,
                'total_bill_balance' => $totalBillBalance,
                'secrecy' => $secrecy,
                'sale_type_name' => $saleTypeName,
                'sell_type_name' => $sellTypeName,
            ];
        }

        return $billInfo;
    }


    /**
     * 预定选房
     * @param $shopIdZong
     * @param $shopId
     * @param $roomTypeIds
     * @param $roomSaleTypeId
     * @param $memberGrade
     * @param $intermediaries_id
     * @param $recordStatus
     * @param $roomNumber
     * @param $floorId
     * @param $buildingId
     * @param $roomStatus
     * @param $startTime
     * @param $endTime
     * @param $admin
     * @return array
     * @throws MyException
     */
    public function selectRoom($shopIdZong, $shopId, $roomTypeIds, $roomSaleTypeId, $memberGrade, $intermediaries_id,
                               $recordStatus, $roomNumber, $floorId, $buildingId, $roomStatus, $startTime, $endTime, $admin)
    {
        if (empty($roomStatus)) {
            $roomStatus = [RoomClearStatus::CLEAR, RoomClearStatus::DIRTY, RoomClearStatus::CLEARING];
        }
        $mustEmpty = false;
        $hasEmptyStatus = false;
        $newRecordStatus = [];
        if (empty($recordStatus)) {
            $hasEmptyStatus = true;
            $newRecordStatus = [RoomRecordStatus::CLOSED];
        } else {
            if (count($recordStatus) == 1 && in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $mustEmpty = true;
            }
            if (in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $hasEmptyStatus = true;
                $newRecordStatus = [];
                foreach ($recordStatus as $itemRecordStatus) {
                    if (RoomRecordStatus::EMPTY_ROOM != $itemRecordStatus) {
                        $newRecordStatus[] = $itemRecordStatus;
                    }
                }
            } else {
                $newRecordStatus = $recordStatus;
            }
        }
        $newRoomList = [];
        $emptyStatus = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, RoomRecordStatus::EMPTY_ROOM);
        $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeIds, 1, $floorId, $buildingId, $roomNumber, $roomStatus, true);
        foreach ($roomList as $itemRoom) {
            $tempBillId = 0;
            $roomId = $itemRoom->id;
            $stayingBill = RoomBillService::getInstance()->getStayingBillByRoomId($shopId, $roomId);
            if ($stayingBill) {
                $billEnterTime = $stayingBill->enter_time;
                $leaveTimePlan = time() + 60;
                if (TimeUtil::getInstance()->timeConflict($startTime, $endTime, $billEnterTime, $leaveTimePlan)) {
                    continue;
                }
            }
//			$tempRoomNumber = $itemRoom->room_number;
//			$tempToomTypeName = $itemRoom->room_type_name;
            //当前房态
            $futureRecordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, []);
            if ($futureRecordList) {
                $currentRecord = array_shift($futureRecordList);
            } else {
                $currentRecord = null;
            }
            //只查询空房
            if ($mustEmpty && $currentRecord) {
                continue;
            }
            $start_time_plan = null;
            $end_time_plan = null;
            $item_room_status_record_id = RoomRecordStatus::EMPTY_ROOM;
            if (!$currentRecord) {
                if (!$hasEmptyStatus) {
                    continue;
                } else {
                    $itemRoom->room_status_name = $emptyStatus->status_name;
                    $itemRoom->room_status_record_id = RoomRecordStatus::EMPTY_ROOM;
                    $itemRoom->room_status_color = $emptyStatus->color;
                }
            } else {
                $tempStatusId = $currentRecord->status_id;
                if (!empty($newRecordStatus) && !in_array($tempStatusId, $newRecordStatus)) {
                    continue;
                }
                if ($currentRecord->bill_id) {
                    $currentRecord->bill_status = $tempStatusId;
                    $tempBillId = $currentRecord->bill_id;
                } else {
                    $itemRoom->link_man = null;
                }
                $itemRoom->room_status_name = $currentRecord->room_status_name;
                $item_room_status_record_id = $tempStatusId;
                $itemRoom->room_status_color = $currentRecord->color;
                $start_time_plan = $currentRecord->start_time_plan;
                $end_time_plan = $currentRecord->end_time_plan;
            }
            $itemRoom->room_status_record_id = $item_room_status_record_id;
            $colorStatus = $itemRoom->clean_status;
            $clearColor = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $colorStatus);
            $itemColor = $clearColor->color;
            $clear_status_name = $clearColor->status_name;
            $itemRoom->clear_color = $itemColor;
            $itemRoom->clear_status_name = $clear_status_name;
            $itemRoom->bill_id = $tempBillId;
            $itemRoom->start_time_plan = $start_time_plan;
            $itemRoom->end_time_plan = $end_time_plan;
            $billInfo = null;
            $itemRoom->bill_info = $billInfo;
            $itemRoom['iid'] = StringUtil::getRandomStrs(11, false, false, true, false);
            $newRoomList[] = $itemRoom;
        }
        return RoomService::getInstance()->groupByBuilding($newRoomList);
    }


    /**
     * 查询所有房态
     * @param $shopId
     * @param $roomTypeIds
     * @param $floors
     * @param $buildingIds
     * @param $cleanStatus
     * @param $roomStatus
     * @param $startTime
     * @param $endTIme
     * @return false|\PDOStatement|string|\think\Collection
     * @throws MyException
     */
    public function getRoomStatusList($shopId, $roomTypeIds, $floors, $buildingIds, $cleanStatus, $roomStatus, $startTime, $endTIme)
    {
        $rooms = RoomService::getInstance()->getRoomList($shopId, $roomTypeIds, 1, $floors, $buildingIds, '', $cleanStatus);
        $roomIds = [];
        foreach ($rooms as $room) {
            $tempRoomId = $room->id;
            $roomIds[] = $tempRoomId;
        }
        $list = $this->getRoomRecordStatusListByRoomId($shopId, $roomIds, $startTime, $endTIme, $roomStatus);
        foreach ($list as $item) {
            $roomId = $item->room_id;
            $tempRoom = RoomService::getInstance()->getRoomById($roomId);
            $clearId = $tempRoom->clean_status;
            $clearColor = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $clearId);
            $floorId = $tempRoom->floor_id;
            $buildingId = $tempRoom->building_id;
            $item->room_number = $tempRoom->room_number;
            $tempFloor = RoomFloorService::getInstance()->getRoomFloorById($floorId);
            $tempBuilding = RoomBuildingService::getInstance()->getBuildingById($buildingId);
            $item->floor_name = $tempFloor->name;
            $item->building_name = $tempBuilding->name;
            $item->clear_color = $clearColor->color;
            $item->clear_status_name = $clearColor->status_name;
        }
        ArrayUtils::getInstance()->arraySort($list, 'room_number', 'desc');
        return $list;
    }


    /**
     * @param $shopIdZong
     * @param $shopId
     * @param $roomTypeIds
     * @param $roomSaleTypeId
     * @param $memberGrade
     * @param $intermediaries_id
     * @param $floorId
     * @param $buildingId
     * @param $roomStatus
     * @param $startTime
     * @param $endTime
     * @param $days
     * @param $admin
     * @return array
     * @throws MyException
     */
    public function selectRoomType($shopIdZong, $shopId, $roomTypeIds, $roomSaleTypeId, $memberGrade, $intermediaries_id, $startTime, $endTime, $days, $admin)
    {
        $date = date("y-m-d", $startTime);
        if (!$memberGrade) {
            $memberGrade = MemberUtilService::getInstance()->getRoomPriceGrade($shopIdZong, '', 2);
        }
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
        $roomSaleTypeSign = $roomSaleType->sell_type_sign;
        $roomTypes = RoomTypeService::getInstance()->getRoomTypeListByIds($shopId, 1, $roomTypeIds);
        $newList = [];
        foreach ($roomTypes as $index => $roomType) {
            $itemRoomTypeId = $roomType->id;
            $hourRoomCount = $this->getHourRoomCount($shopId, $itemRoomTypeId, $startTime, $endTime);
            $count = $this->getMultiDayCount($shopId, $itemRoomTypeId, $startTime, $days, $admin);
            $roomType->hour_room_count = $hourRoomCount;
            $roomType->usable_room_count = $count;
            try {
                if ($roomSaleTypeSign == RoomSaleType::HOUR || $roomSaleTypeSign == RoomSaleType::CONFERENCE_ROOM) {
                    $days = 1;
                    $priceList = RoomPriceUtil::getInstance()->getDayRoomPriceArr($memberGrade, $roomSaleTypeId, $itemRoomTypeId, $intermediaries_id, $days, $date);
                } elseif ($roomSaleTypeSign == RoomSaleType::STANDARD) {
                    $priceList = RoomPriceUtil::getInstance()->getDayRoomPriceArr($memberGrade, $roomSaleTypeId, $itemRoomTypeId, $intermediaries_id, $days, $date);
                } else {
                    //长租房每天房价为当月价格的均价
                    $priceList = RoomPriceUtil::getInstance()->getLongStandardRoomPriceArr($memberGrade, $roomSaleTypeId, $itemRoomTypeId, $intermediaries_id, $days, $date);
                }
//				if (empty($priceList)){
//					continue;
//				}
                $roomType->room_prices = $priceList;
                $newList[] = $roomType;
            } catch (Exception $exception) {
                if (empty($priceList)) {
                    throw new MyException(Code::FAIL, "请检查当前用户等级对应的价格是否设置");
                }
            }
        }
        return $newList;
    }

    /**
     * 查询多天可用房型房间数量
     * @param $shopId
     * @param $itemRoomTypeId
     * @param $startTime
     * @param $days
     * @param $admin
     * @return int|string
     */
    public function getMultiDayCount($shopId, $itemRoomTypeId, $startTime, $days, $admin, $saleTypeIds = null)
    {
//        DebugUtil::log("getMultiDayCount:" . date('Y-m-d H:i:s', $startTime));
        $count = -1;
        $date = date('Y-m-d', $startTime);
        for ($i = 0; $i < $days; $i++) {
            $tempStartDate = TimeUtil::getInstance()->getDate($date, $i) . " 17:00:01";
            $tempStartTime = strtotime($tempStartDate);
            $tempStartDate = date('Y-m-d H:i:s', $tempStartTime);
            $tempEndDate = TimeUtil::getInstance()->getDate($tempStartDate, 1) . " 11:59:59";
            $tempEndTime = strtotime($tempEndDate);
            $tempCount = $this->getUsableRoomCount($shopId, $itemRoomTypeId, $tempStartTime, $tempEndTime, !$admin);
            if ($count == -1) {
                $count = $tempCount;
            } else {
                if ($tempCount < $count) {
                    $count = $tempCount;
                }
            }
        }
        return $count;
    }


    /**
     * 修改房态
     * @param $roomIds
     * @param $recordStatus
     * @param $startTime
     * @param $endTime
     * @param $remark
     * @throws MyException
     */
    public function updateRoomRecordStatus($roomIds, $recordStatus, $startTime, $endTime, $remark)
    {
        if ($startTime < time()) {
            $startTime = time();
        }
        foreach ($roomIds as $roomId) {
            $room = RoomService::getInstance()->getRoomById($roomId);
            $shopId = $room->shop_id;
            $unFinish = [
                RoomBillStatus::TO_BE_CONFIRMED,
                RoomBillStatus::TO_BE_STAY,
                RoomBillStatus::STAYING,
                RoomBillStatus::TO_CHECK_OUT,
            ];
            $recordStatusModel = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, $recordStatus);
            $status_name = $recordStatusModel->status_name;
            $unFinishBill = RoomBillService::getInstance()->getBillByRoomId($shopId, $roomId, null, $unFinish);
            if (!empty($unFinishBill)) {
                foreach ($unFinishBill as $itemUnFinishBill) {
                    $itemUnFinishBillStartTime = $itemUnFinishBill->enter_time_plan;
                    $itemUnFinishBillLeaveTime = $itemUnFinishBill->leave_time_plan;
                    if (($startTime >= $itemUnFinishBillStartTime && $startTime <= $itemUnFinishBillLeaveTime) ||
                        ($endTime >= $itemUnFinishBillStartTime && $endTime <= $itemUnFinishBillLeaveTime)) {
                        throw new MyException(Code::FAIL, "不可修改为 $status_name");
                    }
                }
            }
            $recordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, []);
            if ($recordStatus == RoomRecordStatus::EMPTY_ROOM) {
                if ($recordList) {
                    $firstRecord = $recordList[0];
                    $itemBillId = $firstRecord->bill_id;
                    $oldStartTime = $firstRecord->start_time_plan;
                    if ($itemBillId) {
                        throw new MyException(Code::FAIL, "房态冲突");
                    } else {
                        $oldRecordId = $firstRecord->id;
                        $this->updateStartTimePlanAndEndTimePlan($oldRecordId, $oldStartTime, time());
                    }
                }
            } else {
                foreach ($recordList as $itemRecord) {
                    $itemBillId = $itemRecord->bill_id;
                    $oldRecordId = $itemRecord->id;
                    $oldStartTime = $itemRecord->start_time_plan;
                    $oldEndTime = $itemRecord->end_time_plan;
                    $oldStatusId = $itemRecord->status_id;
                    if ($itemBillId == 0) {
                        $this->updateOldRecordStartTimeAndEndTime($shopId, $roomId, $startTime, $endTime, $oldStartTime, $oldEndTime, $oldRecordId, $oldStatusId, $recordStatus);
                    } else {
                        throw new MyException(Code::FAIL, "房态冲突");
                    }
                }
                $this->saveRoomRecord($shopId, 0, $roomId, $startTime, $endTime, $recordStatus, $remark);
            }
        }
    }


    /**
     * 远期房态
     * @param $shopIdZong
     * @param $shopId
     * @param $startTime
     * @param $roomSaleTypeId
     * @return array
     * @throws MyException
     */
    public function getFutureUsableRoomCount($shopIdZong, $shopId, $startTime, $roomSaleTypeId)
    {
        $startTime = strtotime(date('Y-m-d H:i:s', $startTime)) - 24 * 3600;
        $roomTypeIds = RoomTypeService::getInstance()->getUsableRoomTypeIds($shopIdZong, $shopId, $roomSaleTypeId, null, null);
        $endTime = $startTime + 7 * 24 * 3600;
        $dates = TimeUtil::getInstance()->getAllTimes(date('Y-m-d', $startTime), date('Y-m-d', $endTime), false);
//        DebugUtil::log($dates);
        $roomTypeList = RoomTypeService::getInstance()->getRoomTypeByIds($roomTypeIds);
        $list = [];
        $totalRoomCount = 0;
        $count = 0;
        $totalRoomTypeItem = [];
        foreach ($roomTypeList as $roomType) {
            $count++;
            $roomTypeId = $roomType->id;
            $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeId, 1, '', '', '',
                [RoomClearStatus::CLEAR, RoomClearStatus::DIRTY, RoomClearStatus::CLEARING]);
            $totalCount = count($roomList);
            $roomIds = [];
            foreach ($roomList as $itemRoom) {
                $roomIds[] = $itemRoom->id;
            }
//            DebugUtil::log($dates);
            $itemRoomType = [];
            foreach ($dates as $date) {
                $tempStartDate = $date['start_time'] . " 16:00:01";
                if ($date['start_time'] == date('Y-m-d') && strtotime($tempStartDate) < time()) {
                    $tempStartDate = date('Y-m-d H:i:s');
                }
                $tempStartTime = strtotime($tempStartDate);
                $tempEndDate = $date['end_time'] . " 11:59:00";
                $tempEndTime = strtotime($tempEndDate);
//                DebugUtil::log("$tempStartDate ====" . $date['end_time']);
                $usableCount = $this->getUsableRoomCount($shopId, $roomTypeId, $tempStartTime, $tempEndTime, false);
                $tempDate = date('m-d', strtotime($date['start_time']));
                $itemRoomType[] = ['date' => $tempDate, 'count' => $usableCount];
                $flag = false;
                foreach ($totalRoomTypeItem as $itemIndex => $itemTotalRoomTypeItem) {
                    if ($itemTotalRoomTypeItem['date'] == $tempDate) {
                        $totalRoomTypeItem[$itemIndex]['count'] = $itemTotalRoomTypeItem['count'] + $usableCount;
                        $flag = true;
                        break;
                    }
                }
                if (!$flag) {
                    $totalRoomTypeItem[] = ['date' => $tempDate, 'count' => $usableCount];
                }
            }

            $temp = [
                'room_type_name' => $roomType->name,
                'room_count_list' => $itemRoomType,
                'room_total_count' => $totalCount,
            ];
            $list[] = $temp;
            $totalRoomCount += $totalCount;
            if ($count == count($roomTypeList)) {
                $list[] = [
                    'room_type_name' => "总计",
                    'room_count_list' => $totalRoomTypeItem,
                    'room_total_count' => $totalRoomCount,
                ];
            }
        }
        return $list;
    }


    /**
     * 房态详情
     * @param $shopIdZong
     * @param $shopId
     * @param $roomTypeId
     * @param $startTime
     * @param $roomSaleTypeId
     * @return array
     * @throws MyException
     */
    public function getFutureRoomStatus($shopIdZong, $shopId, $roomTypeId, $startTime, $roomSaleTypeId)
    {
        $roomStatusList = RoomStatusService::getInstance()->getRoomStatusList($shopId);
        $startTime = strtotime(date('Y-m-d H:i:s', $startTime)) - 24 * 3600;
        $endTime = $startTime + 7 * 24 * 3600;
        $list = [];
        if ($roomTypeId) {
            $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
            $roomTypeName = $roomType->name;
            $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeId, 1, '', '', '', '');
            foreach ($roomList as $item) {
                $itemRoomId = $item->id;
                $itemRoomNumber = $item->room_number;
                $clearId = $item->clean_status;
                $clearColor = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $clearId);
                $cleaColor = $clearColor->color;
                $itemRoomRecord = $this->getRoomRecordStatusListByRoomIdInfo($shopId, $itemRoomId, $startTime, $endTime, '', false, $roomStatusList);
//				$itemRoomRecord = $this->getRoomRecordStatusListByRoomId($shopId, $itemRoomId, $startTime, $endTime, '', false);
                if (!empty($itemRoomRecord)) {
                    foreach ($itemRoomRecord as $itemRecord) {
                        $itemRecord->clear_color = $cleaColor;
                        $this->setRecordInfo($itemRecord);
                    }
                }
                $list[] = ['room_type_name' => $roomTypeName, 'room_id' => $itemRoomId, 'room_number' => $itemRoomNumber, 'status_record' => $itemRoomRecord];
            }
        } else {
            $roomTypeIds = RoomTypeService::getInstance()->getUsableRoomTypeIds($shopIdZong, $shopId, $roomSaleTypeId, null, null);
            $roomTypeList = RoomTypeService::getInstance()->getRoomTypeByIds($roomTypeIds);
            foreach ($roomTypeList as $roomType) {
                $itemRoomTypeId = $roomType->id;
                $roomTypeName = $roomType->name;
                $roomList = RoomService::getInstance()->getRoomList($shopId, $itemRoomTypeId, 1, '', '', '', '');
                foreach ($roomList as $item) {
                    $itemRoomId = $item->id;
                    $itemRoomNumber = $item->room_number;
                    $clearId = $item->clean_status;
                    $clearColor = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $clearId);
                    $cleaColor = $clearColor->color;
                    $itemRoomRecord = $this->getRoomRecordStatusListByRoomIdInfo($shopId, $itemRoomId, $startTime, $endTime, '', false, $roomStatusList);
//					$itemRoomRecord = $this->getRoomRecordStatusListByRoomId($shopId, $itemRoomId, $startTime, $endTime, '', false);
                    if (!empty($itemRoomRecord)) {
                        foreach ($itemRoomRecord as $itemRecord) {
                            $itemRecord->clear_color = $cleaColor;
                            $this->setRecordInfo($itemRecord);
                        }
                    }
                    $list[] = ['room_type_name' => $roomTypeName, 'room_id' => $itemRoomId, 'room_number' => $itemRoomNumber, 'status_record' => $itemRoomRecord];
                }
            }
        }
        return $list;
    }

    private function setRecordInfo(&$roomStatusRecord)
    {
        $tempBillId = $roomStatusRecord->bill_id;
        if ($tempBillId) {
            $tempBill = RoomBillService::getInstance()->getBillById($tempBillId);
            $billSource = BillSourceService::getInstance()->getBillSourceById($tempBill->bill_source);
            $billSourceName = $billSource->source_name;
            $linkMan = $tempBill->link_man;
            $linPhone = $tempBill->link_phone;
        } else {
            $billSourceName = null;
            $linkMan = null;
            $linPhone = null;
        }
        $roomStatusRecord->bill_source_name = $billSourceName;
        $roomStatusRecord->link_man = $linkMan;
        $roomStatusRecord->link_phone = $linPhone;
    }

    public function delRecordByBillId($billId)
    {
        $model = new RoomStatusRecordModel();
        return $model->delRecordByBillId($billId);
    }

    /**
     * 自助入住选择可选房间
     * @param $billId
     * @param $floorId
     * @param $buildingId
     * @return array
     * @throws MyException
     */
    public function wxSelfCheckInSelectRoomList($billId, $floorId, $buildingId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $roomTypeId = $bill->room_type_id;
        $roomSaleTypeId = $bill->sale_type_id;
        $roomTypeIds = [$roomTypeId];
        $shopId = $bill->shop_id;
        $startTime = time();
        $endTime = $bill->leave_time_plan;
        $roomId = $bill->room_id;
        if ($roomId) {
            return [];
        }
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $roomTypeCode = $roomType->room_type_code;
        if (empty($roomStatus)) {
            $roomStatus = [RoomClearStatus::CLEAR, RoomClearStatus::DIRTY, RoomClearStatus::CLEARING];
        }
        $selectRoomSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::SELF_SELECT_ROOM);
        $property = $selectRoomSetting->property;
        if ($property['status'] == 0) {
            DebugUtil::log("自助入住选择可选房间，请联系前台排房");
            throw new MyException(Code::DATA_ERROR, "请联系前台排房");
        }
        $selectRoomCount = $property['value'];
        $mustEmpty = false;
        $hasEmptyStatus = false;
        $newRecordStatus = [];
        if (empty($recordStatus)) {
            $hasEmptyStatus = true;
            $newRecordStatus = [RoomRecordStatus::CLOSED];
        } else {
            if (count($recordStatus) == 1 && in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $mustEmpty = true;
            }
            if (in_array(RoomRecordStatus::EMPTY_ROOM, $recordStatus)) {
                $hasEmptyStatus = true;
                $newRecordStatus = [];
                foreach ($recordStatus as $itemRecordStatus) {
                    if (RoomRecordStatus::EMPTY_ROOM != $itemRecordStatus) {
                        array_push($newRecordStatus, $itemRecordStatus);
                    }
                }
            } else {
                $newRecordStatus = $recordStatus;
            }
        }
        $newRoomList = [];
        $emptyStatus = RoomStatusService::getInstance()->getRoomStatusByIdFromCache($shopId, RoomRecordStatus::EMPTY_ROOM);
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        $sign = $thirdSystem->sign;
//        $thirdSystemId = $thirdSystem->id;
//        $otherSystem = false;
        if ($sign == ThirdSystem::JINGFEI) {
            $roomList = RoomService::getInstance()->getRoomList($shopId, $roomTypeIds, 1, $floorId, $buildingId, null, $roomStatus);
        } else {
            $res = RoomBill::getInstance()->getRoomList($shopId, $startTime, $endTime, $roomTypeCode);
            if (!$res) {
                $data = [];
            } else {
                $data = $res['data'];
            }
            $roomNumbers = [];
            foreach ($data as $datum) {
                $roomNumbers[] = $datum['room_number'];
            }
            $roomList = RoomService::getInstance()->getRoomByRoomNumberList($shopId, $roomNumbers);
        }
        //排序
        shuffle($roomList);
        foreach ($roomList as $itemRoom) {
            $tempBillId = 0;
            $roomId = $itemRoom->id;
            if ($sign == ThirdSystem::JINGFEI) {
                $clearStatus = $itemRoom->clean_status;
                if ($clearStatus != RoomClearStatus::CLEAR) {
                    continue;
                }
            }
            $stayingBill = RoomBillService::getInstance()->getStayingBillByRoomId($shopId, $roomId);
            if ($stayingBill) {
                $billEnterTime = $stayingBill->enter_time;
                $leaveTimePlan = time() + 60;
                if (TimeUtil::getInstance()->timeConflict($startTime, $endTime, $billEnterTime, $leaveTimePlan)) {
                    continue;
                }
            }
//			$tempRoomNumber = $itemRoom->room_number;
//			$tempToomTypeName = $itemRoom->room_type_name;
            //当前房态
            $futureRecordList = $this->getRoomRecordStatusListByRoomId($shopId, $roomId, $startTime, $endTime, []);
            if ($futureRecordList) {
                $currentRecord = array_shift($futureRecordList);
            } else {
                $currentRecord = null;
            }
            //只查询空房
            if ($mustEmpty && $currentRecord) {
                continue;
            }
            $start_time_plan = null;
            $end_time_plan = null;
            $item_room_status_record_id = RoomRecordStatus::EMPTY_ROOM;
            if (!$currentRecord) {
                if (!$hasEmptyStatus) {
                    continue;
                } else {
                    $itemRoom->room_status_name = $emptyStatus->status_name;
                    $itemRoom->room_status_record_id = RoomRecordStatus::EMPTY_ROOM;
                    $itemRoom->room_status_color = $emptyStatus->color;
                }
            } else {
                $tempStatusId = $currentRecord->status_id;
                if (!empty($newRecordStatus) && !in_array($tempStatusId, $newRecordStatus)) {
                    continue;
                }
                if ($currentRecord->bill_id) {
                    $currentRecord->bill_status = $tempStatusId;
                    $tempBillId = $currentRecord->bill_id;
                } else {
                    $itemRoom->link_man = null;
                }
                $itemRoom->room_status_name = $currentRecord->room_status_name;
                $item_room_status_record_id = $tempStatusId;
                $itemRoom->room_status_color = $currentRecord->color;
                $start_time_plan = $currentRecord->start_time_plan;
                $end_time_plan = $currentRecord->end_time_plan;
            }
            $itemRoom->room_status_record_id = $item_room_status_record_id;
            $clearColor = RoomClearStatusColorService::getInstance()->getRoomClearStatusColor($shopId, $itemRoom->clean_status);
            $itemRoom->clear_color = $clearColor->color;
            $itemRoom->clear_status_name = $clearColor->status_name;
            $itemRoom->bill_id = $tempBillId;
            $itemRoom->start_time_plan = $start_time_plan;
            $itemRoom->end_time_plan = $end_time_plan;
            $billInfo = null;
            $itemRoom->bill_info = $billInfo;
            array_push($newRoomList, $itemRoom);
            if (count($newRoomList) == $selectRoomCount) {
                break;
            }
        }
        if (empty($newRoomList)) {
            throw new MyException(Code::FAIL, "无空房，请联系前台");
        }
        return RoomService::getInstance()->groupByBuilding($newRoomList);
    }


}