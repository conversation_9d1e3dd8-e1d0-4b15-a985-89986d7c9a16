<?php


namespace app\common\model\room\team;

use app\common\utils\StringUtil;
use think\Model;

/**
 * Name:
 * Description:
 * Author: ShanGui
 * Date 2023-09-08
 */
class TeamRoomUser extends Model
{
    //团队入住人名单
    //id
    //team_id 团队id
    //name 姓名
    //phone 电话
    //identification_type 证件类型
    //identification_number 证件号
    //nation_id 民族id
    //gender 性别
    //create_time
    //update_time
    //pay_status 1已支付，0未支付
    //single 1包房，0合租
    //share 分享同住人 初始状态2，0不分享，1分享，-1拒绝分享
    //share_limit 分享同住人截止时间，超时间无效
    //authentication
    //accept 1能接受分享，0不能
    //team_confirm 默认-1 单人入住0 被分享人接受后变成1，分享人二次确认后被分享人变成2
    private $fields = ['id', 'team_id', 'name', 'phone', 'identification_type', 'identification_number', 'nation_id',
        'gender', 'create_time', 'update_time', 'bill_id', 'common_code', 'pay_status', 'single', 'share', 'share_limit',
        'share_code', 'user_image', 'identification_image', 'authentication', 'confirm', "room_price", "cash_pledge",
        'accept', 'prices', 'accnt', 'team_confirm'];
    private $sqlStr = 'a.id,
		a.team_id,
		a.bill_id,
		a.name,
		a.phone,
		a.identification_type,
		a.identification_number,
		a.nation_id,
		a.gender,
		a.create_time,
		a.update_time,
		a.common_code,
		a.pay_status,
		a.single,
		a.share,
		a.share_limit,
		a.share_code,
		a.identification_image,
		a.user_image,
		a.authentication,
		a.confirm,
		a.room_price,
		a.cash_pledge,
		a.accept,
		a.prices,
		a.accnt,
		a.team_confirm';
    protected $autoWriteTimestamp = true;
    protected $type = [
        'id' => 'integer',
        'team_id' => 'integer',
        'bill_id' => 'integer',
        'identification_type' => 'integer',
        'nation_id' => 'integer',
        'gender' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'pay_status' => 'integer',
        'single' => 'integer',
        'share' => 'integer',
        'share_limit' => 'integer',
        'authentication' => 'integer',
        'confirm' => 'integer',
        'accept' => 'integer',
        'team_confirm' => 'integer',
        'room_price' => 'float',
        'cash_pledge' => 'float',
        'prices' => 'array',
    ];


    public function getTeamRoomUserList($teamId, $single, $page, $limit)
    {
        $this->alias('a')->field($this->sqlStr . ',b.nation_name,c.name as identification_name')
            ->join('nation b', 'a.nation_id = b.id', 'left')
            ->join('identification_type c', 'a.identification_type = c.id', 'left')
            ->where('a.team_id', $teamId);
        if (!StringUtil::checkEmpty($single)) {
            $this->where('a.single', $single);
        }
        if ($page && $limit) {
            $this->page($page, $limit);
        }
        return $this->select();
    }

    public function getTeamRoomUserByBillId($teamId, $billId)
    {
        $this->alias('a')->field($this->sqlStr . ',b.nation_name,c.name as identification_name')
            ->join('nation b', 'a.nation_id = b.id', 'left')
            ->join('identification_type c', 'a.identification_type = c.id', 'left')
            ->where('a.team_id', $teamId)->where('a.bill_id', $billId);
        return $this->select();
    }

    public function getTeamRoomUserBillList($teamId, $single,$roomTypeId, $billStatus,$page, $limit)
    {
        $this->alias('a')->field($this->sqlStr . ',b.room_id,b.room_type_id,b.bill_status')
            ->join('hp_3_room_bill b', 'a.bill_id = b.id', 'left')
            ->where('a.team_id', $teamId);
        if (!StringUtil::checkEmpty($single)) {
            $this->where('a.single', $single);
        }
        if (!StringUtil::checkEmpty($roomTypeId)) {
            $this->where('b.room_type_id', $roomTypeId);
        }
        if (!StringUtil::checkEmpty($billStatus)) {
            $this->where('b.bill_status', $billStatus);
        }
        if ($page && $limit) {
            $this->page($page, $limit);
        }
        return $this->select();
    }
    public function getTeamRoomUserCount($teamId, $single)
    {
        $this->alias('a')->where('a.team_id', $teamId);
        if (!StringUtil::checkEmpty($single)) {
            $this->where('a.single', $single);
        }
        return $this->count();
    }


    public function getTeamRoomUserById($id)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.id', $id);
        return $this->find();
    }

    public function getTeamRoomUserByShareCode($shareCode)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.share_code', $shareCode);
        return $this->find();
    }

    public function getTeamRoomUserListByShareCode($shareCode)
    {
        $this->alias('a')->field($this->sqlStr . ',b.user_status as user_stay_status,b.is_main')
            ->join("room_bill_user b", 'a.common_code = b.common_code and a.bill_id = b.bill_id', 'left')
            ->where('a.share_code', $shareCode);
        return $this->select();
    }

    public function getUserByIdentificationNumber($teamId, $billId, $identificationNumber)
    {
        $this->alias('a')->field($this->sqlStr)
            ->where('a.team_id', $teamId)
            ->where('a.identification_number', $identificationNumber);
        if (!StringUtil::checkEmpty($billId)) {
            $this->where('a.bill_id', $billId);
        }
        return $this->find();
    }

    public function getUserByName($teamId, $billId, $name)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.team_id', $teamId)->where('a.name', $name);
        if (!StringUtil::checkEmpty($billId)) {
            $this->where('a.bill_id', $billId);
        }
        return $this->find();
    }

    public function getRoomUserByCommonCode($teamId, $commonCode)
    {
        $this->alias('a')->field($this->sqlStr . ',b.user_status as user_stay_status')
            ->join("room_bill_user b", 'a.common_code = b.common_code and a.bill_id = b.bill_id', 'left')
            ->where('a.team_id', $teamId)
            ->where('a.common_code', $commonCode);
        return $this->find();
    }

    public function getTeamAllUserByAccnt($teamId, $accnt)
    {
        return $this->alias('a')->field($this->sqlStr)
            ->where('a.team_id', $teamId)->where('a.accnt', $accnt)->find();
    }

    public function getTeamUserByAccnt($accnt)
    {
        return $this->alias('a')->field($this->sqlStr)->where('a.accnt', $accnt)->find();
    }

    public function getTeamUserCountByShareCode($shareCode)
    {
        return $this->alias('a')->field($this->sqlStr . ',b.user_status as user_stay_status')
            ->join("room_bill_user b", 'a.common_code = b.common_code and a.bill_id = b.bill_id', 'left')
            ->where('a.share_code', $shareCode)->count();
    }

    public function getRoomUserByBillId($billId)
    {
        $this->alias('a')->field($this->sqlStr . ',b.nation_name,c.name as identification_name')
            ->join('nation b', 'a.nation_id = b.id', 'left')
            ->join('identification_type c', 'a.identification_type = c.id', 'left')
            ->where('a.bill_id', $billId);
        return $this->select();
    }
    public function getRoomUserListByBillId($billId)
    {
        $this->alias('a')->field($this->sqlStr.',c.room_number,b.bill_code')
            ->join('room_bill b', 'a.bill_id = b.id', 'left')
            ->join('room c', 'b.room_id = c.id', 'left')
            ->where('a.bill_id', $billId);
        return $this->select();
    }

    public function getRoomUserByPhone($teamId, $phone, $billId)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.team_id', $teamId)->where('a.phone', $phone);
        if (!StringUtil::checkEmpty($billId)) {
            $this->where('a.bill_id', $billId);
        }
        return $this->find();
    }

    public function getRoomUserByName($teamId, $name, $billId)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.team_id', $teamId)->where('a.name', $name);
        if (!StringUtil::checkEmpty($billId)) {
            $this->where('a.bill_id', $billId);
        }
        return $this->find();
    }

    public function getRoomUserListByName($teamId, $name)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.team_id', $teamId)->where('a.name', $name);
        return $this->select();
    }

    public function getOverTimeUser($shopId)
    {
        return $this->alias('a')->field($this->sqlStr)
            ->join('team_room b', 'a.team_id = b.id')
            ->where('b.shop_id', $shopId)
            ->where('a.share', ">", 0)
            ->where('a.team_confirm', -1)
            ->where('a.share_limit', '<', time())
            ->where('a.share_limit', '<>', 0)
            ->select();
    }


    public function delTeamRoomUserById($id)
    {
        return $this->where('id', $id)->delete();
    }


    public function saveTeamRoomUser($params)
    {
        return $this->allowField($this->fields)
            ->save($params);
    }


    public function editTeamRoomUser($params)
    {
        return $this->allowField($this->fields)
            ->save($params, ['id' => $params['id']]);
    }

}