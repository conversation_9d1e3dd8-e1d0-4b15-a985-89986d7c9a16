<?php


namespace app\common\service\room;

use app\common\model\room\RoomType as RoomTypeModel;
use app\common\returnConfig\Code;
use app\common\returnConfig\Msg;
use app\common\service\common\AdminLogService;
use app\common\service\intermediary\IntermediaryRoomPriceService;
use app\common\service\shop\ShopService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\user\MemberGradeService;
use app\common\service\user\MemberUtilService;
use app\common\service\user\UserWxService;
use app\common\sysConfig\MyException;
use app\common\utils\ArrayUtils;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\AdminLogType;
use app\common\utils\dataDictionary\CacheTag;
use app\common\utils\dataDictionary\LogLevel;
use app\common\utils\dataDictionary\RoomSaleType;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;
use think\Exception;


class RoomTypeService
{

    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getRoomTypeList($shopId, $status, $roomCount, $page, $limit)
    {
        $model = new RoomTypeModel();
        $list = $model->getRoomTypeList($shopId, $status, $roomCount, $page, $limit);
        if ($page && $limit) {
            $count = $model->getRoomTypeCount($shopId, $status, $roomCount);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function getRoomTypeListFromCache($shopId, $status, $roomCount, $page, $limit)
    {
        $cacheKey = $shopId . '_' . $status . '_' . $roomCount . '_' . $page . '_' . $limit;
        if (CacheManager::getCacheValue($cacheKey, CacheTag::ROOM_PRICE)) {
            return CacheManager::getCacheValue($cacheKey, CacheTag::ROOM_PRICE, $shopId);
        } else {
            $list = $this->getRoomTypeList($shopId, $status, $roomCount, $page, $limit);
            if ((isset($list['count']) && $list['count'] > 0) || (!isset($list['list']) && $list)) {
                CacheManager::setCacheValue($cacheKey, $list, CacheTag::ROOM_PRICE, $shopId, 60);
            }
            return $list;
        }

    }

    public function getRoomTypeListByIds($shopId, $status, $ids)
    {
        $model = new RoomTypeModel();
        return $model->getRoomTypeListByIds($shopId, $status, $ids);
    }


    public function getRoomTypeByName($shopId, $name)
    {
        $model = new RoomTypeModel();
        return $model->getRoomTypeByName($shopId, $name);
    }

    public function getRoomTypeByCode($shopId, $code)
    {
        $model = new RoomTypeModel();
        return $model->getRoomTypeByCode($shopId, $code);
    }

    public function getRoomTypeById($id)
    {
        $model = new RoomTypeModel();
        return $model->getRoomTypeById($id);
    }


    public function editRoomType($params)
    {
        $model = new RoomTypeModel();
        if (isset($params['id'])) {
            $model->editRoomType($params);
            return $params['id'];
        } else {
            $model->saveRoomType($params);
            return $model->id;
        }
    }

    /**
     * 编辑房型
     * @param $shopIdZong
     * @param $shopId
     * @param $params
     * @throws MyException
     */
    public function updateRoomType($shopIdZong, $shopId, $params)
    {
        $oldRoomType = $this->getRoomTypeByName($shopId, $params['name']);
        if (isset($params['id'])) {
            if ($oldRoomType && $oldRoomType->id != $params['id']) {
                throw new MyException(Code::FAIL, Msg::nameExited());
            }
        } else {
            $typeCode = StringUtil::getRandomStrs(10);
            $params['room_type_code'] = $typeCode;
            if ($oldRoomType) {
                throw new MyException(Code::FAIL, '房型名称重复');
            }
        }
        $id = $this->editRoomType($params);
        $roomCount = RoomService::getInstance()->getRoomCount($shopId, $id, 1, '', '', '', '');
        $this->editRoomType(['id' => $id, 'room_count' => $roomCount]);
        RoomPriceService::getInstance()->checkRoomPrice($shopIdZong, $shopId);
        IntermediaryRoomPriceService::getInstance()->checkRoomPrice($shopId);
        $manipulation = '编辑房型';
        AdminLogService::getInstance()->saveAdminLog(LogLevel::EXCEPTION, $manipulation, AdminLogType::ROOM_TYPE);
    }


    public function updateRoomTypeSort($shopIdZong, $shopId, $id, $sort)
    {
        $manipulation = '编辑房型 排序 ' . $sort;
        AdminLogService::getInstance()->saveAdminLog(LogLevel::EXCEPTION, $manipulation, AdminLogType::ROOM_TYPE);
        $data = ['id' => $id, 'sort' => $sort];
        $this->editRoomType($data);
        RoomPriceService::getInstance()->checkRoomPrice($shopIdZong, $shopId);
        IntermediaryRoomPriceService::getInstance()->checkRoomPrice($shopId);
    }

    public function updateRoomTypeStatus($shopIdZong, $shopId, $id, $status)
    {
        $manipulation = '编辑房型 状态：' . ($status == 0 ? "下架" : "上架");
        AdminLogService::getInstance()->saveAdminLog(LogLevel::EXCEPTION, $manipulation, AdminLogType::ROOM_TYPE);
        $data = ['id' => $id, 'status' => $status];
        $this->editRoomType($data);
        RoomPriceService::getInstance()->checkRoomPrice($shopIdZong, $shopId);
        IntermediaryRoomPriceService::getInstance()->checkRoomPrice($shopId);
        CacheManager::clearAllCacheValue($shopId, CacheTag::ROOM_PRICE);
    }

    /**
     * 小程序获取房型
     * @param $shopIdZong
     * @param $shopId
     * @param $userId
     * @param $sellTypeId
     * @param $startTime
     * @param $endTime
     * @return array
     * @throws MyException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function wxGetRoomTypes($shopIdZong, $shopId, $userId, $sellTypeId, $startTime, $endTime)
    {
        $warnCountSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ROOM_NUMBER_WARN);
        $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
        $settingEnterHour = $enterTimeSetting->property['value'];
        $todayDate = date('Y-m-d', time());
        $date = date('Y-m-d', $startTime);
        //不是当天的时间，小程序默认传日期的 0点0分
        if ($date != $todayDate) {
            $startTime = strtotime("$date 08:00:00");
        }
        $settingEnterHourTime = strtotime("$date $settingEnterHour");
        $warnCount = $warnCountSetting->property['value'];
        $priceDate = $date;
        if ($todayDate == $date) {
            //如果预定的是当天的
            if (time() < $settingEnterHourTime) {
                //早上六点前使用前一天的价格
                $priceDate = TimeUtil::getInstance()->getDate($date, -1);
            }
        }
        $week = TimeUtil::getInstance()->getWeek($priceDate);
        $lowestGrade = MemberGradeService::getInstance()->getLowestGrade($shopIdZong);
        $baseGradeId = $lowestGrade->id;
        $memberGradeId = MemberUtilService::getInstance()->getUserGrade($userId);
        $roomSaleTypes = RoomSaleTypeService::getInstance()->getRoomSaleTypeList($shopId, 1, $sellTypeId, 1, '', '');
        $roomTypeDataList = $this->getRoomTypeList($shopId, 1, 1, null, null);
        $roomTypeList = [];
        $roomPrices = RoomPriceUtil::getInstance()->appGetRoomPriceList($shopId, $memberGradeId, 1, $priceDate);
        foreach ($roomSaleTypes as $roomSaleType) {
            $roomSaleTypeId = $roomSaleType->id;
            $baseRoomPrices = RoomPriceService::getInstance()->getRoomPriceByDateList($shopId, $shopIdZong, $roomSaleTypeId, $baseGradeId, $priceDate, $week);
            $startTimeLimit = $roomSaleType->start_time_limit;
            $endTimeLimit = $roomSaleType->end_time_limit;
            $stayTime = $roomSaleType->stay_time;
//			$roomPrices = RoomPriceUtil::getInstance()->appGetRoomPrice($shopId, $roomSaleTypeId, $memberGradeId, 1, $priceDate);
            foreach ($roomPrices as $price) {
                if ($price->sale_type_id != $roomSaleTypeId) continue;
                $roomTypeId = $price->room_type_id;
                foreach ($baseRoomPrices as $itemBaseRoomPrice) {
                    if ($itemBaseRoomPrice->room_type_id == $roomTypeId) {
                        $baseRoomPriceModel = $itemBaseRoomPrice;
                        break;
                    }
                }
                if (empty($baseRoomPriceModel)) continue;
                $priceId = $price->id;
                $baseRoomPrice = $price->price;
                //最高价格
                $peakPrice = RoomPriceService::getInstance()->getPeakPrice($shopId, $roomSaleTypeId, $roomTypeId, $memberGradeId);
                //最低价
                $lowestPrice = RoomPriceService::getInstance()->getLowestPrice($shopId, $roomSaleTypeId, $roomTypeId, $memberGradeId);
                $roomServices = RoomPriceServiceService::getInstance()->getRoomPriceServiceList($priceId, 1, $shopId, 1);
                $roomType = null;
                foreach ($roomTypeDataList as $itemRoomTypeData) {
                    if ($itemRoomTypeData->id == $roomTypeId) {
                        $roomType = $itemRoomTypeData;
                    }
                }
                if (empty($roomType)) {
                    continue;
                }
                if (!empty($roomServices)) {
                    $tempLowestPrice = 0;
                    foreach ($roomServices as $roomService) {
                        $tempServicePrice = $roomService->price;
                        $roomService->price = $tempServicePrice + $baseRoomPrice;
                        if ($tempLowestPrice > $tempServicePrice) {
                            $tempLowestPrice = $tempServicePrice;
                        }
                    }
                    $room_price = $baseRoomPrice + $tempLowestPrice;
                } else {
                    $room_price = $baseRoomPrice;
                }
                $usableCount = RoomBookCountService::getInstance()->wxGetRoomCount($shopId, $sellTypeId, $roomSaleTypeId, $roomTypeId, $memberGradeId, $startTime, $endTime);
                if (!empty($baseRoomPrice)) {
                    $basePrice = $baseRoomPriceModel->price;
                } else {
                    $basePrice = $room_price;
                }
                $roomType->discounts_price = $basePrice - $baseRoomPrice;
                $roomType->price = $room_price;
                $roomType->services = $roomServices;
                $roomType->usable_count = $usableCount;
                $roomType->warn_count = $warnCount;
                $roomType->peak_price = $peakPrice;
                $roomType->lowest_price = $lowestPrice;
                $roomType->room_sale_type_name = $roomSaleType->name;
                $roomType->room_sale_type_id = $roomSaleType->id;
                $roomType->start_time_limit = $startTimeLimit;
                $roomType->end_time_limit = $endTimeLimit;
                $roomType->stay_time = $stayTime;
                $roomType->advance_time = $roomSaleType->advance_time;
                $roomType->price_id = $price->id;
                array_push($roomTypeList, $roomType);
            }
        }
        ArrayUtils::getInstance()->arraySort($roomTypeList, "sort", 'desc');
        return $roomTypeList;
    }


    public function wxGetRoomTypesNew($shopIdZong, $shopId, $userId, $sellTypeId, $startTime, $endTime)
    {
        $roomTypeDataList = $this->getRoomTypeList($shopId, 1, 1, null, null);
        $warnCountSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ROOM_NUMBER_WARN);
        $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
        $settingEnterHour = $enterTimeSetting->property['value'];
        $todayDate = date('Y-m-d', time());
        $date = date('Y-m-d', $startTime);
        //不是当天的时间，小程序默认传日期的 0点0分
        if ($date != $todayDate) {
            $startTime = strtotime("$date 08:00:00");
        }
        $settingEnterHourTime = strtotime("$date $settingEnterHour");
        $warnCount = $warnCountSetting->property['value'];
        $priceDate = $date;
        if ($todayDate == $date) {
            //如果预定的是当天的
            if (time() < $settingEnterHourTime) {
                //早上六点前使用前一天的价格
                $priceDate = TimeUtil::getInstance()->getDate($date, -1);
            }
        }
//		$week = TimeUtil::getInstance()->getWeek($priceDate);
        $memberGradeId = MemberUtilService::getInstance()->getUserGrade($userId);
        $key = md5("wxGetRoomTypesNew $shopId, $memberGradeId, $sellTypeId, $priceDate,$endTime");
        $endDate = date('Y-m-d', $endTime);
        $startDate = date('Y-m-d', $startTime);
//        DebugUtil::log("endDate:$endDate");
        if (strtotime($endDate) - strtotime($startDate) == 24 * 60 * 60) {
            $endDate = $startDate;
        } else {
            $endDate = TimeUtil::getInstance()->getDate($endDate, -1);
        }
//        DebugUtil::log("$startDate,$endDate");
        $endTime = strtotime($endDate);
//		CacheManager::clearCacheValue($key,CacheTag::ROOM_PRICE,$shopId);
        if (CacheManager::has($key, CacheTag::ROOM_PRICE, $shopId)) {
            return CacheManager::getCacheValue($key, CacheTag::ROOM_PRICE, $shopId);
        } else {
            $roomSaleTypes = RoomSaleTypeService::getInstance()->getRoomSaleTypeList($shopId, 1, $sellTypeId, 1, '', '');

            $newRoomTypeList = [];
            $roomPrices = RoomPriceUtil::getInstance()->appGetRoomPriceList($shopId, $memberGradeId, 1, $priceDate);
            foreach ($roomTypeDataList as $itemRoomType) {
                $roomTypeId = $itemRoomType->id;
                foreach ($roomSaleTypes as $roomSaleType) {
                    $tempRoomPrice = null;
                    $roomSaleTypeId = $roomSaleType->id;
                    $startTimeLimit = $roomSaleType->start_time_limit;
                    $endTimeLimit = $roomSaleType->end_time_limit;
                    $stayTime = $roomSaleType->stay_time;
                    foreach ($roomPrices as $itemRoomPrice) {
                        if ($roomTypeId == $itemRoomPrice->room_type_id && $roomSaleTypeId == $itemRoomPrice->sale_type_id) {
                            $tempRoomPrice = $itemRoomPrice;
                            break;
                        }
                    }
                    if (empty($tempRoomPrice)) continue;
                    $priceId = $tempRoomPrice->id;
                    $baseRoomPrice = $tempRoomPrice->price;
                    //最高价格
                    $peakPrice = RoomPriceService::getInstance()->getPeakPrice($shopId, $roomSaleTypeId, $roomTypeId, $memberGradeId);
                    $lowestPrice = RoomPriceService::getInstance()->getLowestPrice($shopId, $roomSaleTypeId, $roomTypeId, $memberGradeId);
                    //最低价
                    $roomServices = RoomPriceServiceService::getInstance()->getRoomPriceServiceList($priceId, 1, $shopId, 1);
                    if (!empty($roomServices)) {
                        $tempLowestPrice = 0;
                        foreach ($roomServices as $roomService) {
                            $tempServicePrice = $roomService->price;
                            $roomService->price = $tempServicePrice + $baseRoomPrice;
                            if ($tempLowestPrice > $tempServicePrice) {
                                $tempLowestPrice = $tempServicePrice;
                            }
                        }
                        $room_price = $baseRoomPrice + $tempLowestPrice;
                    } else {
                        $room_price = $baseRoomPrice;
                    }
                    $usableCount = RoomBookCountService::getInstance()->wxGetRoomCount($shopId, $sellTypeId, $roomSaleTypeId, $roomTypeId, $memberGradeId, $startTime, $endTime);
//                    DebugUtil::log("usableCount $roomTypeId:$usableCount");
                    $basePrice = $room_price;
                    $itemRoomType->discounts_price = $basePrice - $baseRoomPrice;
                    $itemRoomType->price = $room_price;
                    $itemRoomType->services = $roomServices;
                    $itemRoomType->usable_count = $usableCount;
                    $itemRoomType->warn_count = $warnCount;
                    $itemRoomType->peak_price = $peakPrice;
                    $itemRoomType->lowest_price = $lowestPrice;
                    $itemRoomType->room_sale_type_name = $roomSaleType->name;
                    $itemRoomType->room_sale_type_id = $roomSaleType->id;
                    $itemRoomType->start_time_limit = $startTimeLimit;
                    $itemRoomType->end_time_limit = $endTimeLimit;
                    $itemRoomType->stay_time = $stayTime;
                    $itemRoomType->advance_time = $roomSaleType->advance_time;
                    $itemRoomType->price_id = $tempRoomPrice->id;
                    $newRoomTypeList[] = $itemRoomType;
                }
            }
            CacheManager::setCacheValue($key, $newRoomTypeList, CacheTag::ROOM_PRICE, $shopId, 600);
            return $newRoomTypeList;
        }
    }


    public function gerRoomTypeByFloor($shopId, $floorId)
    {
        $rooms = RoomService::getInstance()->getRoomList($shopId, '', 1, $floorId, '', '', '');
        return $this->groupByRoomType($rooms);
    }

    /**
     * 根据房型分组
     * @param $roomList
     * @return array
     */
    private function groupByRoomType($roomList)
    {
        $newList = [];
        foreach ($roomList as $item) {
            if (count($newList) == 0) {
                array_push($newList, ['room_type_name' => $item->room_type_name, 'count' => 1, 'id' => $item->room_type_id]);
            } else {
                $flag = false;
                foreach ($newList as $key => $value) {
                    if (isset($value['room_type_name']) && $value['room_type_name'] == $item->room_type_name) {
                        $newList[$key]['count']++;
                        $flag = true;
                    }
                }
                if (!$flag) {
                    array_push($newList, ['room_type_name' => $item->room_type_name, 'count' => 1, 'id' => $item->room_type_id]);
                }
            }
        }
        return $newList;
    }

    /**
     * 查询可用房型
     * @param $shopIdZong
     * @param $shopId
     * @param $roomSaleTypeId
     * @param $memberGrade
     * @param $intermediaries_id
     * @return array
     */
    public function getUsableRoomTypeIds($shopIdZong, $shopId, $roomSaleTypeId, $memberGrade, $intermediaries_id)
    {
        $key = "$shopIdZong,$shopId,$roomSaleTypeId,$memberGrade,$intermediaries_id";
        if (CacheManager::has($key, CacheTag::ROOM_PRICE, $shopId)) {
            return CacheManager::getCacheValue($key, CacheTag::ROOM_PRICE, $shopId);
        } else {
            if (!StringUtil::checkEmpty($memberGrade)) {
                $roomTypeIds = RoomPriceService::getInstance()->getRoomTypeIds($shopId, $shopIdZong, $roomSaleTypeId, $memberGrade);
            } elseif (!StringUtil::checkEmpty($intermediaries_id)) {
                $roomTypeIds = IntermediaryRoomPriceService::getInstance()->getRoomTypeIds($shopId, $roomSaleTypeId, $intermediaries_id);
            } else {
                $roomTypeIds = RoomPriceService::getInstance()->getRoomTypeIds($shopId, $shopIdZong, $roomSaleTypeId, $memberGrade);
                $roomTypeIds2 = IntermediaryRoomPriceService::getInstance()->getRoomTypeIds($shopId, $roomSaleTypeId, $intermediaries_id);
                $roomTypeIds = array_unique(array_merge($roomTypeIds, $roomTypeIds2));
            }
            CacheManager::setCacheValue($key, $roomTypeIds, CacheTag::ROOM_PRICE, $shopId, 300);
        }
        return $roomTypeIds;
    }

    public function getRoomTypeByIds($ids)
    {
        $model = new RoomTypeModel();
        return $model->getRoomTypeByIds($ids);
    }

    public function updateRoomCount($shopId, $id)
    {
        $roomCount = RoomService::getInstance()->getRoomCount($shopId, $id, 1, '', '', '', '');
        $this->editRoomType(['id' => $id, 'room_count' => $roomCount]);
    }


}