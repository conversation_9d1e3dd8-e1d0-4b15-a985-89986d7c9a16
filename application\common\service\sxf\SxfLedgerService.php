<?php

namespace app\common\service\sxf;

use app\common\model\sxf\SxfLedger as SxfLedgerModel;
use app\common\returnConfig\Code;
use app\common\service\Pay\sxf\SxfRequestUtil;
use app\common\service\resource\ResourceService;
use app\common\sysConfig\MyException;
use app\common\utils\DebugUtil;

class SxfLedgerService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
//判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
//返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getSxfLedgerList($mainMno, $childMno, $status, $page, $limit)
    {
        $model = new SxfLedgerModel();
        $list = $model->getSxfLedgerList($mainMno, $childMno, $status, $page, $limit);
        if ($page && $limit) {
            $count = $model->getSxfLedgerCount($mainMno, $childMno, $status);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function getSxfLedger($mainMno, $childMno, $status)
    {
        $model = new SxfLedgerModel();
        return $model->getSxfLedger($mainMno, $childMno, $status);
    }

    public function getSxfLedgerById($id)
    {
        $model = new SxfLedgerModel();
        return $model->getSxfLedgerById($id);

    }

    public function getSxfLedgerByApplyId($applyId)
    {
        $model = new SxfLedgerModel();
        return $model->getSxfLedgerByApplyId($applyId);

    }

    public function delSxfLedgerById($id)
    {
        $model = new SxfLedgerModel();
        return $model->delSxfLedgerById($id);

    }

    public function addSxfLedger($mainMno, $childMno, $status)
    {
        $data = ['main_mno' => $mainMno, 'child_mno' => $childMno, 'status' => $status];
        $this->editSxfLedger($data);
    }

    /**
     * @param $customerId
     * @param $mno
     * @param $applicationType
     * @param $accountRatio
     * @param $splitAccounts
     * @param $agreementPicStr
     * @param $scenesPicStr
     * @param $ledgerProtocolPicStr
     * @param $otherPicStr
     * @param $remark
     * @return mixed
     * @throws MyException
     */
    public function mnoCommitApply($customerId, $mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
                                   $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $record = $this->getSxfLedger($mno, $splitAccounts['mno'], null);
        if (empty($record)) {
            $this->addSxfLedger($mno, $splitAccounts['mno'], 0);
        } else {
            $recordStatus = $record->result;
            if ($recordStatus == '00' || $recordStatus == '01') {
                throw new MyException(Code::FAIL, '该商户已提交过申请');
            }
        }
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->mnoCommitApply($mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
            $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark);
        if ($res['bizCode'] != '0000') {
            throw new MyException(Code::FAIL, $res['bizMsg']);
        }
        $applyId = $res['id'];
        $resultStatus = $res['applyStatus'];
//        $applyId = "TQAICM000001176148633";
//        $resultStatus = "00";
        $this->addSxfLedgerApply($mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
            $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark, $applyId, $resultStatus);
        return $this->getSxfLedger($mno, $splitAccounts['mno'], null);
    }

    public function querySpecialApplicationApplyInfo($customerId, $applyId)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->querySpecialApplicationApplyInfo($applyId);
        if ($res['bizCode'] == '0000') {
            $record = $this->getSxfLedgerByApplyId($applyId);
            $this->editSxfLedger([
                'id' => $record->id,
                'result' => $res['applyStatus'],
            ]);
            return $this->getSxfLedgerByApplyId($applyId);
        }
        throw new MyException(Code::FAIL, $res['msg']);
    }

    /**
     * 申请退回
     * @param $customerId
     * @param $applyId
     * @return void
     * @throws MyException
     */
    public function specialApplicationBackApplyBill($customerId, $applyId)
    {
        $config = SxfConfigService::getInstance()->getSxfConfigInfoByCustomerId($customerId);
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $res = $sxf->specialApplicationBackApplyBill($applyId);
        if ($res['respData']['bizCode'] == '0000') {
            $record = $this->getSxfLedgerByApplyId($applyId);
            $this->delSxfLedgerById($record->id);
        } else {
            throw new MyException(Code::FAIL, $res['msg']);
        }
    }

    public function addSxfLedgerApply($mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
                                      $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark, $applyId, $result)
    {
        $apply = $this->getSxfLedger($mno, $splitAccounts['mno'], null);
        $scenesPicStrList = empty($scenesPicStr) ? [] : explode(',', $scenesPicStr);
        $agreementPicStrList = empty($agreementPicStr) ? [] : explode(',', $agreementPicStr);
        $ledgerProtocolPicStrList = empty($ledgerProtocolPicStr) ? [] : explode(',', $ledgerProtocolPicStr);
        $otherPicStrList = empty($otherPicStr) ? [] : explode(',', $otherPicStr);
        $scenesPicList = [];
        $ledgerProtocolPicList = [];
        $agreementPicList = [];
        $otherPicList = [];
        foreach ($scenesPicStrList as $picStr) {
            $resource = ResourceService::getInstance()->getResourceByName($picStr);
            if ($resource) {
                $scenesPicList[] = $resource['url'];
            }
        }
        foreach ($agreementPicStrList as $picStr) {
            $resource = ResourceService::getInstance()->getResourceByName($picStr);
            if ($resource) {
                $ledgerProtocolPicList[] = $resource['url'];
            }
        }
        foreach ($ledgerProtocolPicStrList as $picStr) {
            $resource = ResourceService::getInstance()->getResourceByName($picStr);
            if ($resource) {
                $agreementPicList[] = $resource['url'];
            }
        }
        foreach ($otherPicStrList as $picStr) {
            $resource = ResourceService::getInstance()->getResourceByName($picStr);
            if ($resource) {
                $otherPicList[] = $resource['url'];
            }
        }
        $this->editSxfLedger([
            'id' => $apply->id,
            'status' => 1,
            'application_type' => $applicationType,
            'rate' => $accountRatio,
            'split_cycle' => $splitAccounts['split_cycle'],
            'relation_ship' => $splitAccounts['relation_ship'],
            'scenes' => $splitAccounts['scenes'],
            'agreement_pic' => $ledgerProtocolPicList,
            'scenes_pic' => $scenesPicList,
            'ledger_protocol_pic' => $agreementPicList,
            'other_pic' => $otherPicList,
            'remark' => $remark,
            'apply_id' => $applyId,
            "result" => $result
        ]);
    }

    public function editSxfLedger($params)
    {
        DebugUtil::log($params);
        $model = new SxfLedgerModel();
        if (isset($params['id'])) {
            $model->editSxfLedger($params);
            $id = $params['id'];
        } else {
            $model->saveSxfLedger($params);
            $id = $model->id;
        }
        return $id;
    }
}