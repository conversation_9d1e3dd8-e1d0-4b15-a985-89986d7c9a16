<?php


namespace app\admin\controller;


use app\common\filter\PController;
use app\common\returnConfig\Code;
use app\common\returnConfig\Msg;
use app\common\returnConfig\ResultData;
use app\common\service\room\RoomBillConnectService;
use app\common\service\room\RoomBillDetailService;
use app\common\service\room\RoomBillDetailTypeGroupService;
use app\common\service\room\RoomBillDetailTypeService;
use app\common\service\room\RoomBillLogService;
use app\common\service\room\RoomBillQrService;
use app\common\service\room\RoomBillService;
use app\common\service\room\RoomBillUserService;
use app\common\service\room\RoomBillUtil;
use app\common\service\room\RoomFundUtil;
use app\common\service\user\MemberAndRightInterestService;
use app\common\sysConfig\MyException;
use app\common\utils\StringUtil;
use think\Db;
use think\Exception;
use think\Request;
use app\common\validate\RoomBillDetailType as RoomBillDetailTypeValidate;

class RoomBill extends PController
{


    /**
     * 查询订单
     * @param Request $request
     */
    public function getRoomBillList(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $page = $request->param('page');
        $limit = $request->param('limit');
        $linkName = $request->param('link_man');
        $linkPhone = $request->param('link_phone');
        $billStatus = $request->param('bill_status');
        $billSource = $request->param('bill_source');
        $enterTimePlan = $request->param('enter_time_plan/a');
        $enterTime = $request->param('enter_time/a');
        $leaveTimePlan = $request->param('leave_time_plan/a');
        $leaveTime = $request->param('leave_time/a');
        $memo = $request->param('memo', '');
        $roomUser = $request->param('room_user_name', '');
        $intermediary_id = $request->param('intermediary_id', '');
        $billCode = $request->param('bill_code');
        $roomSaleType = $request->param('room_sale_type');
        $roomNumber = $request->param('room_number', '');
        $arranged = $request->param('arranged', '');
        $commonCode = $request->param('common_code');
        $adminId = $request->param('admin_id');
        $stayType = $request->param('stay_type');
        $roomTypeId = $request->param('room_type_id');
        $booking_type = $request->param('booking_type');
        $priceProject = $request->param('price_project');
        $searchWord = [];
        if ($enterTimePlan) {
            $searchWord['enter_time_plan'] = $enterTimePlan;
        }
        if ($enterTime) {
            $searchWord['enter_time'] = $enterTime;
        }
        if ($leaveTimePlan) {
            $searchWord['leave_time_plan'] = $leaveTimePlan;
        }
        if ($leaveTime) {
            $searchWord['leave_time'] = $leaveTime;
        }
        if ($memo) {
            $searchWord['memo'] = $memo;
        }
        if ($roomUser) {
            $searchWord['room_user_name'] = $roomUser;
        }
        if ($intermediary_id) {
            $searchWord['intermediary_id'] = $intermediary_id;
        }
        if ($adminId) {
            $searchWord['admin_id'] = $adminId;
        }
        if ($stayType) {
            $searchWord['stay_type'] = $stayType;
        }
        if ($roomTypeId) {
            $searchWord['room_type_id'] = $roomTypeId;
        }
        if (!StringUtil::checkEmpty($arranged)) {
            $searchWord['arranged'] = $arranged;
        }
        if (!StringUtil::checkEmpty($booking_type)) {
            $searchWord['booking_type'] = $booking_type;
        }
        if (!StringUtil::checkEmpty($priceProject)) {
            $searchWord['price_project'] = $priceProject;
        }
        $res = RoomBillService::getInstance()->adminGetRoomBillList($shopIdZong, $shopId, $billStatus, $linkName, $linkPhone, $billCode,
            $billSource, $commonCode, $roomSaleType, $roomNumber, $searchWord, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 查询订单
     * @param Request $request
     */
    public function adminGetBookingRoomBillList(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $page = $request->param('page');
        $limit = $request->param('limit');
        $linkName = $request->param('link_man');
        $linkPhone = $request->param('link_phone');
        $billStatus = $request->param('bill_status');
        $billSource = $request->param('bill_source');
        $enterTimePlan = $request->param('enter_time_plan/a');
        $enterTime = $request->param('enter_time/a');
        $leaveTimePlan = $request->param('leave_time_plan/a');
        $leaveTime = $request->param('leave_time/a');
        $memo = $request->param('memo', '');
        $roomUser = $request->param('room_user_name', '');
        $intermediary_id = $request->param('intermediary_id', '');
        $billCode = $request->param('bill_code');
        $roomSaleType = $request->param('room_sale_type');
        $roomNumber = $request->param('room_number', '');
        $arranged = $request->param('arranged', '');
        $commonCode = $request->param('common_code');
        $adminId = $request->param('admin_id');
        $stayType = $request->param('stay_type');
        $roomTypeId = $request->param('room_type_id');
        $type = $request->param('type');
        $priceProject = $request->param('price_project');
        $searchWord = [];
        if ($enterTimePlan) {
            $searchWord['enter_time_plan'] = $enterTimePlan;
        }
        if ($enterTime) {
            $searchWord['enter_time'] = $enterTime;
        }
        if ($leaveTimePlan) {
            $searchWord['leave_time_plan'] = $leaveTimePlan;
        }
        if ($leaveTime) {
            $searchWord['leave_time'] = $leaveTime;
        }
        if ($memo) {
            $searchWord['memo'] = $memo;
        }
        if ($roomUser) {
            $searchWord['room_user_name'] = $roomUser;
        }
        if ($intermediary_id) {
            $searchWord['intermediary_id'] = $intermediary_id;
        }
        if ($adminId) {
            $searchWord['admin_id'] = $adminId;
        }
        if ($stayType) {
            $searchWord['stay_type'] = $stayType;
        }
        if ($roomTypeId) {
            $searchWord['room_type_id'] = $roomTypeId;
        }
        if (!StringUtil::checkEmpty($arranged)) {
            $searchWord['arranged'] = $arranged;
        }
        if (!StringUtil::checkEmpty($priceProject)) {
            $searchWord['price_project'] = $priceProject;
        }
        $res = RoomBillService::getInstance()->adminGetBookingRoomBillList($shopId, $linkName, $linkPhone, $billCode,
            $billSource, $commonCode, $roomSaleType, $roomNumber, $searchWord, $type, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 查询订单
     * @param Request $request
     */
    public function adminGetStayingRoomBillList(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id'];
        $page = $request->param('page');
        $limit = $request->param('limit');
        $linkName = $request->param('link_man');
        $linkPhone = $request->param('link_phone');
        $billStatus = $request->param('bill_status');
        $billSource = $request->param('bill_source');
        $enterTimePlan = $request->param('enter_time_plan/a');
        $enterTime = $request->param('enter_time/a');
        $leaveTimePlan = $request->param('leave_time_plan/a');
        $leaveTime = $request->param('leave_time/a');
        $memo = $request->param('memo', '');
        $roomUser = $request->param('room_user_name', '');
        $intermediary_id = $request->param('intermediary_id', '');
        $billCode = $request->param('bill_code');
        $roomSaleType = $request->param('room_sale_type');
        $roomNumber = $request->param('room_number', '');
        $arranged = $request->param('arranged', '');
        $commonCode = $request->param('common_code');
        $adminId = $request->param('admin_id');
        $stayType = $request->param('stay_type');
        $roomTypeId = $request->param('room_type_id');
        $priceProject = $request->param('price_project');
        $searchWord = [];
        if ($enterTimePlan) {
            $searchWord['enter_time_plan'] = $enterTimePlan;
        }
        if ($enterTime) {
            $searchWord['enter_time'] = $enterTime;
        }
        if ($leaveTimePlan) {
            $searchWord['leave_time_plan'] = $leaveTimePlan;
        }
        if ($leaveTime) {
            $searchWord['leave_time'] = $leaveTime;
        }
        if ($memo) {
            $searchWord['memo'] = $memo;
        }
        if ($roomUser) {
            $searchWord['room_user_name'] = $roomUser;
        }
        if ($intermediary_id) {
            $searchWord['intermediary_id'] = $intermediary_id;
        }
        if ($adminId) {
            $searchWord['admin_id'] = $adminId;
        }
        if ($stayType) {
            $searchWord['stay_type'] = $stayType;
        }
        if ($roomTypeId) {
            $searchWord['room_type_id'] = $roomTypeId;
        }
        if (!StringUtil::checkEmpty($arranged)) {
            $searchWord['arranged'] = $arranged;
        }
        if (!StringUtil::checkEmpty($priceProject)) {
            $searchWord['price_project'] = $priceProject;
        }
        $res = RoomBillService::getInstance()->adminGetStayingRoomBillList($shopId, $billStatus, $linkName, $linkPhone, $billCode, $billSource,
            $commonCode, $roomSaleType, $roomNumber, $searchWord, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     *
     * @param Request $request
     */
    public function getConnectBill(Request $request)
    {
        $billId = $request->param('bill_id');
        $res = RoomBillUtil::getInstance()->getConnectBillInfo($billId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 查询订单详情
     * @param Request $request
     */
    public function getRoomBillDetail(Request $request)
    {
        $billId = $request->param('bill_id');
        $res = RoomBillService::getInstance()->getBillInfo($billId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    public function getBillDataTips(Request $request)
    {
        $billId = $request->param('bill_id');
        $shopId = $this->admin_info['shop_id'];
        $res = RoomBillService::getInstance()->getBillDataTips($shopId, $billId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    public function getRoomLog(Request $request)
    {
        $billId = $request->param('bill_id');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $shopId = $this->admin_info['shop_id'];
        $type = $request->param('type');
        $res = RoomBillLogService::getInstance()->getRoomBillLogList($shopId, $billId, $type, '', $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 查询账单
     * @param Request $request
     */
    public function getRoomBillFund(Request $request)
    {
        $billId = $request->param('bill_id');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $typeId = $request->param('room_bill_type_id');
        $type = $request->param('type');
        $pid = $request->param('pid');
        $showZero = $request->param('show_zero',0);
        $res = RoomBillDetailService::getInstance()->getRoomBillDetailList($billId, $type, $typeId, 1, $pid,$showZero, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 查询联房订单
     * @param Request $request
     */
    public function getRoomBillFundByConnectCode(Request $request)
    {
        $connectCode = $request->param('connect_code');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $typeId = $request->param('room_bill_type_id');
        $type = $request->param('type');
        $pid = $request->param('pid');
        $res = RoomBillDetailService::getInstance()->getRoomBillFundByConnectCode($connectCode, $type, $typeId, 1, $pid, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }


    /**
     * 查询可自定义退款金额
     * @param Request $request
     */
    public function getCanRefundAmount(Request $request)
    {
        $billId = $request->param('bill_id');
        $res = RoomBillDetailService::getInstance()->getCanRefundAmount($billId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }


    /**
     * 已完成转为走结
     * @param Request $request
     * @throws \app\common\sysConfig\MyException
     */
    public function finishBillToNoClear(Request $request)
    {
        $billId = $request->param('bill_id');
        RoomBillUtil::getInstance()->finishBillToNoClear($billId);
        ResultData::simpleResult(Code::SUCCESS, Msg::success());
    }

    /**
     * 走结成转为已完
     * @param Request $request
     * @throws \app\common\sysConfig\MyException
     */
    public function clearingBillCheckOut(Request $request)
    {
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $billId = $request->param('bill_id');
        $adminId = $this->admin_info['admin_id'];
        Db::startTrans();
        try {
            RoomBillUtil::getInstance()->clearingBillCheckOut($shopIdZong, $billId, $adminId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 免单
     * @param Request $request
     */
    public function freeCharge(Request $request)
    {
        $params = $request->param();
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->freeCharge($adminId, $params);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, "免单成功");
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 入账
     * @param Request $request
     * @throws \WxPayException
     */
    public function addRoomPayDetail(Request $request)
    {
        $params = $request->param();
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->addRoomPayDetail($shopIdZong, $shopId, $params, $adminId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::addSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    public function posAddDetail(Request $request)
    {
        $params = $request->param();
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            $res = RoomFundUtil::getInstance()->posAddDetail($shopIdZong, $shopId, $adminId, $params);
            Db::commit();
            ResultData::complexResult($res, Code::SUCCESS, Msg::addSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 记消费
     * @param Request $request
     * @throws \WxPayException
     */
    public function addRoomConsumeDetail(Request $request)
    {
        $params = $request->param();
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->addRoomConsumeDetail($params, $adminId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::addSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 冲调
     * @param Request $request
     */
    public function modifyRoomBillDetail(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $modifyAmount = $request->param('modify_amount');
        $detailId = $request->param('detail_id');
        $memo = $request->param('memo');
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->modifyRoomBillDetail($shopIdZong, $adminId, $detailId, $modifyAmount, $memo);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 打折
     * @param Request $request
     */
    public function roomBillDiscount(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $shopId = $this->admin_info['shop_id'];
        $billId = $request->param('bill_id');
        $detailIds = $request->param('detailIds/a');
        $discountRate = $request->param('discount_rate');
        $reductionAmount = $request->param('reduction_amount');
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->roomBillDiscount($shopId, $billId, $adminId, $detailIds, $discountRate, $reductionAmount);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 移除超时收费
     * @param Request $request
     */
    public function delOverTimeRoomBillDetail(Request $request)
    {
        $detailId = $request->param('detail_id');
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->delOverTimeRoomBillDetail($detailId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 账单退款
     * @param Request $request
     */
    public function refundBillDetail(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $refundAmount = $request->param('refund_amount');
        $detailId = $request->param('detail_id');
        $memo = $request->param('memo');
        $print = $request->param('print');
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->refundBillDetail($shopIdZong, $adminId, $detailId, $refundAmount, $memo, $print);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 自定义退款
     * @param Request $request
     */
    public function refundBillDetailByAdmin(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $refundAmount = $request->param('refund_amount');
        $billId = $request->param('bill_id');
        $accountId = $request->param('account_id');
        $memo = $request->param('memo');
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->refundBillDetailByAdmin($shopIdZong, $adminId, $billId, $accountId, $refundAmount, $memo);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 查询入住人
     * @param Request $request
     */
    public function getRoomBillUser(Request $request)
    {
        $billId = $request->param('bill_id');
        $res = RoomBillUserService::getInstance()->getRoomBillUserSimpleList($billId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 删除入住人
     * @param Request $request
     */
    public function delRoomBillUser(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $id = $request->param('room_user_id');
        try {
            Db::startTrans();
            RoomBillUserService::getInstance()->delRoomBillUser($id);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::delSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 编辑入住人
     * @param Request $request
     */
    public function updateRoomBillUser(Request $request)
    {
        $params = $request->param();
        $shop_id_zong = $this->admin_info['shop_id_zong'];
        try {
            Db::startTrans();
            RoomBillUserService::getInstance()->updateRoomBillUser($shop_id_zong, $params);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
        }catch (Exception $exception){{
            Db::rollback();
            ResultData::returnErrorMsg($exception);}
        }
    }


    /**
     * 查询收款分组
     * @param Request $request
     */
    public function getDetailTypeGroup(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $type = $request->param('type');
        $res = RoomBillDetailTypeGroupService::getInstance()->getRoomBillDetailTypeGroupList($shopId, $type);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }


    /**
     * 编辑消费分组
     * @param Request $request
     * @throws \app\common\sysConfig\MyException
     */
    public function editDetailTypeGroup(Request $request)
    {
        $param = $request->param();
        $shopId = $this->admin_info['shop_id'];
        $param['shop_id'] = $shopId;
        RoomBillDetailTypeGroupService::getInstance()->updateDetailTypeGroup($param);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    /**
     * 查询消费，入账项目
     * @param Request $request
     */
    public function getDetailTypeByGroupId(Request $request)
    {
        $groupId = $request->param('group_id');
        $shopId = $this->admin_info['shop_id'];
        $type = $request->param('type');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $res = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeList($shopId, $groupId, $type, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }


    /**
     * 编辑消费、入账项目
     * @param Request $request
     * @throws \app\common\sysConfig\MyException
     */
    public function editPayDetailType(Request $request)
    {
        $param = $request->param();
        $shopId = $this->admin_info['shop_id'];
        $param['shop_id'] = $shopId;
        if (isset($param['id'])) {
            $scene = 'edit';
        } else {
            $scene = 'save';
        }
        $validate = new RoomBillDetailTypeValidate();
        $check = $validate->scene($scene)->check($param);
        if ($check) {
            RoomBillDetailTypeService::getInstance()->updateRoomBillDetailType($param);
            ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
        } else {
            ResultData::simpleResult(Code::FAIL, $validate->getError());
        }
    }

    public function delRoomBillDetailType(Request $request)
    {
        $id = $request->param('id');
        RoomBillDetailTypeService::getInstance()->delRoomBillDetailType($id);
        ResultData::simpleResult(Code::SUCCESS, Msg::delSuccess());

    }


    /**
     * 查询入住人信息
     * @param Request $request
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getRoomUserInfo(Request $request)
    {
        $searchWord = $request->param('search_word');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $res = RoomBillUserService::getInstance()->getUserInfoBySearchWord($shopIdZong, $searchWord, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 联房
     * @param Request $request
     */
    public function connectRoomBill(Request $request)
    {
        $billIds = $request->param('bill_ids/a');
        if (empty($billIds)) {
            throw new MyException(Code::FAIL, "请选择房间");
        }
        Db::startTrans();
        try {
            RoomBillConnectService::getInstance()->addRoomBillConnect($billIds);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 修改房价
     * @param Request $request
     */
    public function updateBillPrice(Request $request)
    {
        $priceId = $request->param('room_price_id');
        $roomPrice = $request->param('room_price');
        $memo = $request->param('memo');
        Db::startTrans();
        try {
            RoomBillUtil::getInstance()->updateRoomPrice($priceId, $roomPrice, $memo);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 设置主房
     * @param Request $request
     */
    public function setMainRoom(Request $request)
    {
        $billId = $request->param('bill_id');
        Db::startTrans();
        try {
            RoomBillUtil::getInstance()->setMainRoom($billId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 编辑订单
     * @param Request $request
     */
    public function updateBillInfo(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $billId = $request->param('bill_id');
        $linkMan = $request->param('link_man');
        $linkPhone = $request->param('link_phone');
        $billSource = $request->param('bill_source');
        $billMemo = $request->param('memo');
        $otherBillCode = $request->param('other_bill_code');
        $secrecy = $request->param('secrecy');
        $distribution_user_id = $request->param('distribution_user_id');
        RoomBillUtil::getInstance()->updateBillInfo($adminId, $billId, $linkMan, $linkPhone, $billSource, $otherBillCode, $billMemo, $secrecy,$distribution_user_id);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    public function updateBillMemo(Request $request)
    {
        $adminId = $this->admin_info['admin_id'];
        $billId = $request->param('bill_id');
        $billMemo = $request->param('memo');
        RoomBillUtil::getInstance()->updateBillMemo($billId,$billMemo,$adminId);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    /**
     * 删除分销人
     * @param Request $request
     * @return void
     */
    public function delDistributionUserId(Request $request)
    {
        $billId = $request->param('bill_id');
        RoomBillUtil::getInstance()->delDistributionUserId( $billId);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }
    /**
     * 更换订单房型
     * @param Request $request
     * @return void
     * @throws MyException
     */
    public function updateRoomType(Request $request)
    {
        $roomTypeId = $request->param('room_type_id');
        $billId = $request->param('bill_id');
        RoomBillUtil::getInstance()->updateRoomType($billId, $roomTypeId);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    /**
     * 编辑详情备注
     * @param Request $request
     */
    public function updateRoomBillDetailMemo(Request $request)
    {
        $detailId = $request->param('detail_id');
        $memo = $request->param('memo');
        RoomBillDetailService::getInstance()->updateMemo($detailId, $memo);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    /**
     * 查询订单二维码
     * @param Request $request
     */
    public function createRoomBillQrCode(Request $request)
    {
        $id = $request->param('bill_id');
        $url = RoomBillQrService::getInstance()->adminGetQrCode($id);
        ResultData::complexResult($url, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 修改预定单时间
     * @param Request $request
     */
    public function updateBillBookTime(Request $request)
    {
        $billId = $request->param('bill_id');
        $startTime = $request->param('start_time');
        $times = $request->param('times');
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $shopId = $this->admin_info['shop_id'];
        Db::startTrans();
        try {
            RoomBillUtil::getInstance()->updateBillBookTime($shopIdZong, $shopId, $billId, $startTime, $times);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 通过房间号查询入住中订单
     * @param Request $request
     */
    public function getStayingRoomList(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $roomNumber = $request->param('room_number');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $res = RoomBillService::getInstance()->getStayingRoomBillByRoomNumber($shopId, $roomNumber, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    public function getDefaultEnterTime()
    {
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $res = MemberAndRightInterestService::getInstance()->getEnterTime($shopIdZong);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 入住人退房
     * @param Request $request
     */
    public function userCheckOut(Request $request)
    {
        $billId = $request->param('bill_id');
        $billUserId = $request->param('bill_user_id');
        $adminId = $this->admin_info['admin_id'];
        $userStatus = $request->param('user_status');
        Db::startTrans();
        try {
            if ($userStatus) {
                RoomBillUserService::getInstance()->updateUserStatus($billUserId, $userStatus);
            } else {
                RoomBillUtil::getInstance()->userCheckOut($billId, $billUserId, $adminId);
            }
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    public function multipleRoomAmount(Request $request)
    {
        $billIds = $request->param('bill_ids/a');
        $print = $request->param('print', 0);
        $amount = $request->param('amount');
        $memo = $request->param('memo');
        $accountId = $request->param('account_id', 0);
        $authCode = $request->param('auth_code');
        $room_bill_type_id = $request->param('room_bill_type_id', 0);
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $shopId = $this->admin_info['shop_id'];
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->multipleRoomAmount($shopIdZong, $shopId, $billIds, $print, $amount, $memo, $accountId, $authCode, $room_bill_type_id, $adminId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 打印二维码
     * @param Request $request
     * @throws MyException
     */
    public function printRoomKeyQrCode(Request $request)
    {
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $shopId = $this->admin_info['shop_id'];
        $billIds = $request->param('bill_ids/a');
        $res = RoomBillQrService::getInstance()->printRoomKeyQrCodes($shopIdZong, $shopId, $billIds);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 入账转移
     * @param Request $request
     */
    public function transferRoomFund(Request $request)
    {
        $newBillId = $request->param('to_bill_id');
        $billDetailId = $request->param('bill_detail_id');
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $shopId = $this->admin_info['shop_id'];
        $adminId = $this->admin_info['admin_id'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->transferRoomFund($newBillId, $billDetailId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * 查询未上传公安入住人
     * @param Request $request
     */
    public function getNotUploadRoomBillUser(Request $request)
    {
        $page = $request->param('page');
        $limit = $request->param('limit');
        $shopId = $this->admin_info['shop_id'];
        $res = RoomBillUserService::getInstance()->getNotUploadRoomBillUser($shopId, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 预定直接转完成
     * @param Request $request
     */
    public function bookingToFinish(Request $request)
    {
        $billId = $request->param('bill_id');
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $remark = $request->param('remark');
        try {
            Db::startTrans();
            RoomBillUtil::getInstance()->bookingToFinish($shopIdZong, $billId, $adminId, $remark);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    /**
     * noshow转完成
     * @param Request $request
     * @return void
     */
    public function noShowToFinish(Request $request)
    {
        $billId = $request->param('bill_id');
        $adminId = $this->admin_info['admin_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        $remark = $request->param('remark');
        try {
            Db::startTrans();
            RoomBillUtil::getInstance()->noShowToFinish($shopIdZong, $billId, $adminId, $remark);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 预授权
     * @param Request $request
     */
    public function getPreAuthBillDetail(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $preStatus = $request->param('pre_status');
        $page = $request->param('page');
        $limit = $request->param('limit');
        $res = RoomFundUtil::getInstance()->getPreAuthBill($shopId, null, $preStatus, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
    }

    /**
     * 预授权完成
     * @param Request $request
     */
    public function preAuthComplete(Request $request)
    {
        $detailId = $request->param('detail_id');
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        try {
            Db::startTrans();
            RoomFundUtil::getInstance()->preAuthComplete($shopIdZong, $shopId, $detailId);
            Db::commit();
            ResultData::simpleResult(Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

}