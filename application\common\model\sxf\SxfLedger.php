<?php

namespace app\common\model\sxf;

use app\common\utils\StringUtil;
use think\Model;

class SxfLedger extends Model
{
    //
    //id
    //main_mno 分账商户号
    //child_mno 分账商户号
    //status 0待处理，1成功，2失败
    //create_time
    //rate 比例
    private $fields = ['id', 'main_mno', 'child_mno', 'status', 'create_time', 'rate', 'split_cycle', 'relation_ship',
        'scenes', 'agreement_pic', 'scenes_pic', 'ledger_protocol_pic', 'other_pic', 'remark', 'result','apply_id'];
    private $sqlStr = 'a.id,
        a.main_mno,
        a.child_mno,
        a.status,
        a.create_time,
        a.rate,
        a.split_cycle,
        a.relation_ship,
        a.scenes,
        a.agreement_pic,
        a.scenes_pic,
        a.ledger_protocol_pic,
        a.other_pic,
        a.remark,
        a.result,
        a.apply_id';
    protected $autoWriteTimestamp = true;
    protected $updateTime = false;
    protected $type = [
        'id' => 'integer',
        'status' => 'integer',
        'create_time' => 'integer',
        'rate' => 'float',
        'agreement_pic' => 'json',
        'scenes_pic' => 'json',
        'ledger_protocol_pic' => 'json',
        'other_pic' => 'json',
        ];


    public function getSxfLedgerList($mainMno, $childMno, $status, $page, $limit)
    {
        $this->alias('a')->field($this->sqlStr);
        if (!StringUtil::checkEmpty($status)) {
            $this->where('a.status', $status);
        }
        if (!StringUtil::checkEmpty($mainMno)) {
            $this->where('a.main_mno', $mainMno);
        }
        if (!StringUtil::checkEmpty($childMno)) {
            $this->where('a.child_mno', $childMno);
        }
        if ($page && $limit) {
            $this->page($page, $limit);
        }
        return $this->select();
    }

    public function getSxfLedgerCount($mainMno, $childMno, $status)
    {
        $this->alias('a');
        if (!StringUtil::checkEmpty($status)) {
            $this->where('a.status', $status);
        }
        if (!StringUtil::checkEmpty($mainMno)) {
            $this->where('a.main_mno', $mainMno);
        }
        if (!StringUtil::checkEmpty($childMno)) {
            $this->where('a.child_mno', $childMno);
        }
        return $this->count();
    }

    public function getSxfLedger($mainMno, $childMno, $status)
    {
        $this->alias('a')->field($this->sqlStr);
        if (!StringUtil::checkEmpty($status)) {
            $this->where('a.status', $status);
        }
        if (!StringUtil::checkEmpty($mainMno)) {
            $this->where('a.main_mno', $mainMno);
        }
        if (!StringUtil::checkEmpty($childMno)) {
            $this->where('a.child_mno', $childMno);
        }

        return $this->find();
    }


    public function getSxfLedgerById($id)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.id', $id);
        return $this->find();
    }
    public function getSxfLedgerByApplyId($applyId)
    {
        $this->alias('a')->field($this->sqlStr)->where('a.apply_id', $applyId);
        return $this->find();
    }


    public function delSxfLedgerById($id)
    {
        return $this->where('id', $id)->delete();
    }


    public function saveSxfLedger($params)
    {
        return $this->allowField($this->fields)
            ->save($params);
    }


    public function editSxfLedger($params)
    {
        return $this->allowField($this->fields)
            ->save($params, ['id' => $params['id']]);
    }

}