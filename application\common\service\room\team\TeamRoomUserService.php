<?php


namespace app\common\service\room\team;

use app\common\model\room\team\TeamRoomUser as TeamRoomUserModel;
use app\common\returnConfig\Code;
use app\common\service\room\RoomBillService;
use app\common\service\room\RoomBillUserService;
use app\common\service\room\RoomService;
use app\common\service\room\RoomTypeService;
use app\common\service\shop\ShopService;
use app\common\service\user\UserCommonService;
use app\common\service\user\UserOcrService;
use app\common\service\user\UserWxService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\DebugUtil;
use app\common\utils\ExcelUtil;
use app\common\utils\StringUtil;
use think\Config;
use think\Exception;

/**
 * Name:
 * Description:
 * Author: ShanGui
 * Date 2023-09-08
 */
class TeamRoomUserService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getTeamRoomUserList($teamId, $single, $page, $limit)
    {
        $model = new TeamRoomUserModel();
        $list = $model->getTeamRoomUserList($teamId, $single, $page, $limit);
        if ($page && $limit) {
            $count = $this->getCount($teamId, $single);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function getTeamRoomUserBillList($teamId, $single, $roomTypeId, $billStatus, $page, $limit)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserBillList($teamId, $single, $roomTypeId, $billStatus, $page, $limit);
    }

    public function getList($teamId, $single, $page, $limit)
    {
        $model = new TeamRoomUserModel();
        $list = $model->getTeamRoomUserList($teamId, $single, $page, $limit);
        return $list;
    }

    public function getCount($teamId, $single)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserCount($teamId, $single);
    }


    public function getTeamRoomUserCount($teamId, $single)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserCount($teamId, $single);
    }

    public function getTeamRoomUserById($id)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserById($id);
    }

    public function getTeamRoomUserByShareCode($code)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserByShareCode($code);
    }

    public function getTeamRoomUserListByShareCode($code)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamRoomUserListByShareCode($code);
    }

    public function delTeamRoomUserById($id)
    {
        $delUser = $this->getTeamRoomUserById($id);
        DebugUtil::log("del teamUser:" . json_encode($delUser));
        $model = new TeamRoomUserModel();
        return $model->delTeamRoomUserById($id);
    }


    /**
     * 查询团队人员
     * @param $teamId
     * @param $commonCode
     * @param $shareCode
     * @return array|int|string
     */
    public function getTeamAllUser($teamId, $commonCode, $shareCode)
    {
        if (empty($shareCode)) {
            $tempUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
            if (!empty($tempUser)) {
                $tempBillId = $tempUser->bill_id;
                $tempRoom = $this->getRoomClearStatus($tempBillId);
                $tempUser->room = $tempRoom;
                $newShareCode = $tempUser->share_code;
                if (!empty($newShareCode)) {
                    $users = $this->getTeamAllUserByShareCode($teamId, $newShareCode, $commonCode);
                } else {
                    $tempUser->share_from = 1;
                    $users = [$tempUser];
                }
            } else {
                $users = [];
            }
        } else {
            $users = $this->getTeamAllUserByShareCode($teamId, $shareCode, $commonCode);
        }
        return $users;
    }

    public function getTeamAllUserByAccnt($teamId, $accnt)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamAllUserByAccnt($teamId, $accnt);
    }

    public function getTeamUserByAccnt($accnt)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamUserByAccnt($accnt);
    }

    /**
     * @param $id
     * @param $teamConfirm 默认-1 单人入住0 被分享人接受后变成1，分享人二次确认后被分享人变成2
     * @return void
     */
    public function updateTeamRoomUserConTeamConfirm($id, $teamConfirm)
    {
        $this->editTeamRoomUser(['id' => $id, 'team_confirm' => $teamConfirm]);
    }

    public function getTeamAllUserByShareCode($teamId, $shareCode, $commonCode)
    {
        $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
        if (empty($qr)) {
            $tempUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
            return [$tempUser];
        }
        $commonCodeFrom = $qr->common_code_from;
        //两个人相互分享，A点了B的连接，B点了A的连接
        $qrUrl = $qr->url;
        $teamId = $qr->team_id;
        $fromUser = $this->getRoomUserByCommonCode($teamId, $commonCodeFrom);
        $fromUserCode = $fromUser->share_code;
        if ($fromUserCode && $fromUserCode != $shareCode) {
            //A点了B的连接，获取到code，查询二维码
            //二维码的分享分的code应该和这个code一致，如果不一致这说明点了其他人的连接
            $tempUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
            if (empty($tempUser)) {
                return [];
            }
            $tempUserCode = $tempUser->share_code;
            if (!empty($tempUserCode)) {
                $shareCode = $tempUserCode;
            }
            $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
            if (!empty($qr)) {
                $commonCodeFrom = $qr->common_code_from;
            }
        }
        $users = $this->getTeamRoomUserListByShareCode($shareCode);
        if (empty($users)) {
            $users = [];
        }
        $inUsers = false;
        foreach ($users as $user) {
            $itemUserCommonCode = $user->common_code;
            if ($itemUserCommonCode == $commonCode) {
                $inUsers = true;
            }
            $tempBillId = $user->bill_id;
            $user->room = $this->getRoomClearStatus($tempBillId);
            if ($itemUserCommonCode == $commonCodeFrom) {
                $user->share_from = 1;
                $user->qr_url = $qrUrl;
            } else {
                $user->share_from = 0;
                $user->qr_url = null;
            }
        }
        if (!$inUsers) {
            $tempUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
            if ($tempUser) {
                $tempUser->share_from = 0;
                $tempBillId = $tempUser->bill_id;
                $tempUser->room = $this->getRoomClearStatus($tempBillId);
                $users[] = $tempUser;
            }
        }
        return $users;
    }

    private function getRoomClearStatus($tempBillId)
    {
        if (!$tempBillId) {
            return null;
        }
        $tempBill = RoomBillService::getInstance()->getBillByIdFromCache($tempBillId);
        $roomId = $tempBill->room_id;
        $shopId = $tempBill->shop_id;
        $tempRoom = null;
        if ($roomId) {
            $tempRoom = RoomService::getInstance()->getRoomByIdFromCache($roomId);
            $roomNumber = $tempRoom->room_number;
            $cleanStatus = TeamSelectRoomUtil::getInstance()->flashRoomStatus($shopId, $roomId, $roomNumber);
            $tempRoom->clean_status = $cleanStatus;
        }
        return $tempRoom;
    }


    /**
     * 添加团队
     * @param $teamId
     * @param $userList
     * @param $billId
     */
    public function addTeamRoomUser($teamId, $userList, $billId = 0)
    {
        foreach ($userList as $item) {
            $name = isset($item['name']) ? $item['name'] : "";
            $old = $this->getRoomUserByName($teamId, $name, null);
            if (!empty($old)) {
                continue;
            }
            $phone = isset($item['phone']) ? $item['phone'] : "";
            $identification_type = isset($item['identification_type']) ? $item['identification_type'] : 1;
            $identification_number = isset($item['identification_number']) ? $item['identification_number'] : "";
            if (mb_strlen($identification_number) == 18) {
                $gender = StringUtil::getGenderFromIdCard($identification_number);
            } else {
                $gender = isset($item['gender']) ? $item['gender'] : "";
            }
            $nation_id = isset($item['nation_id']) ? $item['nation_id'] : 1;
            $data = ['team_id' => $teamId, 'name' => $name, 'phone' => $phone,

                'identification_type' => $identification_type, 'identification_number' => $identification_number,
                'nation_id' => $nation_id, 'gender' => $gender];
            if (isset($item['room_price'])) {
                $data['room_price'] = $item['room_price'];
                $data['cash_pledge'] = $item['cash_pledge'];
            }
            if (!empty($identification_number)) {
                $old = $this->getUserByIdentificationNumber($teamId, null, $identification_number);
                if ($old) {
                    $data['id'] = $old->id;
                }
            }
            $data['share'] = 2;
            if ($billId) {
                $data['bill_id'] = $billId;
            }
            $data['team_confirm'] = -1;
            $id = $this->editTeamRoomUser($data);
        }
    }

    /**
     * 添加团队
     * @param $teamId
     * @param $userList
     * @param $billId
     * @param $bedCount
     * @param $customPrice
     */
    public function apiAddTeamRoomUser($teamId, $teamName, $teamType, $userList, $billId, $bedCount, $customPrice, $shareUsable, $all)
    {
        $accept = 0;
        $share = 2;
        DebugUtil::log("teamType: $teamType");
        if ($shareUsable == 0) {
            $share = 0;
        } else {
            if ($teamType == 3) {
                //旅游团默认有同住人
                $share = 1;
            } elseif ($teamType == 2) {
                //有名单
                $accept = 1;
                if ($billId) {
                    DebugUtil::log("step 2");
                    $accept = 0;
                    if ($bedCount > 1) {
                        DebugUtil::log("step 3");
                        $share = 0;
                    } else {
                        DebugUtil::log("step 4");
                        $share = 2;
                    }
                    DebugUtil::log("step 5");
                }
            } else {
                //无名单
                $accept = 1;
                if ($billId) {
                    $accept = 0;
                    if ($bedCount == 2) {
                        //指定了房间，床位为2,无法分享
                        $share = 0;
                    }
                }
            }
        }
        foreach ($userList as $item) {
            $name = isset($item['name']) ? $item['name'] : "";
            if ($name == $teamName) {
                continue;
            }
            $userStatus = isset($item['user_status']) ? $item['user_status'] : 0;

            $phone = isset($item['phone']) ? $item['phone'] : "";
            $identification_type = isset($item['identification_type']) ? $item['identification_type'] : 1;
            $identification_number = isset($item['identification_number']) ? $item['identification_number'] : "";
            if (mb_strlen($identification_number) == 18) {
                $gender = StringUtil::getGenderFromIdCard($identification_number);
            } else {
                $gender = isset($item['gender']) ? $item['gender'] : "";
            }
            $nation_id = isset($item['nation_id']) ? $item['nation_id'] : 1;
            $accnt = isset($item['accnt']) ? $item['accnt'] : null;
            $data = ['team_id' => $teamId, 'name' => $name,
                'identification_type' => $identification_type, 'gender' => $gender];
            if (!empty($identification_number)) {
                $data['identification_number'] = $identification_number;
            }
            if (!empty($phone)) {
                $data['phone'] = $phone;
            }
            if (!empty($nation_id)) {
                $data['nation_id'] = $nation_id;
            }
            $oldAccnt = $this->getTeamAllUserByAccnt($teamId, $accnt);
            $oldId = 0;
            if ($oldAccnt) {
                if ($all) {
                    continue;
                }
                $oldId = $oldAccnt->id;
            }
            if (!$oldId) {
                $old = $this->getRoomUserByName($teamId, $name, null);
                if ($old) {
                    if ($all) {
                        continue;
                    }
                    $oldShare = $old->share;
                    $oldId = $old->id;
                    $share = $oldShare;
                }
            }
            if ($userStatus == -1) {
                //取消
                $this->delTeamRoomUserById($oldId);
                continue;
            }
            $single = isset($item['single']) ? $item['single'] : -1;
//            if ($single < 0) {
//                $single = 0;
//            }
            $roomPrice = 0;
            $cashPledge = 0;
            $prices = [];
            if (isset($item['room_price'])) {
                foreach ($prices as $index => $itemPrice) {
                    $roomPrice += $itemPrice['room_price'];
                    if (!isset($itemPrice['cash_pledge'])) {
                        $cashPledge = floatval(sprintf("%.2f", ($customPrice[$index]['cash_pledge'] / $bedCount)));
                    } else {
                        $roomCashPledge = floatval(sprintf("%.2f", ($customPrice[$index]['cash_pledge'] / $bedCount)));
                        if ($cashPledge == 0 && $roomCashPledge != $cashPledge) {
                            $cashPledge = $itemPrice['cash_pledge'];
                        } else {
                            $cashPledge = $roomCashPledge;
                        }
                    }
                    $prices[] = ['date' => $itemPrice['date'], 'room_price' => $roomPrice, 'cash_pledge' => $cashPledge];
                }
            } elseif ($customPrice) {
                foreach ($customPrice as $itemPrice) {
                    $tempRoomPrice = floatval(sprintf("%.2f", ($itemPrice['room_price'] / $bedCount)));
                    $roomPrice += $tempRoomPrice;
                    $tempCashPledge = floatval(sprintf("%.2f", ($itemPrice['cash_pledge'] / $bedCount)));
                    $prices[] = ['date' => $itemPrice['date'], 'room_price' => $tempRoomPrice, 'cash_pledge' => $tempCashPledge];
                }
            }
            $data['room_price'] = $roomPrice;
            $data['cash_pledge'] = $cashPledge;

            if ($oldId) {
                $data['id'] = $oldId;
            }
            if ($billId) {
                $data['bill_id'] = $billId;
            }
            $data['prices'] = $prices;
            $data['single'] = $single;
            $data['share'] = $share;
            $data['accept'] = $accept;
            $data['accnt'] = $accnt;
            $data['team_confirm'] = -1;
            $id = $this->editTeamRoomUser($data);
            //用户扫码并上传身份证后，pms修改了用户名导致相同的名字，需要删除一个
            $saveUser = $this->getTeamRoomUserById($id);
            $saveUserCommonCode = $saveUser->common_code;
            if (!$saveUserCommonCode) {
                $sameNameUserList = $this->getRoomUserListByName($teamId, $name);
                if (count($sameNameUserList) > 1) {
                    foreach ($sameNameUserList as $sameNameUser) {
                        $tempUserId = $sameNameUser->id;
                        if ($tempUserId != $id) {
                            $this->editTeamRoomUser(['id' => $id, 'phone' => $sameNameUser->phone, 'single' => $single,
                                'identification_number' => $sameNameUser->identification_number,
                                'identification_type' => $sameNameUser->identification_type,
                                '$tempCommonCode' => $sameNameUser->common_code,
                                'identification_image' => $sameNameUser->identification_image,
                                'gender' => $sameNameUser->gender,
                            ]);
                            $this->delTeamRoomUserById($sameNameUser->id);
                        }
                    }
                }
            }
        }
    }

    public function checkTeamUser($teamId, $userList)
    {
        $users = $this->getList($teamId, null, null, null);
        foreach ($users as $user) {
            $isExist = false;
            foreach ($userList as $item) {
                if ($item['accnt'] == $user->accnt) {
                    $isExist = true;
                    break;
                }
            }
            if (!$isExist) {
                $this->delTeamRoomUserById($user->id);
            }
        }
    }

    public function getUserByIdentificationNumber($teamId, $billId, $identificationNumber)
    {
        $model = new TeamRoomUserModel();
        return $model->getUserByIdentificationNumber($teamId, $billId, $identificationNumber);
    }

    public function getRoomUserByBillId($billId)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserByBillId($billId);
    }
    public function getRoomUserListByBillId($billId)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserListByBillId($billId);
    }

    public function getRoomUserByPhone($teamId, $phone, $billId)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserByPhone($teamId, $phone, $billId);
    }

    public function getRoomUserByName($teamId, $name, $billId)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserByName($teamId, $name, $billId);
    }

    public function getRoomUserListByName($teamId, $name)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserListByName($teamId, $name);
    }

    public function getRoomUserByCommonCode($teamId, $commonCode)
    {
        $model = new TeamRoomUserModel();
        return $model->getRoomUserByCommonCode($teamId, $commonCode);
    }

    public function getTeamUserCountByShareCode($shareCode)
    {
        $model = new TeamRoomUserModel();
        return $model->getTeamUserCountByShareCode($shareCode);
    }

    public function updateTeamUserInfo($id, $billId, $name, $phone, $identification_type, $identification_number,
                                       $gender, $nation_id, $payStatus, $commonCode, $single, $identification_image)
    {
        $data = ['id' => $id];
        if (!StringUtil::checkEmpty($name)) {
            $data['name'] = $name;
        }
        if (!StringUtil::checkEmpty($phone)) {
            $data['phone'] = $phone;
        }
        if (!StringUtil::checkEmpty($identification_image)) {
            $data['identification_image'] = $identification_image;
        }
        if (!StringUtil::checkEmpty($identification_type)) {
            $data['identification_type'] = $identification_type;
        }
        if (!StringUtil::checkEmpty($identification_number)) {
            $data['identification_number'] = $identification_number;
        }
        if (!StringUtil::checkEmpty($nation_id)) {
            $data['nation_id'] = $nation_id;
        }
        if (!StringUtil::checkEmpty($gender)) {
            $data['gender'] = $gender;
        }
        if (!StringUtil::checkEmpty($billId)) {
            $data['bill_id'] = $billId;
        }
        if (!StringUtil::checkEmpty($payStatus)) {
            $data['pay_status'] = $payStatus;
        }
        if (!StringUtil::checkEmpty($commonCode)) {
            $data['common_code'] = $commonCode;
        }
        if (!StringUtil::checkEmpty($single)) {
            $data['single'] = $single;
        }
        DebugUtil::log("updateTeamUserInfo");
        $this->editTeamRoomUser($data);
    }

    /**
     * 添加团队入住人
     * @param $teamId
     * @param $billId
     * @param $name
     * @param $phone
     * @param $identification_type
     * @param $identification_number
     * @param $gender
     * @param $nation_id
     * @param $commonCode
     */
    public function addTeamUser($teamId, $billId, $name, $phone, $identification_type, $identification_number,
                                $gender, $nation_id, $commonCode, $shareCode, $share, $single)
    {
        $data = ['team_id' => $teamId, 'name' => $name, 'phone' => $phone,
            'identification_type' => $identification_type, 'identification_number' => $identification_number,
            'nation_id' => $nation_id, 'gender' => $gender, 'bill_id' => $billId];
        if ($shareCode) {
            $data['share_code'] = $shareCode;
        }
        if (!StringUtil::checkEmpty($commonCode)) {
            $data['common_code'] = $commonCode;
            $old = $this->getRoomUserByCommonCode($teamId, $commonCode);
            if ($old) {
                $data['id'] = $old->id;
                $data['single'] = $old->single;
                $data['share'] = $old->share;
                $data['bill_id'] = $old->bill_id;
            }
        }
        if (!empty($identification_number)) {
            $old = $this->getUserByIdentificationNumber($teamId, $billId, $identification_number);
            if ($old) {
                $data['id'] = $old->id;
                $data['single'] = $old->single;
                $data['share'] = $old->share;
                $data['bill_id'] = $old->bill_id;
            }
        }
        if (!empty($name)) {
            $old = $this->getRoomUserByName($teamId, $name, $billId);
            if ($old) {
                $data['id'] = $old->id;
                $data['single'] = $old->single;
                $data['share'] = $old->share;
                $data['bill_id'] = $old->bill_id;
            }
        }
        list($prices, $roomPrice, $cashPledge) = TeamSelectRoomUtil::getInstance()->getTeamPrice($teamId);
        if (!isset($data['share'])) {
            $data['share'] = $share;
        }
        if (!StringUtil::checkEmpty($single)) {
            $data['single'] = $single;
        } else {
            $data['single'] = -1;
        }
        $data['prices'] = $prices;
        $data['room_price'] = $roomPrice;
        $data['cash_pledge'] = $cashPledge;
        $data['team_confirm'] = -1;
        return $this->editTeamRoomUser($data);
    }

    public function updateTeamUser($param)
    {
        $identification_number = isset($param['identification_number']) ? $param['identification_number'] : "";
        if (mb_strlen($identification_number) == 18) {
            $gender = StringUtil::getGenderFromIdCard($identification_number);
            $param['gender'] = $gender;
        }
        if (!empty($identification_number) && isset($param['id'])) {
            $old = $this->getTeamRoomUserById($param['id']);
            if ($old) {
                $param['id'] = $old->id;
            }
        }
//        DebugUtil::log($param);
        $this->editTeamRoomUser($param);
    }

    public function updateSingle($id, $single)
    {
        $this->editTeamRoomUser(['id' => $id, "single" => $single]);
    }

    public function updateShare($id, $share, $shareLimit, $shareCode)
    {
        $data = [];
        if (!StringUtil::checkEmpty($share)) {
            $data['share'] = $share;
        }
        if (!StringUtil::checkEmpty($shareLimit)) {
            $data['share_limit'] = $shareLimit;
        }
        if (!StringUtil::checkEmpty($shareCode)) {
            $data['share_code'] = $shareCode;
        }
        if (!empty($data)) {
            $data['id'] = $id;
            $this->editTeamRoomUser($data);
        }
    }

    public function refuseSameRoom($id)
    {
        $this->editTeamRoomUser(['id' => $id, 'share' => -1, 'share_limit' => 0, 'share_code' => null]);
    }

    public function apiUpdateTeamUser($userList, $teamId, $teamType, $billId, $customPrice, $shareUsable, $bedCount)
    {
        $accept = 0;
        $share = 2;
        if ($shareUsable == 0) {
            $share = 0;
        } else {
            if ($teamType == 3) {
                //旅游团默认有同住人
                $share = 1;
            } elseif ($teamType == 2) {
                $accept = 1;
                if ($billId) {
                    $accept = 0;
                    if ($bedCount == 2) {
                        $share = 0;
                    } else {
                        $share = 1;
                    }
                }
            } else {
                $accept = 1;
                if ($billId) {
                    $accept = 0;
                    if ($bedCount == 2) {
                        //指定了房间，床位为2,无法分享
                        $share = 0;
                    }
                }
            }
        }
        foreach ($userList as $item) {
            $name = $item['name'];
            $old = $this->getRoomUserByName($teamId, $name, $billId);
            if (isset($item['prices'])) {
                $data['room_price'] = $item['prices'];
                $data['cash_pledge'] = $item['cash_pledge'];
            } elseif ($customPrice) {
                $data['room_price'] = floatval(sprintf("%.2f", ($customPrice['custom_room_price'] / $bedCount)));
                $data['cash_pledge'] = floatval(sprintf("%.2f", ($customPrice['custom_cash_pledge'] / $bedCount)));
            }

            $phone = isset($item['phone']) ? $item['phone'] : "";
            $identification_type = isset($item['identification_type']) ? $item['identification_type'] : 1;
            $identification_number = isset($item['identification_number']) ? $item['identification_number'] : "";
            if (mb_strlen($identification_number) == 18) {
                $gender = StringUtil::getGenderFromIdCard($identification_number);
            } else {
                $gender = isset($item['gender']) ? $item['gender'] : "";
            }
            $nation_id = isset($item['nation_id']) ? $item['nation_id'] : 1;
            $data = ['team_id' => $teamId, 'name' => $name, 'phone' => $phone,

                'identification_type' => $identification_type, 'identification_number' => $identification_number,
                'nation_id' => $nation_id, 'gender' => $gender];
            if (isset($item['prices'])) {
                $data['room_price'] = $item['prices'];
                $data['cash_pledge'] = $item['cash_pledge'];
            } elseif ($customPrice) {
                $data['room_price'] = floatval(sprintf("%.2f", ($customPrice['custom_room_price'] / $bedCount)));
                $data['cash_pledge'] = floatval(sprintf("%.2f", ($customPrice['custom_cash_pledge'] / $bedCount)));
            }
            if (!empty($identification_number)) {
                $old = $this->getUserByIdentificationNumber($teamId, $billId, $identification_number);
                if ($old) {
                    $data['id'] = $old->id;
                }
            }
            if (!empty($name)) {
                $old = $this->getRoomUserByName($teamId, $name, $billId);
                if ($old) {
                    $data['id'] = $old->id;
                }
            }
            if ($billId) {
                $data['bill_id'] = $billId;
            }
            $data['share'] = $share;
            $data['accept'] = $accept;
            if (empty($old)) {
                $id = $this->editTeamRoomUser($data);
            } else {
                $data['id'] = $old->id;
            }
        }

    }

    /**
     * 从指定的名单信息更新到当前用户
     * @param $teamId
     * @param $commonCode
     */
    public function synchronizationTeamRoomUser($teamId, $commonCode)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shareUsable = $team->share_usable;
        $teamType = $team->team_type;
        $userOcr = UserOcrService::getInstance()->getUserOcrByCommonCode($commonCode);
        $name = $userOcr->name;
        $identification_number = $userOcr->identification_number;
        $id_image = $userOcr->id_image;
        $oldUser = $this->getRoomUserByName($teamId, $name, null);
        if ($oldUser) {
            $prices = $oldUser->prices;
            $accnt = $oldUser->accnt;
            $newUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
            $newUserId = $newUser->id;
            $newUserShare = $newUser->share;
            $oldUserShare = $oldUser->share;
            $oldAccept = $oldUser->accept;
            if ($oldUser->id != $newUserId && $oldUser->bill_id) {
                DebugUtil::log("del old user");
                $this->delTeamRoomUserById($oldUser->id);
            }
            $OldBillId = $oldUser->bill_id;
            if ($shareUsable == 0) {
                $share = 0;
            } else {
                if ($oldUserShare == 0) {
                    $share = $oldUserShare;
                } else {
                    $share = $newUserShare;
                }
            }
            $updateData = ['id' => $newUserId, 'share' => $share,
                'accept' => $oldAccept, 'prices' => $prices, 'accnt' => $accnt, 'identification_image' => $id_image,
                'identification_number' => $identification_number, 'name' => $name];
            if ($OldBillId) {
                $updateData['bill_id'] = $OldBillId;
            }
            $this->editTeamRoomUser($updateData);
            if ($OldBillId) {
                TeamSelectRoomUtil::getInstance()->autoSetRoomBillRoom($teamId, $OldBillId, $name);
            }
        }
    }

    public function setOverTime($id)
    {
        $this->editTeamRoomUser(['id' => $id, 'share_limit' => 0, 'share_code' => null]);
    }


    public function getOverTimeUser($shopId)
    {
        $model = new TeamRoomUserModel();
        return $model->getOverTimeUser($shopId);
    }


    public function delRoomUser($teamId, $billId, $identificationNumber)
    {
        $old = $this->getUserByIdentificationNumber($teamId, $billId, $identificationNumber);
        if ($old) {
            $id = $old->id;
            $this->delTeamRoomUserById($id);
            DebugUtil::log("del user $identificationNumber");
        }
    }

    /**
     * 接受分享的人点确认授权
     * @param $id
     * @param $confirm
     * @return void
     */
    public function updateTeamUserConfirm($id, $confirm)
    {
        $this->editTeamRoomUser(['id' => $id, 'confirm' => $confirm]);

    }

    public function getSameRoomUserCount($teamId, $commonCode)
    {
        $teamUser = $this->getRoomUserByCommonCode($teamId, $commonCode);
        if ($teamUser) {
            $shareCode = $teamUser->share_code;
            $this->getTeamUserCountByShareCode($shareCode);
        }

    }

    public function editTeamRoomUser($params)
    {
        DebugUtil::log(json_encode($params));
        $model = new TeamRoomUserModel();
        if (isset($params['id'])) {
            $model->editTeamRoomUser($params);
            $id = $params['id'];
        } else {
            if (isset($params['name'])) {
                $teamId = $params['team_id'];
                $name = $params['name'];
                $cacheKey = "team_room_user_name_" . $teamId . '_' . $name;
                if (CacheManager::has($cacheKey)) {
                    return 0;
                }
                CacheManager::setCacheValue($cacheKey, $name, null, null, 5);
            }
            $model->saveTeamRoomUser($params);
            $id = $model->id;
        }
        return $id;
    }

    /**
     * 删除重复用户
     * @param $teamId
     * @return void
     */
    public function delSameUser($teamId)
    {
        $sameUserNameList = [];
        $tempUserNameList = [];
        $userList = $this->getList($teamId, null, null, null);
        foreach ($userList as $user) {
            $name = $user['name'];
            if (isset($tempUserNameList[$name])) {
                if (isset($sameUserNameList[$name])) {
                    $sameUserNameList[$name]['count']++;
                } else {
                    $sameUserNameList[$name] = ['name' => $name, 'count' => 1];
                }
            } else {
                $tempUserNameList[$name] = $name;
            }
        }
        foreach ($sameUserNameList as $sameUser) {
            $isFirstUser = true;
            $firstUser = [];
            foreach ($userList as $user) {

                if (!empty($user['phone'])) {
                    $firstUser['phone'] = $user['phone'];
                }
                if (!empty($user['identification_type'])) {
                    $firstUser['identification_type'] = $user['identification_type'];
                }
                if (!empty($user['identification_number'])) {
                    $firstUser['identification_number'] = $user['identification_number'];
                }
                if (!empty($user['nation_id'])) {
                    $firstUser['nation_id'] = $user['nation_id'];
                }
                if (!empty($user['gender'])) {
                    $firstUser['gender'] = $user['gender'];
                }
                if (!empty($user['bill_id'])) {
                    $firstUser['bill_id'] = $user['bill_id'];
                }
                if (!empty($user['common_code'])) {
                    $firstUser['common_code'] = $user['common_code'];
                }
                if (!empty($user['pay_status'])) {
                    $firstUser['pay_status'] = $user['pay_status'];
                }
                if (!empty($user['single'])) {
                    $firstUser['single'] = $user['single'];
                }
                if (!empty($user['share'])) {
                    $firstUser['share'] = $user['share'];
                }
                if (!empty($user['share_limit'])) {
                    $firstUser['share_limit'] = $user['share_limit'];
                }
                if (!empty($user['share_code'])) {
                    $firstUser['share_code'] = $user['share_code'];
                }
                if (!empty($user['user_image'])) {
                    $firstUser['user_image'] = $user['user_image'];
                }
                if (!empty($user['identification_image'])) {
                    $firstUser['identification_image'] = $user['identification_image'];
                }
                if (!empty($user['authentication'])) {
                    $firstUser['authentication'] = $user['authentication'];
                }
                if (!empty($user['confirm'])) {
                    $firstUser['confirm'] = $user['confirm'];
                }
                if (!empty($user['room_price'])) {
                    $firstUser['room_price'] = $user['room_price'];
                }
                if (!empty($user['cash_pledge'])) {
                    $firstUser['cash_pledge'] = $user['cash_pledge'];
                }
                if (!empty($user['accept'])) {
                    $firstUser['accept'] = $user['accept'];
                }
                if (!empty($user['prices'])) {
                    $firstUser['prices'] = $user['prices'];
                }
                if (!empty($user['accnt'])) {
                    $firstUser['accnt'] = $user['accnt'];
                }
                if (!empty($user['team_confirm'])) {
                    $firstUser['team_confirm'] = $user['team_confirm'];
                }
                if ($isFirstUser) {
                    $firstUser['id'] = $user['id'];
                    $firstUser['team_id'] = $user['team_id'];
                    $firstUser['name'] = $user['name'];
                } else {
                    $this->delTeamRoomUserById($user['id']);
                }
                $isFirstUser = false;
            }
            $this->editTeamRoomUser($firstUser);
        }
    }

    public function getTeamRoomUserInfoList($userId, $teamId, $code, $page, $limit)
    {
        $teamCode = TeamRoomManagerService::getInstance()->getTeamRoomManagerByCode($code);
        if (empty($teamCode)) {
            throw new MyException(Code::FAIL, "二维码无效");
        }
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user['common_code'];
        $userCommon = UserCommonService::getInstance()->getUserCommonById($commonCode);
        $phone = $userCommon['phone'];
        TeamRoomManagerService::getInstance()->updateStatus($code, $commonCode, $phone);
        $roomTypeList = [];
        $manCount = 0;
        $womanCount = 0;
        $joinCount = 0;
        $team = TeamRoomService::getInstance()->getTeamRoomById($teamId);
        $shopId = $team['shop_id'];
        $shop = ShopService::getInstance()->getShopById($shopId);
        $shopName = $shop['shop_name'];
        $userList = $this->getList($teamId, null, $page, $limit);
        foreach ($userList as $user) {
            $billId = $user['bill_id'];
            $commonCode = $user['common_code'];
            $identification_number = $user['identification_number'];
            $authentication = $user['authentication'];
            $billStatus = 3;
            $user_status = -2;
            $gender = $user['gender'];
            $roomNumber = null;
            $roomTypeName = null;
            if ($billId) {
                $bill = RoomBillService::getInstance()->getBillById($billId);
                $billStatus = $bill['bill_status'];
                $roomId = $bill['room_id'];
                $roomTypeId = $bill->room_type_id;
                if ($commonCode) {
                    $user_status = 0;
                    $joinCount++;
                    $roomBillUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($billId, $commonCode);
                    if ($roomBillUser) {
                        $user_status = $roomBillUser->user_status;
                    }
                    if ($user_status == 1) {
                        if ($identification_number) {
                            $user_status = 0;
                        }
                        if ($authentication) {
                            $user_status = 1;
                        }
                    }
                }
                if ($roomTypeId) {
                    $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                    $roomTypeName = $roomType['name'];
                    if (!in_array($roomTypeId, $roomTypeList)) {
                        $roomTypeList[] = ['name' => $roomTypeName, 'count' => 1];
                    } else {
                        $roomTypeList[$roomTypeName]['count']++;
                    }
                }
                if ($roomId) {
                    $room = RoomService::getInstance()->getRoomById($roomId);
                    $roomNumber = $room['room_number'];
                }
                if ($gender == 1) {
                    $manCount++;
                } elseif ($gender == 2) {
                    $womanCount++;
                }
            }
            $user['bill_status'] = $billStatus;
            //未报名，已采集，已认证，入住中，已退房
            $user['user_status'] = $user_status;
            $user['room_number'] = $roomNumber;
            $user['room_type_name'] = $roomTypeName;
        }
        $roomTypeCount = count($roomTypeList);
        $count = $this->getCount($teamId, null);
        $team['user_list'] = $userList;
        $team['user_count'] = $count;
        $team['man_count'] = $manCount;
        $team['woman_count'] = $womanCount;
        $team['room_type_count'] = $roomTypeCount;
        $team['room_type'] = $roomTypeList;
        $team['join_count'] = $joinCount;
        $team['shop_name'] = $shopName;
        return $team;
    }

    public function downLoadUserExcel($teamId)
    {
        $roomTypeList = [];
        $team = TeamRoomService::getInstance()->getTeamRoomById($teamId);
        $shopId = $team['shop_id'];
        $enterTimePlan = $team['enter_time_plan'];
        $leaveTimePlan = $team['leave_time_plan'];
        $enterTimeDate = date('Y-m-d H:i', $enterTimePlan);
        $leaveTimeDate = date('Y-m-d H:i', $leaveTimePlan);
        $userList = $this->getList($teamId, null, null, null);
        foreach ($userList as $user) {
            $billId = $user['bill_id'];
            $commonCode = $user['common_code'];
            $authentication = $user['authentication'];
            $billStatus = 3;
            $user_status = -2;
            $roomNumber = null;
            $roomTypeName = null;
            if ($billId) {
                $bill = RoomBillService::getInstance()->getBillById($billId);
                $billStatus = $bill['bill_status'];
                $roomId = $bill['room_id'];
                $roomTypeId = $bill->room_type_id;
                if ($billStatus == RoomBillStatus::STAYING || $billStatus == RoomBillStatus::TO_CHECK_OUT) {
                    $enterTimeDate = date('Y-m-d H:i', $bill->enter_time);
                    $leaveTimeDate = date('Y-m-d H:i', $bill->leave_time_plan);
                } elseif ($billStatus == RoomBillStatus::FINISHED) {
                    $enterTimeDate = date('Y-m-d H:i', $bill->enter_time);
                    $leaveTimeDate = date('Y-m-d H:i', $bill->leave_time);
                }
                //['未报名', '已采集', '已认证', '入住中', '已退房'];
                if ($commonCode) {
                    $roomBillUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($billId, $commonCode);
                    if ($roomBillUser) {
                        $user_status = $roomBillUser['user_status'];
                    } else {
                        if ($authentication) {
                            $user_status = 0;
                        }
                    }
                }
                if ($roomTypeId) {
                    $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                    $roomTypeName = $roomType['name'];
                    if (!in_array($roomTypeId, $roomTypeList)) {
                        $roomTypeList[] = ['name' => $roomTypeName, 'count' => 1];
                    } else {
                        $roomTypeList[$roomTypeName]['count']++;
                    }
                }
                if ($roomId) {
                    $room = RoomService::getInstance()->getRoomById($roomId);
                    $roomNumber = $room['room_number'];
                }
            }
            $user['bill_status'] = $billStatus;
            //未报名，已采集，已认证，入住中，已退房
            $user['user_status'] = $user_status;
            $user['room_number'] = $roomNumber;
            $user['room_type_name'] = $roomTypeName;
        }
        $dataName = [
            "姓名", "房间号", "电话", "入住状态", "入住时间", "退房时间", "备注",
        ];
        $lineData = [];
        //未报名，已采集，已认证，入住中，已退房
        $userStatusNameList = ['未报名', '已采集', '已认证', '入住中', '已退房'];
        foreach ($userList as $itemUser) {
            $lineData[] = [$itemUser['name'], $itemUser['room_number'], $itemUser['phone'], $userStatusNameList[$itemUser['user_status'] + 2], $enterTimeDate, $leaveTimeDate, null];
        }
        $fileName = "入住单_" . date('YmdHis') . ".xlsx";
        $savePath = Config::get('RESOURCE_PATH') . DS . "excel";
        if (!file_exists($savePath)) {
            mkdir($savePath, 0777, true);
        }
        ExcelUtil::getInstance()->createExcel("团队入住名单", $dataName, $lineData, $fileName, $savePath);
        $path = Config::get("RESOURCE_URL") . DS . "excel";
        $fileUrl = $path . DS . $fileName;
        return $fileUrl;
    }


}