<?php

namespace app\admin\controller;

use app\common\filter\PController;
use app\common\returnConfig\Code;
use app\common\returnConfig\Msg;
use app\common\returnConfig\ResultData;
use app\common\service\Pay\sxf\SxfPayService;
use app\common\service\sxf\SxfConfigService;
use app\common\service\sxf\SxfIncomeNotifyService;
use app\common\service\sxf\SxfLedgerService;
use app\common\service\sxf\SxfReamNameApplyService;
use app\common\service\sxf\SxfShopService;
use app\common\utils\CmsPost\AgencyPost;
use app\common\utils\dataDictionary\WxNotify;
use think\Db;
use think\Exception;
use think\Request;

class Sxf extends PController
{
    /**
     * 生成进件url
     * @param Request $request
     * @return void
     */
    public function createIncomeUrl(Request $request)
    {
        $pageType = $request->param('page_type');
        $incomeMode = $request->param('income_mode');
        $incomeCallbackUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::SXF_INCOME_NOTIFY;
        $shopId = $this->admin_info['shop_id'];
        $specifyWechatChannel = $request->param('specify_wechat_channel');
        $qualificationType = $request->param('qualification_type');
        $mccCode = $request->param('mcc_code');
        $mno = $request->param('mno');
        $businessRule = $request->param('business_rule');
        $qrcodeList = $request->param('qrcode_list/a');
        $bankCardRates = $request->param('bank_card_rates/a');
        Db::startTrans();
        try {
            $res = SxfIncomeNotifyService::getInstance()->addSxfIncomeApplyRecord($shopId, $mno, $pageType, $incomeMode, $incomeCallbackUrl, $specifyWechatChannel, $qualificationType, $mccCode, $businessRule, $qrcodeList, $bankCardRates);
            Db::commit();
            ResultData::complexResult($res, Code::SUCCESS, Msg::success());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }


    /**
     * 查询进度
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function queryRecord(Request $request)
    {
        $pageApplyId = $request->param('page_apply_id');
        $res = SxfIncomeNotifyService::getInstance()->queryIncome($pageApplyId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * @param Request $request
     * @return void
     */
    public function queryRecordList(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $page = $request->param('page');
        $limit = $request->param('limit');
        $mno = $request->param('mno');
        $taskStatus = $request->param('task_status');
        $pageType = $request->param('page_type');
        $res = SxfIncomeNotifyService::getInstance()->getSxfIncomeNotifyList($shopId, $mno, $taskStatus, "01", $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }


    /**
     * 更新子商户号
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function updateSubAppId(Request $request)
    {
        $subAppId = $request->param('sub_app_id');
        $shopId = $this->admin_info['shop_id'];
        $shopIdZong = $this->admin_info['shop_id_zong'];
        SxfShopService::getInstance()->updateSubAppid($shopIdZong, $shopId, $subAppId);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }


    public function getSxfShopMno(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $res = SxfShopService::getInstance()->getSxfByShopId($shopId);
        if ($res && ($res['wx_auth'] == 0 || $res['zfb_auth'] == 0)) {
            SxfShopService::getInstance()->queryGrantStatus($shopId);
        }
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * 手动配置商户号
     * @param Request $request
     * @return void
     */
    public function editShopMno(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $mno = $request->param('mno');
        $customerId = $this->admin_info['id'];
        SxfShopService::getInstance()->adminUpdateSxfShop($customerId, $shopId, $mno);
        ResultData::simpleResult(Code::SUCCESS, Msg::getSuccess());
    }

    public function getSxfMchInfo()
    {
        $shopId = $this->admin_info['shop_id'];
        $res = SxfPayService::getInstance()->getSxfMnoInfo($shopId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * 微信实名认证
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function wxRealNameApply(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $contactName = $request->param('contact_name');
        $phoneNumber = $request->param('phone_number');
        $contactIdNo = $request->param('contact_id_no');
        $identityType = $request->param('identity_type');
        $contactPositivePic = $request->param('contact_positive_pic');
        $contactOppositePic = $request->param('contact_opposite_pic');
        $contactPersonLicStt = $request->param('contact_person_lic_stt');
        $contactPersonLicEnd = $request->param('contact_person_lic_end');
        $legalPersonAddress = $request->param('legal_person_address');
        $type = 1;
        SxfShopService::getInstance()->realNameApply($type, $shopId, $contactName, $phoneNumber, $contactIdNo, $identityType,
            $contactPositivePic, $contactOppositePic, $contactPersonLicStt, $contactPersonLicEnd,
            $legalPersonAddress);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }

    /**
     * 实名
     * @param Request $request
     * @return void
     */
    public function getRealNameApplyList(Request $request)
    {
        $page = $request->param('page');
        $limit = $request->param('limit');
        $shopId = $this->admin_info['shop_id'];
        $type = $request->param('type');
        $res = SxfReamNameApplyService::getInstance()->getSxfReamNameApplyList($shopId, $type, $page, $limit);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * 查询申请
     * @param Request $request
     * @return void
     */
    public function queryRealNameApply(Request $request)
    {
        $applyNo = $request->param('apply_no');
        $res = SxfReamNameApplyService::getInstance()->wxQueryApplyInfo($this->admin_info['shop_id'], $applyNo);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * 支付宝实名认证
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function zfbRealNameApply(Request $request)
    {
        $shopId = $this->admin_info['shop_id'];
        $contactName = $request->param('contact_name');
        $phoneNumber = $request->param('phone_number');
        $contactIdNo = $request->param('contact_id_no');
        $identityType = $request->param('identity_type');
        $type = 2;
        SxfShopService::getInstance()->aliRealNameApply($type, $shopId, $contactName, $phoneNumber, $contactIdNo, $identityType);
        ResultData::simpleResult(Code::SUCCESS, Msg::editSuccess());
    }


    /**
     * 查询申请
     * @param Request $request
     * @return void
     */
    public function queryZfbRealNameApply(Request $request)
    {
        $applyNo = $request->param('apply_no');
        $res = SxfReamNameApplyService::getInstance()->zfbQueryApplyInfo($this->admin_info['shop_id'], $applyNo);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }


    public function getSuiXingFuRateGroupDetailByShop(Request $request)
    {
        $param = $request->param();
        return AgencyPost::getInstance()->getSuiXingFuRateGroupDetailByShop($param);
    }

    public function getCustomerSuiXingFu(Request $request)
    {
        $customerId = $this->admin_info['id'];
        return SxfConfigService::getInstance()->getSysOrgId($customerId);
    }

    /**
     * 文件上传
     * @param Request $request
     * @return void
     */
    public function updateFile(Request $request)
    {
        $customerId = $this->admin_info['id'];
        $file = $request->file('file');
        $res = SxfPayService::getInstance()->uploadFilePic($customerId, $file);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());

    }


    /**
     * 商户分账申请提交
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function mnoCommitApply(Request $request)
    {
        $customerId = $this->admin_info['id'];
        $mno = $request->param('mno');
        $applicationType = 2;
        $accountRatio = $request->param('account_ratio');
        $splitAccounts = $request->param('split_accounts/a');
        $agreementPicStr = $request->param('agreement_pic_str');
        $scenesPicStr = $request->param('scenes_pic_str');
        $ledgerProtocolPicStr = $request->param('ledger_protocol_pic_str');
        $otherPicStr = $request->param('other_pic_str');
        $remark = $request->param('remark');
        $res = SxfLedgerService::getInstance()->mnoCommitApply($customerId, $mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
            $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    public function querySpecialApplicationApplyInfo(Request $request)
    {
        $customerId = $this->admin_info['id'];
        $applyId = $request->param('apply_id');
        $res = SxfLedgerService::getInstance()->querySpecialApplicationApplyInfo($customerId, $applyId);
        ResultData::complexResult($res, Code::SUCCESS, Msg::success());
    }

    /**
     * @param Request $request
     * @return void
     * @throws \app\common\sysConfig\MyException
     */
    public function specialApplicationBackApplyBill(Request $request)
    {
        $customerId = $this->admin_info['id'];
        $applyId = $request->param('apply_id');
        SxfLedgerService::getInstance()->specialApplicationBackApplyBill($customerId, $applyId);
        ResultData::simpleResult(Code::SUCCESS, Msg::getSuccess());
    }

}