<?php

namespace app\common\service\Pay\sxf;

use app\common\returnConfig\Code;
use app\common\service\resource\ResourceService;
use app\common\service\resource\ResourceTypeService;
use app\common\sysConfig\MyException;
use app\common\utils\dataDictionary\WxNotify;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use GuzzleHttp\Client;
use think\Config;
use think\File;
use think\Request;

class SxfRequestUtil
{

    //api文档地址 https://paas.tianquetech.com/docs/#/api/tpzlsc1
    private $orgId;
    private $privateKey;
//    private $publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjo1+KBcvwDSIo+nMYLeOJ19Ju4ii0xH66ZxFd869EWFWk/EJa3xIA2+4qGf/Ic7m7zi/NHuCnfUtUDmUdP0JfaZiYwn+1Ek7tYAOc1+1GxhzcexSJLyJlR2JLMfEM+rZooW4Ei7q3a8jdTWUNoak/bVPXnLEVLrbIguXABERQ0Ze0X9Fs0y/zkQFg8UjxUN88g2CRfMC6LldHm7UBo+d+WlpOYH7u0OTzoLLiP/04N1cfTgjjtqTBI7qkOGxYs6aBZHG1DJ6WdP+5w+ho91sBTVajsCxAaMoExWQM2ipf/1qGdsWmkZScPflBqg7m0olOD87ymAVP/3Tcbvi34bDfwIDAQAB';
    private $publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCOmsrFtFPTnEzfpJ/hDl5RODBxw4i9Ex3NmmG/N7A1+by032zZZgLLpdNh8y5otjFY0E37Nyr4FGKFRSSuDiTk8vfx3pv6ImS1Rxjjg4qdVHIfqhCeB0Z2ZPuBD3Gbj8hHFEtXZq8+msAFu/5ZQjiVhgs5WWBjh54LYWSum+d9+wIDAQAB';
    public static $hwxOrgId = "18324121";//18324121
    public static $hwxPrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCyRs7/d2B4fA88jyvwVv2qqMLHiHfsnKKD41JFCxYIlISBBkD3dwY4VzPx1EKz9GW1e9R8XdcMizB5cMODwJU+/HghTSgMn7JXIJ51zRWd2LMzuxhDEncj1U97CzXTz9mCN/BpCTOropCaqplHmT2afvMaID1giZb7rRdkDHhFoYkS2aB/uCxNfiVVUO1F2L9IgvWXvnVdxurv15L8W4upEMb00WlxNwWEoZlay88KZjvA+JObrM8gMx+512Y736fBAt1t8g5Et8EHZ3JoDTBC7XK66h1WP6Av+EOFA8CcbRkb2VpSbtrn3tb9L1D1Iznv6DjrSBqH6y+4vJhWGfBZAgMBAAECggEBAK4MoWUaOC4I+JsoMD6Zv1gRIPDLFEIF/kgC9DwCAcXosaCB6YVwR7tTr5wGngSws2fGeIHe3+gYMAAwDkGgEFWJCQOCH3qLowKbDvl1ewg7G2GC5MH+0iBCzjETlb6wKuuOD4GMgUytDr5hlfon6tPd4xt7pFCOo8AckjyUTAu1cOhSQuSPcfsekmcgySv9x5tR+oijMSuq4ql991jA5wqgzvWHCTmWj8XmYm0OgFKGkNnqAkpJuWNReTnLUarobfOgP7ZO0Zy4UADEMHGv0n1269hCl1HOZyO+nb0Q/EflSd50mGmrIajh6RQD8RqzoOcLPVVyRmSAiS9TABgOUR0CgYEA7bFTxTXx5RA381EfgRqswY9bYjXxovF0jrmoMPfUirBmbDiK8O6UT4jnFX+vDSYYwUt7AV57Q4T2Tto+K9ar1LsY35CObcgwCw3Zs8ur49Wqgb+trce1oTFzKB+xd4YFm82+nzOyfyzi5ypF304uOjZqCdPc8FiNu7UDwCeNqusCgYEAwAHz68TvEy/wwSz0g14hK3qgrSymrC9/UZ6M4FkuTAuQJSbhLkrGCvXFNmzyhk/W9A+zNWvTa/AUpaYc9AgfDzHiiUyzWzzmygksTkwqa1Rx/A48QeUjoAY/Of07Gx+wPeKhSIAkuo43y/+n26lpJr+Nz4LnnEjClM/S0logOMsCgYBfmouPLvt0nuliPKbnTstdr0OaZQgj+epf3d0AlS4v+nO346Y+8jdFJlOMGidWqpQaazm2e0IpDjnjzXRkPZwvnHGMuqAPGA365D9ESg+BPFewiGBbHZC4nSMsaFMBOC7ZVA/wCOMqs+sv/5jLEK8XSvurqEOdiIC5lz1C9KXOsQKBgQCYXjRZ6bqq6EVOlsUmpoD+Bt8Q7bkKNNr9QyuaMUsbD6YAxXy2hqHoOqaYjRZcXWk/3mGhSALLygYoeczKHtO+9CBIQnSmN4sR0vu02p5i8+1MT2OkCxkdOTfdZcYMvTXXtku0w/Phk1BV83gaSbLnC+WWQdRYYij/fHhZcynUxwKBgAjsFSnUOsQMcea1es0uVs2+lKnkJXnIBOszTVWLsr9LEMaeYAiVBS/4LuGsF8bVmeztApUHJTL5AajROiG1apFw8zlJtyejKikYbcClKMVnjOYDu0yYa1u8uaF7B3PWlto8/scumoxim8Sd6qVM+OYwu2kSqYgnbyL1fqUyaKRL";
    public static $hwxMno = "399200427027644";


    public function __construct($orgId, $privateKey)
    {
        $this->orgId = $orgId;
        if (!$privateKey) {
            $this->privateKey = self::$hwxPrivateKey;
        } else {
            $this->privateKey = $privateKey;
        }

    }

    public static $API_URL = 'https://openapi.tianquetech.com';
//    public static $API_URL = 'https://openapi-test.tianquetech.com';


    public static $API_ARR = [
        'income' => '/merchant/income',
        'editMerchantInfo' => '/merchant/editMerchantInfo',
        'queryMerchantInfo' => '/merchant/queryMerchantInfo',
        'queryModifyResult' => '/merchant/queryModifyResult',
        'addConf' => '/merchant/weChatPaySet/addConf',
        'queryConf' => '/merchant/weChatPaySet/queryConf',
        'openElecSignature' => '/merchant/elecSignature/openElecSignature',
        'commitApply' => '/merchant/realName/commitApply',
        'queryApplyInfo' => '/merchant/realName/queryApplyInfo',
        'jsapiScan' => '/order/jsapiScan',
        'appletScanPre' => '/order/appletScanPre',
        'tradeQuery' => '/query/tradeQuery',
        'refund' => '/order/refund',
        'getUrl' => '/merchant/sign/getUrl',
        'merchantSetup' => '/merchant/merchantSetup',
        'reverseScan' => '/order/reverseScan',
        'getSubOPenid' => '/query/getSubOPenid',
        'activePlusScan' => '/order/activePlusScan',
        'aliCommitApply' => '/merchant/alipayRealName/commitApply',
        'aliQueryApplyInfo' => '/merchant/alipayRealName/queryApplyInfo',
        'specialCommitApply' => '/merchant/specialApplication/commitApply',
        'specialQueryApplyInfo' => '/merchant/specialApplication/queryApplyInfo',
        'specialBackApplyBill' => '/merchant/specialApplication/backApplyBill',
        'createUrl' => '/merchant/createUrl',//获取进件链接
        'queryPageMerchantInfo' => '/merchant/queryPageMerchantInfo',//审核结果查询
        'merchantInfoQuery' => '/merchant/merchantInfoQuery',//商户信息查询
        'queryGrantStatus' => '/merchant/realName/queryGrantStatus',//
        'aliQueryGrantStatus' => '/merchant/alipayRealName/queryGrantStatus',
        'getSignUrl' => '/merchant/sign/getUrl',//分账协议签署
        'querySignContract' => '/merchant/sign/querySignContract',//分账协议签署结果查询
        'setMnoArray' => '/query/ledger/setMnoArray',//分账设置
        'launchLedger' => '/query/ledger/launchLedger',//订单分账
        'queryLedgerAccount' => '/query/ledger/queryLedgerAccount',//订单分账查询
        'ledgerBack' => '/query/ledger/ledgerBack',//分账退回
        'queryLedgerAmt' => '/query/ledger/queryLedgerAmt',//分账交易金额查询
        'ledgerCommitApply' => '/merchant/specialApplication/commitApply',//分账申请
        'uploadPicture' => '/merchant/uploadPicture',//图片资料上传
        'querySpecialApplicationApplyInfo' => '/merchant/specialApplication/queryApplyInfo',//商户特殊申请查询
        'specialApplicationBackApplyBill' => '/merchant/specialApplication/backApplyBill',//商户特殊申请撤销
    ];

    /**
     * 签名
     * @param $reqData
     * @param $orgId
     * @param $privateKey
     * @param string $signType
     * @return array
     */
    private function generateSign($reqData, string $signType = "RSA")
    {
        if (!$signType) $signType = 'RSA';
        $data = [];
        $data['signType'] = $signType;
        $data['version'] = '1.0';
        $data['orgId'] = $this->orgId;
        $data['reqId'] = date('YmdHis') . StringUtil::getRandomStrs(6);
        $data['reqData'] = $reqData;
        $data['timestamp'] = date('YmdHis');
        $rsaPriKeyStr = $this->privateKey;
        $stringToBeSigned = $this->getSignContent($data);
        //按照指定长度对字符串进行折行处理
        $res = $this->format_secret_key($rsaPriKeyStr, 'pri');
        if ("RSA2" == $signType) {
            $signstatus = openssl_sign($stringToBeSigned, $sign, $res, OPENSSL_ALGO_SHA256);
        } else {
            $signstatus = openssl_sign($stringToBeSigned, $sign, $res);
        }
        if ($signstatus === false) {
            DebugUtil::log($signstatus, 'error');
        }
        $data['sign'] = base64_encode($sign);
        return $data;
    }

    /**
     * 拼接签名字符串
     * @param $params
     * @return false|string
     */
    public function getSignContent($params)
    {
        if (isset($params['sign'])) {
            unset($params['sign']);
        }
        ksort($params);
        $stringToBeSigned = "";
        foreach ($params as $k => $v) {
            $isarray = is_array($v);
            if ($isarray) {
                $stringToBeSigned .= "$k" . "=" . json_encode($v, 320) . "&";
            } else {
                $stringToBeSigned .= "$k" . "=" . "$v" . "&";
            }
        }
        unset ($k, $v);
        $stringToBeSigned = substr($stringToBeSigned, 0, strlen($stringToBeSigned) - 1);
        return $stringToBeSigned;
    }

    public function verifySign($data)
    {
        $oldSign = $data['sign'];
        $paramStr = $this->getSignContent($data);
//        DebugUtil::log(json_encode($data));
//        DebugUtil::log($paramStr);
        return $this->verify($paramStr, $oldSign, $this->publicKey);
    }

    private function format_secret_key($secret_key, $type)
    {
        // 64个英文字符后接换行符"\n",最后再接换行符"\n"
        $key = (wordwrap($secret_key, 64, "\n", true)) . "\n";
        // 添加pem格式头和尾
        if ($type == 'pub') {
            $pem_key = "-----BEGIN PUBLIC KEY-----\n" . $key . "-----END PUBLIC KEY-----\n";
        } else if ($type == 'pri') {
            $pem_key = "-----BEGIN RSA PRIVATE KEY-----\n" . $key . "-----END RSA PRIVATE KEY-----\n";
        } else {
            throw new MyException(Code::FAIL, "格式化正式错误");
        }
        return $pem_key;
    }

    private function verify($paramStr, $sign, $rsaPublicKey)
    {
        // $pubKey = $this->$rsaPublicKey;
        //将字符串格式公私钥转为pem格式公私钥
        $pubKeyPem = $this->format_secret_key($rsaPublicKey, 'pub');
        //转换为openssl密钥，必须是没有经过pkcs8转换的公钥
        $res = openssl_get_publickey($pubKeyPem);
        //url解码签名
        //$signUrl = urldecode($sign);
        //base64解码签名
        $signBase64 = base64_decode($sign);
        //调用openssl内置方法验签，返回bool值
        $result = (bool)openssl_verify($paramStr, $signBase64, $res);
        //释放资源
        openssl_free_key($res);
        //返回资源是否成功
        return $result;
    }

    private function sendPost($apipath, $reqData, $headers = array(), $timeout = 60, $flag = 0)
    {
        $url = self::$API_URL . self::$API_ARR[$apipath];
        $data = $this->generateSign($reqData);
        $reqStr = json_encode($data);
        DebugUtil::log($reqStr);
        DebugUtil::log($url);
        $ch = curl_init();
        if (!$flag) curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $reqStr);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        // 设置请求的最长执行时间为 30 秒
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        if (!$headers) {
            $headers = ['content-type:application/json;charset=UTF-8'];
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        try {
            $response = curl_exec($ch);
        } catch (\Exception $e) {
            throw new MyException(Code::FAIL, "请求错误");
        }
        $curlError = curl_error($ch);
        if (!empty($curlError)) {
            return null;
        }
        curl_close($ch);
        if (empty($response)) {
            return null;
        }
        DebugUtil::log($response);
        $res = json_decode($response, true);
        if (!isset($res['code'])) {
            throw new MyException(Code::FAIL, "请求错误");
        }
        if (!isset($res['respData'])) {
            throw new MyException(Code::FAIL, $res['msg']);
        }
        return $res;
    }


    /**
     * 图片资料上传
     * @param string $apipath API路径
     * @param File $file 上传的文件
     * @param string $pictureType 图片类型码
     * @return array 返回上传结果
     * @throws MyException
     */
    private function uploadFile($apipath, $filePath, $fileName, $pictureType)
    {


        $url = self::$API_URL . self::$API_ARR[$apipath];
        $client = new Client();

        try {
            $response = $client->request("POST", $url, [
                'multipart' => [
                    [
                        'name' => 'orgId',
                        'contents' => $this->orgId
                    ],
                    [
                        'name' => 'reqId',
                        'contents' => date('YmdHis') . StringUtil::getRandomStrs(6)
                    ],
                    [
                        'name' => 'pictureType',
                        'contents' => $pictureType
                    ],
                    [
                        'name' => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => $fileName// 使用原始文件名
                    ]
                ],
                'headers' => [
                    'User-Agent' => 'Hotel-PMS/1.0'
                ],
                'timeout' => 60 // 设置超时时间
            ]);

            $responseBody = $response->getBody()->getContents();
//            DebugUtil::log("上传响应: " . $responseBody);

            // 解析响应
            $result = json_decode($responseBody, true);
            if (!$result) {
                throw new MyException(Code::FAIL, "响应解析失败");
            }
            return $result;
        } catch (\Exception $e) {
            throw new MyException(Code::FAIL, "文件上传失败: " . $e->getMessage());
        }
    }

    /**
     * 获取进件链接
     * @param $mno
     * @param $pageType
     * @param $incomeMode
     * @param $incomeCallbackUrl
     * @return bool|string|null
     * @throws MyException
     */
    public function createUrl($mno, $pageType, $incomeMode, $incomeCallbackUrl, $specifyWechatChannel, $qualificationType, $mccCode, $businessRule, $qrcodeList, $bankCardRates)
    {
        $data = [
            'pageType' => $pageType,
            'incomeMode' => $incomeMode,
            'incomeCallbackUrl' => $incomeCallbackUrl,
        ];
        if ($mno) {
            $data['mno'] = $mno;
        }
        if ($specifyWechatChannel) {
            $data['specifyWechatChannel'] = $specifyWechatChannel;
        }
        if ($qualificationType) {
            $data['qualificationType'] = $qualificationType;
        }
        if ($mccCode) {
            $data['mccCode'] = $mccCode;
        }
        if ($businessRule) {
            $data['businessRule'] = $businessRule;
        }
        if ($qrcodeList) {
            $qrList = [];
            foreach ($qrcodeList as $qrcode) {
                $qrList[] = ['rate' => $qrcode['rate'], 'rateType' => $qrcode['rate_type']];
            }
            $data['qrcodeList'] = $qrList;
        }
        if ($bankCardRates) {
            $data['bankCardRates'] = $bankCardRates;
        }
        $res = $this->sendPost('createUrl', $data);
        $respData = $res['respData'];
        $bizCode = $respData['bizCode'];
        if ($bizCode != '0000') {
            throw new MyException(Code::FAIL, "错误码：" . $bizCode . ';原因：' . $respData['bizMsg']);
        }
        return $respData;
    }


    /**
     * 审核结果查询
     * @param $pageApplyId
     * @return mixed
     * @throws MyException
     */
    public function queryPageMerchantInfo($pageApplyId)
    {
        $data = [
            'pageApplyId' => $pageApplyId,
        ];
        $res = $this->sendPost('queryPageMerchantInfo', $data);
        $respData = $res['respData'];
        $bizCode = $respData['bizCode'];
        if ($bizCode != '0000') {
            if ($bizCode == '0001') {
                throw new MyException(Code::FAIL, "未填写进件信息");
            } else {
                throw new MyException(Code::FAIL, "错误码：" . $bizCode . ';原因：' . $respData['bizMsg']);
            }
        }
        return $respData;
    }

    public function queryMerchantInfo($pageApplyId)
    {
        $data = [
            'pageApplyId' => $pageApplyId,
        ];
        $res = $this->sendPost('queryMerchantInfo', $data);
        $respData = $res['respData'];
        $bizCode = $respData['bizCode'];
        if ($bizCode != '0000') {
            if ($bizCode == '0001') {
                throw new MyException(Code::FAIL, "未填写进件信息");
            } else {
                throw new MyException(Code::FAIL, "错误码：" . $bizCode . ';原因：' . $respData['bizMsg']);
            }
        }
        return $respData;
    }

    /**
     * 支付码
     * @param $mno
     * @param $ordNo
     * @param $amt
     * @param $authCode
     * @param $subject string 标题
     * @param $trmIp string 商户交易终端ip地址
     * @return mixed
     * @throws MyException
     */
    public function reverseScan($mno, $ordNo, $amt, $authCode, $subject, $ledgerRule)
    {
        $request = Request::instance();
        $notifyUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::SXF_REVERSE_SCAN_NOTIFY;
        $trmIp = Request::instance()->ip();
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'amt' => $amt,
            'authCode' => $authCode,
            'scene' => "1",
            'subject' => $subject,
            'trmIp' => $trmIp,
        ];
        if ($ledgerRule) {
            $data['ledgerAccountEffectTime'] = 30;
            $data['fusruleId'] = $ledgerRule;
        }
        if ($notifyUrl) {
            $data['notifyUrl'] = $notifyUrl;
        }
        $res = $this->sendPost('reverseScan', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 主码
     * @param $mno
     * @param $ordNo
     * @param $amt
     * @param $authCode
     * @param $subject
     * @param $trmIp
     * @return mixed
     * @throws MyException
     */
    public function activePlusScan($mno, $ordNo, $amt, $payType, $subject, $ledgerRule)
    {
        $request = Request::instance();
        $notifyUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::SXF_ACTIVE_PLUS_SCAN_NOTIFY;
        $trmIp = Request::instance()->ip();
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'amt' => $amt,
//            'payType' => $payType,
            'subject' => $subject,
            'trmIp' => $trmIp,
            'notifyUrl' => $notifyUrl,
        ];
        if ($ledgerRule) {
            $data['ledgerAccountEffectTime'] = 30;
            $data['fusruleId'] = $ledgerRule;
        }
        $res = $this->sendPost('activePlusScan', $data);
        $respData = $res['respData'];
        $bizCode = $respData['bizCode'];
        return $respData;
    }

    /**
     * 交易查询
     * @param $mno
     * @param $ordNo
     * @return mixed
     * @throws MyException
     */
    public function tradeQuery($mno, $ordNo)
    {
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo
        ];
        $res = $this->sendPost('tradeQuery', $data);
        $respData = $res['respData'];
        $bizCode = $respData['bizCode'];
        return $respData;
    }

    /**
     * 小程序支付
     * @param $mno
     * @param $ordNo
     * @param $amt
     * @param $subject
     * @param $userId
     * @return mixed
     * @throws MyException
     */
    public function jsapiScan($mno, $subAppid, $ordNo, $amt, $subject, $userId, $notifyUrl, $extend, $ledgerRule)
    {
        $trmIp = Request::instance()->ip();
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'amt' => $amt,
            'payType' => "WECHAT",
            'payWay' => "03",
            'subject' => $subject,
            'subAppid' => $subAppid,
            'trmIp' => $trmIp,
            'userId' => $userId,
            'extend' => $extend,
            'notifyUrl' => $notifyUrl,
        ];
        if ($ledgerRule) {
            $data['ledgerAccountEffectTime'] = 30;
            $data['fusruleId'] = $ledgerRule;
        }
        $res = $this->sendPost('jsapiScan', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function appletScanPre($mno, $ordNo, $amt, $subject, $notifyUrl, $extend)
    {
        $request = Request::instance();
        $url = explode('index.php/', $request->url(true))[0] . 'index.php/' . $notifyUrl;
        $trmIp = Request::instance()->ip();
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'amt' => $amt,
            'appletSource' => "01",
            'subject' => $subject,
            'trmIp' => $trmIp,
            'notifyUrl' => $url,
            'extend' => $extend,
        ];
        $res = $this->sendPost('appletScanPre', $data);
        if (!isset($res['respData'])) {
            throw new MyException(Code::FAIL, '原因：' . $res['msg']);
        }
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 退款
     * @param $mno
     * @param $ordNo
     * @param $origOrderNo
     * @param $amt
     * @param $refundReason
     * @return mixed
     * @throws MyException
     */
    public function refund($mno, $ordNo, $origOrderNo, $amt, $refundReason)
    {
        $request = Request::instance();
        $notifyUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::SXF_REFUND_NOTIFY;
        $trmIp = Request::instance()->ip();
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'origOrderNo' => $origOrderNo,
            'amt' => $amt,
            'payType' => "WECHAT",
            'refundReason' => $refundReason,
            'notifyUrl' => $notifyUrl,
            'trmIp' => $trmIp,
        ];
        $res = $this->sendPost('refund', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function getSubOPenid($mno, $subMchId, $subOpenId, $authCode)
    {
        $data = [
            'mno' => $mno,
            'subMchId' => $subMchId,
            'subOpenId' => $subOpenId,
            'authCode' => $authCode,
        ];
        $res = $this->sendPost('getSubOPenid', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 商户信息查询
     * @param $mno
     * @return mixed
     * @throws MyException
     */
    public function merchantInfoQuery($mno)
    {
        $data = [
            'mno' => $mno
        ];
        $res = $this->sendPost('merchantInfoQuery', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 实名认证
     * @param $mno
     * @return mixed
     */
    public function commitApply($mno, $contactName, $phoneNumber, $contactIdNo, $identityType, $contactPositivePic,
                                $contactOppositePic, $contactPersonLicStt, $contactPersonLicEnt, $legalPersonAddress)
    {
        $request = Request::instance();
        $notifyUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::REAL_NAME_NOTIFY;
        $data = [
            'mno' => $mno
        ];
        if ($contactName) {
            $data['contactName'] = $contactName;
        }
        if ($phoneNumber) {
            $data['phoneNumber'] = $phoneNumber;
        }
        if ($contactIdNo) {
            $data['contactIdNo'] = $contactIdNo;
        }
        if ($identityType) {
            $data['identityType'] = $identityType;
        }
        if ($contactPositivePic) {
            $data['contactPositivePic'] = $contactPositivePic;
        }
        if ($contactOppositePic) {
            $data['contactOppositePic'] = $contactOppositePic;
        }
        if ($contactPersonLicStt) {
            $data['contactPersonLicStt'] = $contactPersonLicStt;
        }
        if ($contactPersonLicEnt) {
            $data['contactPersonLicEnt'] = $contactPersonLicEnt;
        }
        if ($legalPersonAddress) {
            $data['legalPersonAddress'] = $legalPersonAddress;
        }
        $data['backUrl'] = $notifyUrl;
        $res = $this->sendPost('commitApply', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 微信实名认证申请结果查询
     * @param $mno
     * @param $wxApplyNo
     * @return mixed
     * @throws MyException
     */
    public function queryApplyInfo($mno, $wxApplyNo)
    {
        $data = [
            'mno' => $mno,
        ];
        if ($wxApplyNo) {
            $data['wxApplyNo'] = $wxApplyNo;
        }
        $res = $this->sendPost('queryApplyInfo', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 微信子商户授权状态查询
     * @param $subMchId
     * @return mixed
     * @throws MyException
     */
    public function queryGrantStatus($subMchId)
    {
        $data = [
            'subMchId' => $subMchId,
        ];
        $res = $this->sendPost('queryGrantStatus', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function aliQueryGrantStatus($subMchId)
    {
        $data = [
            'subMchId' => $subMchId,
        ];
        $res = $this->sendPost('aliQueryGrantStatus', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function queryConf($mno, $subMchId)
    {
        $data = [
            'mno' => $mno,
            'subMchId' => $subMchId,
        ];
        $res = $this->sendPost('queryConf', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 配置appid
     * @param $mno
     * @param $subMchId
     * @param $type
     * @param $subAppid
     * @param $accountType
     * @return mixed
     * @throws MyException
     */
    public function addConf($mno, $subMchId, $type, $subAppid, $accountType)
    {
        $data = [
            'mno' => $mno,
            'subMchId' => $subMchId,
            'type' => $type,
            'subAppid' => $subAppid,
            'accountType' => $accountType,
        ];
        $res = $this->sendPost('addConf', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function aliCommitApply($mno, $contactName, $phoneNumber, $identityType, $contactIdNo)
    {
        $request = Request::instance();
        $notifyUrl = explode('index.php/', $request->url(true))[0] . 'index.php/' . WxNotify::ALI_REAL_NAME_NOTIFY;
        $data = [
            'mno' => $mno
        ];
        if ($contactName) {
            $data['contactName'] = $contactName;
        }
        if ($phoneNumber) {
            $data['phoneNumber'] = $phoneNumber;
        }
        if ($contactIdNo) {
            $data['contactIdNo'] = $contactIdNo;
        }
        if ($identityType) {
            $data['identityType'] = $identityType;
        }
        $data['backUrl'] = $notifyUrl;
        $res = $this->sendPost('aliCommitApply', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 支付宝实名认证申请结果查询
     * @param $mno
     * @param $wxApplyNo
     * @return mixed
     * @throws MyException
     */
    public function queryAliApplyInfo($mno, $zfbApplyNo)
    {
        $data = [
            'mno' => $mno,
        ];
        if ($zfbApplyNo) {
            $data['zfbApplyNo'] = $zfbApplyNo;
        }
        $res = $this->sendPost('aliQueryApplyInfo', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * @param $mno
     * @param $singType 签约类型，枚举值
     * 00：接口签约，调用成功后，返回签约地址，商户需访问该地址进行签约
     * 01：短信签约，调用成功后，我们将签约地址通过短信发送给商户，商户在短信内点开链接完成签约
     * 02：线下签约，需先调用图片上传接口上传协议图片，然后在此接口上传图片信息，我司进行审核
     * @return mixed
     * @throws MyException
     */
    public function getSignUrl($mno, $singType)
    {
        $data = [
            'mno' => $mno,
            'signType' => $singType,
        ];
        $res = $this->sendPost('getSignUrl', $data);
        $respData = $res['respData'];
        return $respData;
    }

    public function querySignContract($mno)
    {
        $data = [
            'mno' => $mno,
        ];
        $res = $this->sendPost('querySignContract', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 分账设置
     * @param $mno
     * @param $mnoArray
     * @return mixed
     * @throws MyException
     */
    public function setMnoArray($mno, $mnoArray)
    {
        $data = [
            'mno' => $mno,
            'mnoArray' => $mnoArray,
        ];
        $res = $this->sendPost('setMnoArray', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 订单分账
     * @param $mno
     * @param $ordNo
     * @param $uuid
     * @param $ledgerAccountFlag
     * @param $ledgerRule
     * @param $notifyAddress
     * @param $thirdPartyUuid
     * @return mixed
     * @throws MyException
     */
    public function launchLedger($mno, $ordNo, $uuid, $ledgerAccountFlag, $ledgerRule, $notifyAddress, $thirdPartyUuid)
    {
        $data = [
            'mno' => $mno,
            'ordNo' => $ordNo,
            'uuid' => $uuid,
            'ledgerAccountFlag' => $ledgerAccountFlag,
            'ledgerRule' => $ledgerRule,
            'notifyAddress' => $notifyAddress,
        ];
        if ($thirdPartyUuid) {
            $data['thirdPartyUuid'] = $thirdPartyUuid;
        }
        $res = $this->sendPost('launchLedger', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 分账结果查询
     * @param $mno
     * @param $ordNo
     * @param $uuid
     * @param $ledgerUuid
     * @return mixed
     * @throws MyException
     */
    public function queryLedgerAccount($mno, $ordNo, $uuid, $ledgerUuid)
    {
        $data = [
            'mno' => $mno,
        ];
        if ($ordNo) {
            $data['ordNo'] = $ordNo;
        }
        if ($ledgerUuid) {
            $data['ledgerUuid'] = $ledgerUuid;
        }
        if ($uuid) {
            $data['uuid'] = $uuid;
        }
        $res = $this->sendPost('queryLedgerAccount', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 分账退回
     * @param $mno
     * @param $uuid
     * @param $origUuid
     * @param $origLedgerUuid
     * @param $ledgerRule
     * @param $notifyAddress
     * @param $thirdPartyUuid
     * @return mixed
     * @throws MyException
     */
    public function ledgerBack($mno, $uuid, $origUuid, $origLedgerUuid, $ledgerRule, $notifyAddress, $thirdPartyUuid)
    {
        $data = [
            'mno' => $mno,
            'uuid' => $uuid,
            'origUuid' => $origUuid,
            'origLedgerUuid' => $origLedgerUuid,
            'ledgerRule' => $ledgerRule,
            'notifyAddress' => $notifyAddress,
            'thirdPartyUuid' => $thirdPartyUuid,
        ];

        $res = $this->sendPost('ledgerBack', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 分账交易金额查询
     * @param $mno
     * @param $uuid
     * @param $ordNo
     * @return mixed
     * @throws MyException
     */
    public function queryLedgerAmt($mno, $uuid, $ordNo)
    {
        $data = [
            'mno' => $mno,
        ];
        if ($ordNo) {
            $data['ordNo'] = $ordNo;
        }
        if ($uuid) {
            $data['uuid'] = $uuid;
        }

        $res = $this->sendPost('queryLedgerAmt', $data);
        $respData = $res['respData'];
        return $respData;
    }


    public function mnoCommitApply($mno, $applicationType, $accountRatio, $splitAccounts, $agreementPicStr,
                                   $scenesPicStr, $ledgerProtocolPicStr, $otherPicStr, $remark)
    {
        $notifyUrl = explode('index.php/', Request::instance()->url(true))[0] . 'index.php/' . WxNotify::SXF_LEDGER_COMMIT_APPLY_NOTIFY;
        $data = [
            'mno' => $mno,
            'applicationType' => $applicationType,
            'callbackUrl' => $notifyUrl,
        ];
        if ($applicationType == "1") {
            $data['paymentSet'] = "1";
            $data['settTime'] = "00";
            $data['settleAccount'] = "";
            $data['licensePictureUrl'] = "";
            $data['assistPictureUrlStr'] = "";
            $data['operaReason'] = "";
        } else {
            $splitAccountsTemp = [
                'mno' => $splitAccounts['mno'],
                'splitCycle' => $splitAccounts['split_cycle'],
                'relationShip' => $splitAccounts['relation_ship'],
                'scenes' => $splitAccounts['scenes'],
            ];
            $data['accountRatio'] = $accountRatio;
            $data['splitAccounts'] = [$splitAccountsTemp];
            $data['agreementPicStr'] = $agreementPicStr;
            $data['scenesPicStr'] = $scenesPicStr;
            $data['ledgerProtocolPicStr'] = $ledgerProtocolPicStr;
            $data['otherPicStr'] = $otherPicStr;
            $data['remark'] = $remark;
            if ($accountRatio > 80 && empty($ledgerProtocolPicStr)) {
                throw new MyException(Code::FAIL, "合作协议，最大分账比例大于等于80%时必传，最多上传6张，以英文逗号隔开");
            }
        }
        $res = $this->sendPost('ledgerCommitApply', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 上传图片资料
     * @param File $file 上传的文件
     * @param string $pictureType 图片类型码，默认为19
     * 枚举值：19，20，21，22，23，27，84，85，86，87，88，89
     * 上述枚举支持的文件格式为png、jpg、gif、jpeg、bmp、rar、zip、doc、docx、xls、xlsx、pdf格式
     * 其余枚举支持的文件格式为png、jpg、gif、jpeg、bmp格式
     * 图片大小：≤ 2M
     * 注意：gif、jpeg格式会引起微信实名认证报错，建议转化格式
     * @return array 返回上传结果
     * @throws MyException
     */
    public function uploadPicture($shopId,File $file, $pictureType = "19")
    {
        $fileData = ResourceService::getInstance()->uploadFile($file);
        $filePath = $fileData['path'];
        $fileName = $fileData['name'];
        $fileUrl = $fileData['url'];
        $type = $fileData['type'];
        $resourceType = ResourceTypeService::getInstance()->getResourceTypeBySign($shopId,'user');
        $typeId = $resourceType->id;
        $result = $this->uploadFile("uploadPicture", $filePath, $fileName, $pictureType);
        if ($result['code'] == '0000') {
            ResourceService::getInstance()->saveResource($shopId,$fileUrl,$result['respData']['PhotoUrl'],$type,$typeId,1,1,'',0);
            return $result['respData']['PhotoUrl'];
        } else {
            throw new MyException(Code::FAIL, $result['msg']);
        }
    }

    /**
     * 查询特殊申请单详情
     * @param $applyId
     * @return mixed
     * @throws MyException
     */
    public function querySpecialApplicationApplyInfo($applyId)
    {
        $data = [
            'id' => $applyId,
        ];
        $res = $this->sendPost('querySpecialApplicationApplyInfo', $data);
        $respData = $res['respData'];
        return $respData;
    }

    /**
     * 特殊申请退回申请单
     * @param $applyId
     * @return mixed
     * @throws MyException
     */
    public function specialApplicationBackApplyBill($applyId)
    {
        $data = [
            'id' => $applyId,
        ];
        $res = $this->sendPost('specialApplicationBackApplyBill', $data);
        $respData = $res['respData'];
        return $respData;
    }

}