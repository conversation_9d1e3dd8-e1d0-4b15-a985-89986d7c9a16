<?php


namespace app\common\service\resource;

use app\common\model\resource\Resource as ResourceModel;
use app\common\utils\aliyun\AliOss;
use app\common\utils\DebugUtil;
use app\common\utils\FileUtil;
use app\common\utils\tencent\TencentOss;
use think\Config;
use think\File;

class ResourceService
{
	private static $object = null;

	public function __construct() {
	}

	public static function getInstance() {
		//判断类内部的静态属性是否存在对象
		if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
			self::$object = new self();
		}
		//返回对象给外部
		return self::$object;
	}

	public function __clone() {
	}

	public function getResourceList($shopIdZong,$shopId,$typeId, $type,$status, $page,$limit) {
		$model = new ResourceModel();
		$list = $model->getResourceList($shopIdZong,$shopId,$typeId, $type,$status, $page,$limit);
		if ($page && $limit) {
			$count = $this->getResourceCount($shopIdZong,$shopId,$typeId, $type,$status);
			return ['list' => $list, 'count' => $count];
		}
		return $list;
	}
	public function getResourceCount($shopIdZong,$shopId,$typeId, $type,$status) {
		$model = new ResourceModel();
		return $model->getResourceCount($shopIdZong,$shopId,$typeId, $type,$status);
	}



	public function editResource($params) {
		$model = new ResourceModel();
		if (isset($params['id'])) {
			$model->editResource($params);
			$id = $params['id'];
		} else {
			$model->saveResource($params);
			$id = $model->id;
		}
		return $id;
	}


	public function getResourceById($id){
		$model = new ResourceModel();
		return $model->getResourceById($id);
	}
    public function getResourceByName($name){
        $model = new ResourceModel();
        return $model->getResourceByName($name);
    }

	/**
	 * 修改status状态为0
	 * @param $id
	 */
	public function delResource($id){
		$this->editResource(['id'=>$id,'status'=>0]);
	}
	/**
	 * 修改status状态为0
	 * @param $id
	 */
	public function recoverResource($id){
		$this->editResource(['id'=>$id,'status'=>1]);
	}

	public function updateResourceType($id,$typeId){
		$this->editResource(['id'=>$id,'type_id'=>$typeId]);
	}
	/**
	 * 删除数据
	 * @param $id
	 * @return int
	 */
	public function delResourceData($id){
		$model = new ResourceModel();
		return $model->where('id',$id)->delete();
	}

	public function realDelResource($id,$shopIdZong){
        $shopIdZong = 0;
		$savePath = Config::get('RESOURCE_PATH');
		$appUrl = Config::get('APP_URL');
		$resource = $this->getResourceById($id);
		$storage_type = $resource->storage_type;
		$url = $resource->url;
		$savePath = str_replace($appUrl, $savePath, $url);
		if ($storage_type == 1){
			if (file_exists($savePath)){
				unlink($savePath);
			}
		} elseif($storage_type == 2) {
			$aliOss = AliossService::getInstance()->getAliossByShopId($shopIdZong);
			$aliossUtil = new AliOss();
			$aliossUtil->delFile($aliOss->access_key, $aliOss->access_key_secret, $aliOss->end_point,$aliOss->bucket, $url);
		} else {
			$tencentOss = TencentossService::getInstance()->getTencentossByShopId($shopIdZong);
            DebugUtil::log(json_encode($tencentOss));
			$tencentOssUtil = new TencentOss();
			$tencentOssUtil->delObject($tencentOss->secret_id, $tencentOss->secret_key, $tencentOss->region,
				$tencentOss->bucket, $tencentOss->appid,$url);
		}
		$this->delResourceData($id);
	}


	public function saveResource($shopId,$url,$name,$type,$typeId,$level,$storageType,$remark,$isPublic=0){
		if (!$typeId){
			$resourceType = ResourceTypeService::getInstance()->getResourceTypeBySign($shopId,'fwb');
			$typeId = $resourceType->id;
		}
		return $this->editResource(['shop_id'=>$shopId,'url'=>$url,'name'=>$name,'type'=>$type,'type_id'=>$typeId,
			'level'=>$level,'storage_type'=>$storageType,'remark'=>$remark,'is_public'=>$isPublic]);
	}

	public function uploadFile(File $file){
		$savePath = Config::get('RESOURCE_PATH');
		$appUrl = Config::get('RESOURCE_URL');
		$fileData =  FileUtil::getInstance()->uploadFile($file,$savePath);
		$fileData['url'] = $appUrl . $fileData['url'];
		return $fileData;
	}



	/**
	 * 上传文件到云存储
	 * @param $shopIdZong int 总店id
	 * @param $url string 上传本地url
	 * @param $type string 文件类型
	 * @param $name string 文件名称
	 * @param $localPath string 本地存储路径
	 * @return string|string[]
	 * @throws \OSS\Core\OssException
	 * @throws \app\common\sysConfig\MyException
	 */
	public function saveResourceToOss($shopIdZong, $url,$type,$name, $localPath){
		if ($shopIdZong == 0){
			return $this->uploadFileToOss($shopIdZong, $url,$type,$name, $localPath);
		} else {
			$saveType = ResourceStorageSettingService::getInstance()->getSaveType($shopIdZong);
			if ($saveType == 1){
				return $this->uploadFileToOss(0, $url,$type,$name, $localPath);
			} else {
				return $this->uploadFileToOss($shopIdZong, $url,$type,$name, $localPath);
			}
		}
	}


	/**
	 * 文件上传云存储
	 * @param $shopIdZong
	 * @param $url
	 * @param $type
	 * @param $name
	 * @param $localPath
	 * @return array
	 * @throws \OSS\Core\OssException
	 * @throws \app\common\sysConfig\MyException
	 */
	private function uploadFileToOss($shopIdZong, $url,$type,$name, $localPath){
		$saveType = ResourceStorageSettingService::getInstance()->getSaveType($shopIdZong);
		if ($saveType == 1){
			//服务器
			$filUrl = $url;
		} elseif ($saveType == 2){
			//阿里云
			$aliOss = AliossService::getInstance()->getAliossByShopId($shopIdZong);
			$aliossUtil = new AliOss();
			$filUrl = $aliossUtil->uploadFile($aliOss->access_key, $aliOss->access_key_secret, $aliOss->end_point,
				$aliOss->bucket, $type . '/' . $name, $localPath, true);
			if ($aliOss->domain) {
				$filUrl = str_replace('http://' . $aliOss->bucket . '.' . $aliOss->end_point, $aliOss->domain, $filUrl);
			}
		} else {
			//腾讯云
			$tencentOss = TencentossService::getInstance()->getTencentossByShopId($shopIdZong);
			$tencentOssUtil = new TencentOss();
			$filUrl = $tencentOssUtil->uploadFile($tencentOss->secret_id, $tencentOss->secret_key, $tencentOss->region,
				$tencentOss->bucket, $tencentOss->appid, $localPath, $type . '/' . $name, true);
			if ($tencentOss->domain && $tencentOss->domain != '自定义域名') {
				$filUrl = $tencentOss->domain . '/' . $type . '/' . $name;
			}
		}
		return array($saveType, $filUrl);
	}



	/**
	 * 微信上传图片
	 * @param $shopIdZong
	 * @param $shopId
	 * @param $file
	 * @param $level
	 * @return mixed|string
	 * @throws \OSS\Core\OssException
	 * @throws \app\common\sysConfig\MyException
	 */
	public function wxSaveFile($shopIdZong,$shopId,$file,$level=1){
		$userType = ResourceTypeService::getInstance()->getResourceTypeBySign($shopIdZong,'user');
		$resourceTypeId = $userType->id;
		$fileData = $this->uploadFile($file);
		$url = $fileData['url'];
		$name = $fileData['name'];
		$path = $fileData['path'];
		$type = $fileData['type'];
		list($storage_type,$url) = $this->saveResourceToOss($shopIdZong,$url,$type,$name,$path);
		$this->saveResource($shopId,$url,$name,$type,$resourceTypeId,$level,$storage_type,'',0);
		return $url;
	}


	/**
	 * 管理端上传文件
	 * @param $shopId
	 * @param $shopIdZong
	 * @param File $file
	 * @param $typeId
	 * @param int $level
	 * @param string $remark
	 * @return mixed|string
	 * @throws \OSS\Core\OssException
	 * @throws \app\common\sysConfig\MyException
	 */
	public function adminSaveResource($shopId,$shopIdZong,File $file,$typeId,$level=1,$remark=''){
		$fileData = $this->uploadFile($file);
		$url = $fileData['url'];
		$name = $fileData['name'];
		$path = $fileData['path'];
		$type = $fileData['type'];
		$isPublic = 0;
		list($storage_type,$url) = $this->saveResourceToOss($shopIdZong,$url,$type,$name,$path);
		if ($typeId){
			$resourceType = ResourceTypeService::getInstance()->getResourceTypeById($typeId);
			if ($resourceType){
				$isPublic = $resourceType->is_public;
			}
		}
		$id = $this->saveResource($shopId,$url,$name,$type,$typeId,$level,$storage_type,$remark,$isPublic);
		return $url;
	}


	/**
	 * 上传附件
	 * @param $shopIdZong
	 * @param File $file
	 * @return array
	 * @throws \OSS\Core\OssException
	 * @throws \app\common\sysConfig\MyException
	 */
	public function uploadAttachment($shopIdZong,File $file){
		$fileData = $this->uploadFile($file);
		$url = $fileData['url'];
		$name = $fileData['name'];
		$path = $fileData['path'];
		$type = $fileData['type'];
		$oldName = $fileData['old_name'];
//		list($storage_type,$url) = $this->saveResourceToOss($shopIdZong,$url,$type,$name,$path);
		return ['url'=>$url,'old_name'=>$oldName];
	}


}