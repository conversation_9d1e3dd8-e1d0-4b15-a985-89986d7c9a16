<?php


namespace app\common\service\user;

use app\common\model\sysSetting\BillSource;
use app\common\model\user\UserCommon as UserCommonModel;
use app\common\returnConfig\Code;
use app\common\service\admin\ShopAdminService;
use app\common\service\boss\BossAdminUserService;
use app\common\service\boss\ShopBossService;
use app\common\service\common\AdminLogService;
use app\common\service\common\NationService;
use app\common\service\coupon\CouponRecordService;
use app\common\service\finance\SysAccountService;
use app\common\service\finance\userFinance\UserFinanceService;
use app\common\service\hook\MemberHookService;
use app\common\service\recharge\MemberRechargeBillService;
use app\common\service\shop\ShopService;
use app\common\service\sms\AlismsTemplateService;
use app\common\service\sysSetting\BillSourceService;
use app\common\service\sysSetting\BillTypeService;
use app\common\service\sysSetting\MemberAgreementService;
use app\common\service\sysSetting\ShopSecretService;
use app\common\service\sysSetting\SysSettingService;
use app\common\service\sysSetting\UserSourceService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\AccountType;
use app\common\utils\dataDictionary\AdminLogType;
use app\common\utils\dataDictionary\BillType;
use app\common\utils\dataDictionary\LogLevel;
use app\common\utils\dataDictionary\RichText;
use app\common\utils\dataDictionary\SmsTemplate;
use app\common\utils\dataDictionary\SysSetting;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;

class UserCommonService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getUserCommonSimpleList($shopIdZong, $userShopId, $code, $name, $phone, $grade, $useStatus,
                                            $userSource, $block, $startTime, $endTime, $sort, $page, $limit)
    {
        $model = new UserCommonModel();
        return $model->getUserCommonSimpleList($shopIdZong, $userShopId, $code, $name, $phone, $grade, $useStatus,
            $userSource, $block, $startTime, $endTime, $sort, $page, $limit);
    }


    /**
     * @param $shopIdZong int 总店
     * @param $itemShopId int 选择开卡酒店
     * @param $shopId
     * @param $code
     * @param $name
     * @param $phone
     * @param $grade
     * @param $useStatus
     * @param $userSource
     * @param $block
     * @param $page
     * @param $startTime
     * @param $sort
     * @param $endTime
     * @param $limit
     * @return array|false|\PDOStatement|string|\think\Collection
     * @throws MyException
     */
    public function getUserCommonList($shopIdZong, $itemShopId, $shopId, $code, $name, $phone, $grade, $useStatus,
                                      $userSource, $block, $startTime, $endTime, $sort, $page, $limit)
    {
        if (!$grade && StringUtil::checkEmpty($name) && StringUtil::checkEmpty($phone)) {
            $grade = MemberGradeService::getInstance()->getMemberGradeIds($shopIdZong);
        }
        if (!StringUtil::checkEmpty($startTime)) {
            $startTime = strtotime(date('Y-m-d 00:00:00', $startTime));
        }
        if (!StringUtil::checkEmpty($endTime)) {
            $endTime = strtotime(date('Y-m-d 23:59:59', $endTime));
        }
        //只允许查看分店自己酒店会员
        $setting = SysSettingService::getInstance()->getSysSettingBySign($shopIdZong, SysSetting::VIEW_ITEM_SHOP_MEMBER);
        $settingStatus = $setting->property['status'];
        if ($settingStatus == 0) {
            //查询全部
            $userShopId = $shopId;
        } else {
            //查询当前店铺
            if ($itemShopId == $shopIdZong) {
                $userShopId = $shopId;
            } else {
                $userShopId = $itemShopId;
            }
        }
        $model = new UserCommonModel();
        $list = $model->getUserCommonSimpleList($shopIdZong, $userShopId, $code, $name, $phone, $grade, $useStatus,
            $userSource, $block, $startTime, $endTime, $sort, $page, $limit);
        foreach ($list as $item) {
            $tempGradeId = $item->grade;
            $tempCommonCode = $item->common_code;
            $tempGrade = MemberGradeService::getInstance()->getMemberGradeById($tempGradeId);
            $item->grade_name = $tempGrade->grade_name;
            $tempShopId = $item->shop_id;
            if ($tempShopId == 0) {
                $tempShopId = $shopIdZong;
            }
            $tempShop = ShopService::getInstance()->getShopById($tempShopId);
            $item->shop_name = $tempShop->shop_name;
            $couponCount = CouponRecordService::getInstance()->getCouponRecordCount($shopIdZong, '', $tempCommonCode, '', '', 0, null, null);
            $item->coupon_count = $couponCount;
            $tempUserSource = UserSourceService::getInstance()->getUserSourceById($item->user_source);
            if (empty($tempUserSource)) {
                $tempUserSource = UserSourceService::getInstance()->getUserSourceBySign($shopIdZong, \app\common\utils\dataDictionary\BillSource::BURU);
                $this->updateByCommonCode($tempCommonCode, ['user_source' => $tempUserSource->id]);
            }
            $item->user_source_name = $tempUserSource->source_name;
            $userShopBalance = UserShopBalanceService::getInstance()->getUserShopBalance($tempCommonCode, $itemShopId);
            $item->user_shop_balance = $userShopBalance->balance;
            $item->wx = UserWxService::getInstance()->getUserByCommonCode($tempCommonCode, true);
            $userRemark = UserRemarkService::getInstance()->getUserRemark($tempCommonCode);
            if (!empty($userRemark)) {
                $tempUserRemark = $userRemark->remark;;
            } else {
                $tempUserRemark = null;
            }
            $item->remark = $tempUserRemark;
        }
        if ($page && $limit) {
            $totalShopBalance = UserShopBalanceService::getInstance()->getSumBalance($itemShopId);
            $count = $this->getUserCommonCount($shopIdZong, $userShopId, $code, $name, $phone, $grade, $useStatus, $userSource, $block, $startTime, $endTime);
            return ['list' => $list, 'count' => $count, 'total_shop_balance' => $totalShopBalance];
        }
        return $list;
    }

    public function getUserListByGradeId($shopIdZong, $gradeId)
    {
        $model = new UserCommonModel();
        return $model->getUserByGradeId($shopIdZong, $gradeId);
    }

    public function getUserCountByGradeId($shopIdZong, $gradeId)
    {
        $model = new UserCommonModel();
        return $model->getUserCountByGradeId($shopIdZong, $gradeId);
    }

    /**
     * 查询数量
     * @param $shopIdZong
     * @param $shopId
     * @param $code
     * @param $name
     * @param $phone
     * @param $grade
     * @param $useStatus
     * @param $userSource
     * @param $block
     * @param $startTime
     * @param $endTime
     * @return int|string
     */
    public function getUserCommonCount($shopIdZong, $shopId, $code, $name, $phone, $grade, $useStatus, $userSource, $block, $startTime, $endTime)
    {
        $model = new UserCommonModel();
        return $model->getUserCommonCount($shopIdZong, $shopId, $code, $name, $phone, $grade, $useStatus, $userSource, $block, $startTime, $endTime);
    }


    public function editUserCommon($params)
    {
        DebugUtil::log(json_encode($params));
        $model = new UserCommonModel();
        if (isset($params['id'])) {
            $model->editUserCommon($params);
            return $params['id'];
        } else {
            $model->saveUserCommon($params);
            return $model->id;
        }
    }

    public function getCommonUser($shopIdZong, $idCard, $phone)
    {
        $model = new UserCommonModel();
        return $model->getCommonUser($shopIdZong, $idCard, $phone);
    }

    public function getUserCommonById($id)
    {
        $model = new UserCommonModel();
        return $model->getUserCommonById($id);
    }

    /**
     * 更新用户来源
     * @param $userId
     * @param $userSource
     */
    public function updateUserSource($userId, $userSource)
    {
        $this->editUserCommon(['id' => $userId, 'user_source' => $userSource]);
    }

    /**
     * 通过commonCode查询
     * @param $commonCode
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getUserCommonByCommonCode($commonCode)
    {
        $model = new UserCommonModel();
        return $model->getUserCommonByCommonCode($commonCode);
    }

    public function getUserCommonByCommonCodeFromCache($commonCode)
    {
        $key = 'common_' . $commonCode;
        if (CacheManager::has($key)) {
            $user = CacheManager::getCacheValue($key);
            return $user;
        } else {
            $user = $this->getUserCommonByCommonCode($commonCode);
            CacheManager::setCacheValue($key, $user, null, null, 10);
            return $user;
        }
    }


    /**
     * 修改余额并添加记录
     * @param $commonCode
     * @param $shopId
     * @param $billId
     * @param $billType
     * @param $change
     * @param $changeReason
     * @param $adminId
     * @param array $params
     * @return mixed
     * @throws MyException
     */
    public function updateUserBalance($commonCode, $shopId, $billId, $billType, $change, $changeReason, $adminId, $params = [])
    {
        DebugUtil::log("$billId, $billType, $change");
        $user = $this->getUserCommonByCommonCode($commonCode);
        $balance = $user->balance;
        $shopIdZong = $user->shop_id_zong;
        $totalBalance = sprintf('%.2f', $balance + $change);
        if ($totalBalance < 0) {
            throw new MyException(Code::FAIL, "余额不足");
        }
        if ($billType == BillType::CASH_RECHARGE_REFUND) {
            $balanceRecord = UserBalanceRecordService::getInstance()->getByBillId($billId, BillType::CASH_RECHARGE);
            UserBalanceRecordService::getInstance()->updateStatus($balanceRecord->id, 2);
        }
        $balanceSetting = SysSettingService::getInstance()->getSysSettingBySign($shopIdZong, SysSetting::USER_BALANCE);
        $balanceType = $balanceSetting->property['value'];
        $realBalance = $user->real_balance;
        $giftBalance = $user->gift_balance;
        $oldDetailId = isset($params['old_detail_id']) ? $params['old_detail_id'] : 0;
        $billDetailId = isset($params['detail_id']) ? $params['detail_id'] : 0;
        if ($billType == BillType::CASH_RECHARGE) {
            $rechargeBill = MemberRechargeBillService::getInstance()->getBillById($billId);
            $changeAmount = $rechargeBill->money;
            $giftAmount = $rechargeBill->give_money;
        } elseif ($billType == BillType::CASH_RECHARGE_REFUND) {
            $rechargeBill = MemberRechargeBillService::getInstance()->getBillById($billId);
            $changeAmount = $rechargeBill->money * -1;
            $giftAmount = $rechargeBill->give_money * -1;
        } elseif ($billType == BillType::MODIFY) {
            $giftAmount = $change;
            $changeAmount = 0;
        } else {
            $selectBillType = $billType;
            $oldDetail = null;
            if ($oldDetailId) {
                if ($billType == BillType::ROOM_REFUND) {
                    $selectBillType = BillType::BOOK_ROOM;
                }
                $oldDetail = UserBalanceRecordService::getInstance()->getByBillDetailId($billId, $selectBillType, $oldDetailId);
            }
            if ($billType == BillType::ROOM_REFUND && $oldDetail) {
                $giftAmount = $oldDetail->gift_amount * -1;
                $changeAmount = $oldDetail->change_amount * -1;
            } else {
                if ($balanceType == 1) {
                    //先扣除充值金额，再扣除赠送金额
                    if ($change < 0) {
                        if ($realBalance >= $change * -1) {
                            $changeAmount = $change;
                            $giftAmount = 0;
                        } else {
                            $changeAmount = $realBalance * -1;
                            $giftAmount = $change + $realBalance;
                        }
                    } else {
                        $changeAmount = $change;
                        $giftAmount = 0;
                    }
                } else {
                    //按照比例扣除
                    $changeAmount = sprintf('%.2f', ($realBalance / $balance * $change));
                    $giftAmount = $change + $changeAmount;
                    if ($change < 0) {
                        if ($giftAmount * -1 > $giftBalance) {
                            //四舍五入可能导致余数
                            $giftAmount = $giftBalance * -1;
                            $changeAmount = $change + $giftBalance;
                        }
                    } else {
                        if ($giftAmount > $giftBalance) {
                            //四舍五入可能导致余数
                            $giftAmount = $giftBalance * -1;
                            $changeAmount = $change - $giftBalance;
                        }
                    }
                }
            }
        }
        $recordId = UserBalanceRecordService::getInstance()->addUserBalanceRecord($shopId, $commonCode, $billId, $billType,
            $totalBalance, $change, $changeAmount, $giftAmount, $changeReason, $adminId, $billDetailId);
        $totalChangeAmount = $realBalance + $changeAmount;
        $totalGiftAmount = $giftBalance + $giftAmount;
        $this->updateUserBalanceByCommonCode($commonCode, $totalBalance, $totalChangeAmount, $totalGiftAmount);
        if (in_array($billType, [BillType::ROOM_REFUND, BillType::STORE_REFUND, BillType::TRAVEL_REFUND, BillType::DISHES_REFUND])) {
            MemberHookService::getInstance()->memberConsumeRefundHook($shopId, $recordId);
        } else {
            if ($billType == BillType::CASH_RECHARGE) {
                if ($change > 0) {
                    MemberHookService::getInstance()->memberRechargeHook($shopId, $billId, $recordId, $params);
                } else {
                    MemberHookService::getInstance()->memberRechargeRefundHook($shopId, $recordId);
                }
            } elseif ($billType == BillType::MODIFY) {

            } else {
                MemberHookService::getInstance()->memberConsumeHook($shopId, $recordId);
            }
        }
        return $recordId;
    }


    /**
     * 消费返现
     * @param $shopId
     * @param $commonCode
     * @param $change
     * @param $billId
     * @param $billType
     * @param $changeReason
     * @return void
     */
    public function xffxUpdateBalance($shopId, $commonCode, $change, $billId, $billType, $changeReason)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $realBalance = $user->real_balance;
        $giftBalance = $user->gift_balance;
        $balance = $user->balance;
        $totalBalance = $balance + $change;
        $recordId = UserBalanceRecordService::getInstance()->addUserBalanceRecord($shopId, $commonCode, $billId, $billType,
            $totalBalance, $change, 0, $change, $changeReason . "消费返现赠送", 0, 0);
        $totalGiftAmount = $giftBalance + $change;
        $this->updateUserBalanceByCommonCode($commonCode, $totalBalance, $realBalance, $totalGiftAmount);
    }

    /**
     * api消费扣款
     * @param $commonCode
     * @param $shopId
     * @param $billId
     * @param $billType
     * @param $change
     * @param $changeReason
     * @param $thirdOrderNo
     * @return mixed
     * @throws MyException
     */
    private function apiPayUpdateUserBalance($commonCode, $shopId, $billId, $billType, $change, $changeReason, $thirdOrderNo)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $shopIdZong = $user->shop_id_zong;
        $balance = $user->balance;
        $totalBalance = sprintf('%.2f', $balance + $change);
        if ($totalBalance < 0) {
            throw new MyException(Code::FAIL, "余额不足");
        }
        $thirdRecord = UserBalanceRecordService::getInstance()->getRecordByThirdOrderNo($shopId, $thirdOrderNo);
        if (!empty($thirdRecord)) {
            throw new MyException(Code::FAIL, "第三方订单号重复");
        }
        $recordId = UserBalanceRecordService::getInstance()->apiAddUserBalanceRecord($shopId, $commonCode, $billId, $billType, $totalBalance, $change, $changeReason, $thirdOrderNo, null);
        $balanceSetting = SysSettingService::getInstance()->getSysSettingBySign($shopIdZong, SysSetting::USER_BALANCE);
        $balanceType = $balanceSetting->property['value'];
        $realBalance = $user->real_balance;
        $giftBalance = $user->gift_balance;
        if ($balanceType == 1) {
            //先扣除充值金额，再扣除赠送金额
            if ($realBalance > $change) {
                $changeAmount = sprintf('%.2f', $realBalance - $change);
                $giftAmount = 0;
            } else {
                $changeAmount = sprintf('%.2f', $giftBalance - $change);
                $giftAmount = $change - $changeAmount;
            }
        } else {
            //按照比例扣除
            $changeAmount = sprintf('%.2f', ($realBalance / $balance * $change));
            $giftAmount = $change - $changeAmount;
            if ($giftAmount > $giftBalance) {
                //四舍五入可能导致余数
                $giftAmount = $giftBalance;
                $changeAmount = $change - $giftAmount;
            }
        }
        $this->updateUserBalanceByCommonCode($commonCode, $totalBalance, $changeAmount, $giftAmount);
        if (in_array($billType, [BillType::ROOM_REFUND, BillType::STORE_REFUND, BillType::TRAVEL_REFUND, BillType::DISHES_REFUND])) {
            MemberHookService::getInstance()->memberConsumeRefundHook($shopId, $recordId);
        } else {
            if ($billType == BillType::CASH_RECHARGE) {
                if ($change > 0) {
                    MemberHookService::getInstance()->memberRechargeHook($shopId, $billId, $recordId, []);
                } else {
                    MemberHookService::getInstance()->memberRechargeRefundHook($shopId, $recordId);
                }
            } elseif ($billType == BillType::MODIFY) {

            } else {
                MemberHookService::getInstance()->memberConsumeHook($shopId, $recordId);
            }
        }
        return $recordId;
    }

    /**
     * api消费扣退款
     * @param $commonCode
     * @param $shopId
     * @param $billId
     * @param $billType
     * @param $change
     * @param $changeReason
     * @param $thirdOrderNo
     * @param $thirdRefundCode
     * @return mixed
     * @throws MyException
     */
    private function apiRefundUpdateUserBalance($commonCode, $shopId, $billId, $billType, $change, $changeReason, $thirdOrderNo, $thirdRefundCode)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $balance = $user->balance;
        $shopIdZong = $user->shop_id_zong;
        $totalBalance = sprintf('%.2f', $balance + $change);
        $thirdRecords = UserBalanceRecordService::getInstance()->getRecordByThirdOrderNo($shopId, $thirdOrderNo);
        if (empty($thirdRecords)) {
            throw new MyException(Code::FAIL, "第三方订单号错误");
        }
        $totalPayAmount = 0;
        $totalRefundAmount = 0;
        foreach ($thirdRecords as $thirdRecord) {
            $itemRefundCode = $thirdRecord->third_refund_code;
            if (empty($itemRefundCode)) {
                $totalPayAmount = $thirdRecord->change;
            } else {
                $totalRefundAmount += $thirdRecord->change;
            }
        }
        if ($totalPayAmount * -1 < $totalRefundAmount * -1 + $change) {
            throw new MyException(Code::FAIL, "总退款金额大于支付金额");
        }
        $recordId = UserBalanceRecordService::getInstance()->apiAddUserBalanceRecord($shopId, $commonCode, $billId, $billType, $totalBalance, $change, $changeReason, $thirdOrderNo, $thirdRefundCode);
        $balanceSetting = SysSettingService::getInstance()->getSysSettingBySign($shopIdZong, SysSetting::USER_BALANCE);
        $balanceType = $balanceSetting->property['value'];
        $realBalance = $user->real_balance;
        $giftBalance = $user->gift_balance;
        if ($balanceType == 1) {
            //先扣除充值金额，再扣除赠送金额
            if ($realBalance > $change) {
                $changeAmount = sprintf('%.2f', $realBalance - $change);
                $giftAmount = 0;
            } else {
                $changeAmount = sprintf('%.2f', $giftBalance - $change);
                $giftAmount = $change - $changeAmount;
            }
        } else {
            //按照比例扣除
            $changeAmount = sprintf('%.2f', ($realBalance / $balance * $change));
            $giftAmount = $change - $changeAmount;
            if ($giftAmount > $giftBalance) {
                //四舍五入可能导致余数
                $giftAmount = $giftBalance;
                $changeAmount = $change - $giftAmount;
            }
        }
        $this->updateUserBalanceByCommonCode($commonCode, $totalBalance, $changeAmount, $giftAmount);
        if (in_array($billType, [BillType::ROOM_REFUND, BillType::STORE_REFUND, BillType::TRAVEL_REFUND, BillType::DISHES_REFUND])) {
            MemberHookService::getInstance()->memberConsumeRefundHook($shopId, $recordId);
        } else {
            if ($billType == BillType::CASH_RECHARGE) {
                if ($change > 0) {
                    MemberHookService::getInstance()->memberRechargeHook($shopId, $billId, $recordId, []);
                } else {
                    MemberHookService::getInstance()->memberRechargeRefundHook($shopId, $recordId);
                }
            } elseif ($billType == BillType::MODIFY) {

            } else {
                MemberHookService::getInstance()->memberConsumeHook($shopId, $recordId);
            }
        }
        return $recordId;
    }


    public function getUserByPhone($shopIdZong, $phone)
    {
        $model = new UserCommonModel();
        return $model->getUserByPhone($shopIdZong, $phone);
    }

    public function getUserByIdentification($shopIdZong, $identification, $identificationType)
    {
        $model = new UserCommonModel();
        return $model->getUserByIdentification($shopIdZong, $identification, $identificationType);
    }

    /**
     * 修改用户成长值
     * @param $commonCode
     * @param $shopId
     * @param $billId
     * @param $billType
     * @param $change
     * @param $changeReason
     * @param $adminId
     * @throws MyException
     */
    public function updateUserGrowth($commonCode, $shopId, $billId, $billType, $change, $changeReason, $adminId)
    {

        $user = $this->getUserCommonByCommonCode($commonCode);
        $phone = $user->phone;
        $growth = $user->growth;
        if ($growth < 0) {
            return;
        }
        $userGrade = $user->grade;
        $shopIdZong = $user->shop_id_zong;
        $totalGrowth = $growth + $change;
        if ($totalGrowth < 0) {
            throw new MyException(Code::FAIL, "成长值小于0");
        }
        if ($billType != -1) {
            DebugUtil::log("totalGrowth:$commonCode   $growth + $change");
            UserGrowthRecordService::getInstance()->addUserGrowthRecord($shopId, $commonCode, $billId, $billType, $totalGrowth, $change, $changeReason, $adminId);
        }
        $this->updateUserGrowthByCommonCode($commonCode, $totalGrowth);
        $memberGrade = MemberGradeService::getInstance()->getMemberGradeByGrowth($shopIdZong, $totalGrowth);
        if (empty($memberGrade)) {
            return;
        }
        $newMemberGradeId = $memberGrade->id;
        $newMemberGradeName = $memberGrade->grade_name;
        $usable_month = $memberGrade->usable_month;
        $limitTime = strtotime(TimeUtil::getInstance()->getNextMonthDay(date('Y-m-d H:i:s'), $usable_month));
        if ($userGrade != $newMemberGradeId) {
            $this->editUserCommon(['id' => $user->id, 'grade' => $newMemberGradeId, 'limit_time' => $limitTime]);
            if ($billType == BillType::BUY_MEMBER) {
                $userLogContent = "购买会员升级 $newMemberGradeName";
            } elseif ($billType == BillType::MODIFY) {
                $userLogContent = "手动修改会员升级 $newMemberGradeName";
            } else {
                $userLogContent = "消费升级 $newMemberGradeName";
            }
            DebugUtil::log("修改等级 $phone $newMemberGradeName");
            MemberHookService::getInstance()->meberGrowUp($shopId, $commonCode);
            UserLogService::getInstance()->saveUserLog($commonCode, LogLevel::NORMAL, $userLogContent, AdminLogType::GRADE, $adminId, $shopId);
        }
    }

    public function buyMemberGrade($gradeId, $commonCode, $changeGrowth, $month, $adminId, $shopId)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $memberGrade = MemberGradeService::getInstance()->getMemberGradeById($gradeId);
        $newMemberGradeName = $memberGrade->grade_name;

        $growth = $user->growth;
        $userGrade = $user->grade;
        if ($userGrade == $gradeId) {
            $changeGrowth = 0;
        }
        $totalGrowth = $growth + $changeGrowth;
        if ($totalGrowth > 0) {
            $this->updateUserGrowthByCommonCode($commonCode, $totalGrowth);
        }
        if ($month < 1) {
            $days = (int)$month * 30;
            $limitTime = TimeUtil::getInstance()->getDate(date('Y-m-d H:i:s'), $days, 'Y-m-d H:i:s');
        } else {
            $limitTime = TimeUtil::getInstance()->getNextMonthDay(date('Y-m-d H:i:s'), $month);
        }
        $limitTime = strtotime($limitTime);
        $this->editUserCommon(['id' => $user->id, 'grade' => $gradeId, 'limit_time' => $limitTime]);
        $limitDate = date('Y-m-d H:i:s', strtotime($limitTime));
        if ($userGrade == $gradeId) {
            $userLogContent = "购买会员 $newMemberGradeName 至 $limitDate";
        } else {
            $userLogContent = "购买会员升级 $newMemberGradeName";
        }
        UserLogService::getInstance()->saveUserLog($commonCode, LogLevel::NORMAL, $userLogContent, AdminLogType::GRADE, $adminId, $shopId);
        MemberHookService::getInstance()->meberGrowUp($shopId, $commonCode);
    }


    /**
     * 修改用户积分
     * @param $commonCode
     * @param $shopId
     * @param $billId
     * @param $billType
     * @param $change
     * @param $changeReason
     * @param $adminId
     * @throws MyException
     */
    public function updateUserPoint($commonCode, $shopId, $billId, $billType, $change, $changeReason, $adminId)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $point = $user->point;
        $totalPoint = $point + $change;
        if ($totalPoint < 0) {
            throw new MyException(Code::FAIL, "积分小于0");
        }
        UserPointRecordService::getInstance()->addUserPointRecord($shopId, $commonCode, $billId, $billType, $totalPoint, $change, $changeReason, $adminId);
        $this->updateUserPointByCommonCode($commonCode, $totalPoint);
    }

    /**
     * 修改余额
     * @param $commonCode
     * @param $balance
     * @return false|int
     */
    private function updateUserBalanceByCommonCode($commonCode, $balance, $changeAmount, $giftAmount)
    {
        $model = new UserCommonModel();
        return $model->updateUserBalanceByCommonCode($commonCode, $balance, $changeAmount, $giftAmount);
    }

    /**
     * 修改成长值
     * @param $commonCode
     * @param $growth
     * @return false|int
     */
    private function updateUserGrowthByCommonCode($commonCode, $growth)
    {
        $model = new UserCommonModel();
        return $model->updateUserGrowthByCommonCode($commonCode, $growth);
    }

    /**
     * 修改积分
     * @param $commonCode
     * @param $point
     * @return false|int
     */
    private function updateUserPointByCommonCode($commonCode, $point)
    {
        $model = new UserCommonModel();
        return $model->updateUserPointByCommonCode($commonCode, $point);
    }

    public function setBlock($commonCode, $block)
    {
        $this->updateByCommonCode($commonCode, ['block' => $block]);
    }


    public function updateByCommonCode($commonCode, $param)
    {
        DebugUtil::log($param);
        if (empty($commonCode)) {
            throw new MyException(Code::FAIL, "更新失败");
        }
        $model = new UserCommonModel();
        return $model->updateByCommonCode($commonCode, $param);
    }

    /**
     * 通过手机号，姓名查询用户
     * @param $shopIdZong
     * @param $shopId
     * @param $name
     * @param $phone
     * @param $code
     * @param $page
     * @param $limit
     * @return array|false|\PDOStatement|string|\think\Collection
     */
    public function getUserByPhoneAndName($shopIdZong, $shopId, $name, $phone, $code, $page, $limit)
    {
        $model = new UserCommonModel();
        $list = $model->getUserByPhoneAndName($shopIdZong, $shopId, $name, $phone, $code, $page, $limit);
        if ($page && $limit) {
            $count = $this->getUserByPhoneAndNameCount($shopIdZong, $shopId, $name, $phone, $code);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    /**
     * 根据姓名手机号模糊查询
     * @param $shopIdZong
     * @param $shopId
     * @param $searchWord
     * @return array|null
     */
    public function getUserCommonCodeByPhoneOrName($shopIdZong, $shopId, $searchWord)
    {
        if (StringUtil::checkEmpty($searchWord)) {
            return null;
        }
        $commonCodeList = [];
        $list = $this->getUserByPhoneAndName($shopIdZong, $shopId, $searchWord, $searchWord, '', '', '');
        foreach ($list as $item) {
            array_push($commonCodeList, $item->common_code);
        }
        return $commonCodeList;
    }

    /**
     * 查询客人
     * @param $shopIdZong
     * @param $searchWord
     * @param $member
     * @param $page
     * @param $limit
     * @return array|false|\PDOStatement|string|\think\Collection
     */
    public function getSearchUser($shopIdZong, $searchWord, $member, $page, $limit)
    {
        $gradeId = null;
        if ($member) {
            $lowestGrade = MemberGradeService::getInstance()->getLowestGrade($shopIdZong);
            $gradeId = $lowestGrade->id;
        }
        $model = new UserCommonModel();
        $list = $model->getSearchUserList($shopIdZong, $searchWord, $gradeId, $page, $limit);
        foreach ($list as $item) {
            $tempGradeId = $item->grade;
            if ($tempGradeId == 0) {
                $tempGrade = MemberGradeService::getInstance()->getLowestGrade($shopIdZong);
            } else {
                $tempGrade = MemberGradeService::getInstance()->getMemberGradeById($tempGradeId);
            }
            $item->grade_name = $tempGrade->grade_name;
            $item->upgrade_growth_value = $tempGrade->upgrade_growth_value;
            $tempShopId = $item->shop_id;
            if ($tempShopId == 0) {
                $tempShopId = $shopIdZong;
            }
            $shop = ShopService::getInstance()->getShopById($tempShopId);
            $item->register_shop_name = $shop->shop_name;
        }
        if ($page && $limit) {
            $count = $model->getSearchUserCount($shopIdZong, $searchWord, $gradeId);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }


    public function getUserByCode($shopIdZong, $userCode)
    {
        $model = new UserCommonModel();
        return $model->getUserByCode($shopIdZong, $userCode);
    }

    public function getUserByPhoneAndNameCount($shopIdZong, $shopId, $name, $phone, $code)
    {
        $model = new UserCommonModel();
        return $model->getUserByPhoneAndNameCount($shopIdZong, $shopId, $name, $phone, $code);
    }


    /**
     * 查询用户信息
     * @param $commonCode
     * @param $shopId
     * @param $simple
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     */
    public function adminGetUserInfo($commonCode, $shopId, $simple)
    {
        MemberUtilService::getInstance()->checkMemberRightInterest($commonCode);
        $user = $this->getUserCommonByCommonCode($commonCode);
        $shopIdZong = $user->shop_id_zong;
        if ($simple) {
            return $user;
        }
        $registerShop = ShopService::getInstance()->getShopById($user->shop_id);
        $user->register_shop_name = $registerShop->shop_name;
        $grade = $user->grade;
        if ($grade == 0) {
            $user->grade_info = null;
        } else {
            $memberGrade = MemberGradeService::getInstance()->getMemberGradeById($grade);
            $right_itererest = MemberRightInterestService::getInstance()->getMemberRightInterestByGradeId($shopIdZong, $memberGrade->id);
            $memberGrade->right_itererest = $right_itererest;
            $user->grade_info = $memberGrade;
        }
        $userShopBalance = UserShopBalanceService::getInstance()->getUserShopBalance($commonCode, $shopId);
        $user->user_shop_balance = $userShopBalance;
        $wxUser = UserWxService::getInstance()->getUserByCommonCode($commonCode);
        if (!empty($wxUser)) {
            $user->wx = $wxUser;
        } else {
            $user->wx = null;
        }
        $coupons = CouponRecordService::getInstance()->getCouponRecordList($shopIdZong, '', $commonCode, '', '', 0, '', null, null, '');
        $user->coupons = $coupons;
        $userRemark = UserRemarkService::getInstance()->getUserRemark($commonCode);
        if (empty($userRemark)) {
            $remark = ["car_number" => [], "email" => "", "hobby" => "", "remark" => ""];
        } else {
            $remark = $userRemark->remark;
        }
        $user->remark = $remark;
        $nation = $user->nation;
        $nationNamme = null;
        if ($nation) {
            $nationModel = NationService::getInstance()->getNationById($nation);
            $nationNamme = $nationModel->nation_name;
        }
        $user->nation_name = $nationNamme;
        $userSourceId = $user->user_source;
        $userSource = UserSourceService::getInstance()->getUserSourceById($userSourceId);
        if (empty($userSource)) {
            $tempUserSource = UserSourceService::getInstance()->getUserSourceBySign($shopIdZong, \app\common\utils\dataDictionary\BillSource::BURU);
            $this->updateByCommonCode($commonCode, ['user_source' => $tempUserSource->id]);
            $user->user_source_name = $tempUserSource->source_name;
        } else {
            $user->user_source_name = $userSource->source_name;
        }
        $userDevelop = UserDevelopService::getInstance()->getByCommonCode($commonCode);
        $developAdminName = null;
        $developAdminNickname = null;
        $devAdminId = 0;
        if (!empty($userDevelop)) {
            $devAdminId = $userDevelop->admin_id;
            if ($devAdminId != 0) {
                $devAdmin = ShopAdminService::getInstance()->getAdminById($devAdminId);
                $developAdminName = $devAdmin->name;
                $developAdminNickname = $devAdmin->nickname;
            }
        }
        $user->develop_admin_id = $devAdminId;
        $user->develop_admin_name = $developAdminName;
        $user->develop_admin_nickname = $developAdminNickname;
        return $user;
    }


    /**
     * 小程序查询用户信息
     * @param $token
     * @param $userId
     * @param $simple
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     */
    public function wxGetUserInfo($token, $userId, $simple)
    {
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $wxUser->user_token = $token;
        if (empty($commonCode) || $simple) {
            $wxUser->grade_info = null;
            return $wxUser;
        }
        MemberUtilService::getInstance()->checkMemberRightInterest($commonCode);
        $user = $this->getUserCommonByCommonCode($commonCode);
        $shopIdZong = $user->shop_id_zong;
        $registerShopId = $user->shop_id;
        if (!$registerShopId) {
            $registerShopId = $shopIdZong;
        }
        $registerShop = ShopService::getInstance()->getShopById($registerShopId);
        $wxUser->register_shop_name = $registerShop->shop_name;
        $grade = $user->grade;
        if ($grade == 0) {
            $wxUser->grade_info = null;
        } else {
            $memberGrade = MemberGradeService::getInstance()->getMemberGradeById($grade);
            $right_itererest = MemberRightInterestService::getInstance()->getMemberRightInterestByGradeId($shopIdZong, $memberGrade->id);
            $memberGrade->right_itererest = $right_itererest;
            $wxUser->grade_info = $memberGrade;
        }
        $userShopBalance = UserShopBalanceService::getInstance()->getUserShopBalanceList('', $commonCode, '', '');
        $wxUser->user_shop_balance = $userShopBalance;
        $coupons = CouponRecordService::getInstance()->getCouponRecordList($shopIdZong, '', $commonCode, '', '', 0, '', null, null, '');
        $wxUser->coupons = $coupons;
        $isBoss = false;
        if ($commonCode) {
            $bossUser = ShopBossService::getInstance()->getShopBossByCommonCode($commonCode, 1);
            if ($bossUser) {
                $isBoss = true;
            }
        }
        $wxUser->block = $user->block;
        $wxUser->is_boss = $isBoss;
        $wxUser->gender = $user->gender;
        $wxUser->name = $user->name;
        $wxUser->identification_type = $user->identification_type;
        $wxUser->identification_number = $user->identification_number;
        $wxUser->birthday = $user->birthday;
        $wxUser->limit_time = $user->limit_time;
        $agreement = MemberAgreementService::getInstance()->getMemberAgreementByCommonCode($shopIdZong, RichText::USER_AGREEMENT, $commonCode);
        $user_agreement = false;
        if (!$agreement) {
            $user_agreement = true;
        }
        $wxUser->user_agreement = $user_agreement;
        return $wxUser;
    }

    /**
     * 更新到期时间
     * @param $commonCode
     * @param $limitTime
     * @throws MyException
     */
    public function updateLimitTime($commonCode, $limitTime)
    {
        $this->updateByCommonCode($commonCode, ['limit_time' => $limitTime]);
    }

    /**
     * 判断余额是否足够
     * @param $commonCode
     * @param $amount
     * @throws MyException
     */
    public function checkBalance($commonCode, $amount)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $balance = $user->balance;
        if ($amount > $balance) {
            throw new MyException(Code::FAIL, "余额不足");
        }
    }

    public function checkPoint($commonCode, $point)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $userPoint = $user->point;
        if ($point > $userPoint) {
            throw new MyException(Code::FAIL, "积分不足");
        }
    }


    /**
     * 修改会员余额
     * @param $shopIdZong
     * @param $shopId
     * @param $memberCode
     * @param $amount
     * @param $remark
     * @param $adminId
     * @throws MyException
     */
    public function adminBossUpdateBalance($shopIdZong, $shopId, $memberCode, $amount, $remark, $adminId)
    {
        if (strlen($memberCode) != 26) {
            throw new MyException(Code::FAIL, "会员码错误");
        }
        $userCode = $this->getUserCode($memberCode);
        $user = $this->getUserByCode($shopIdZong, $userCode);
        if (empty($user)) {
            throw new MyException(Code::FAIL, "会员码错误");
        }
        $commonCode = $user->common_code;

        UserCommonService::getInstance()->updateUserBalance($commonCode, $shopId, 0, BillType::MODIFY, ($amount * -1), $remark, $adminId);
        $sysAccount = SysAccountService::getInstance()->getSysAccountByShopIdAndSign($shopIdZong, $shopId, AccountType::MEMBER_PAY);
        if (empty($sysAccount)) {
            throw new MyException(Code::FAIL, "不支持该支付方式");
        }
        $accountId = $sysAccount->id;
        $billSource = BillSourceService::getInstance()->getResourceBySign($shopId, \app\common\utils\dataDictionary\BillSource::BURU);
        $billSourceId = $billSource->id;
        SysAccountService::getInstance()->updateAccountAmount($shopIdZong, $shopId, $adminId, $accountId,
            BillType::MODIFY, $amount * -1, $remark, $billSourceId, $commonCode, 0, null, []);
        MemberHookService::getInstance()->updateUserBalance($shopId, $commonCode, $amount * -1);
    }


    /**
     * 通过api修改用户余额
     * @param $shopIdZong
     * @param $shopId
     * @param $hotelCode
     * @param $memberCode
     * @param $apiAmount
     * @param $remark
     * @param $timestamp
     * @param $thirdOrderCode
     * @param $sign
     * @throws MyException
     */
    public function apiUpdateBalance($shopIdZong, $shopId, $hotelCode, $memberCode, $apiAmount, $remark, $timestamp, $thirdOrderCode, $sign)
    {
        if (time() - $timestamp > 120) {
            throw new MyException(Code::FAIL, "请求过期");
        }
        $this->checkMemberSign($shopId,
            ['hotel_code' => $hotelCode, 'member_code' => $memberCode, 'amount' => $apiAmount, 'remark' => $remark, 'timestamp' => $timestamp, 'third_order_code' => $thirdOrderCode]
            , $sign);
        if (strlen($memberCode) != 26) {
            throw new MyException(Code::FAIL, "会员码错误");
        }
        if ($apiAmount < 0) {
            throw new MyException(Code::FAIL, "消费金额错误");
        }
        if (empty($thirdOrderCode)) {
            throw new MyException(Code::FAIL, "第三方订单号错误");
        }
        $userCode = $this->getUserCode($memberCode);
        $user = $this->getUserByCode($shopIdZong, $userCode);
        if (empty($user)) {
            throw new MyException(Code::FAIL, "会员码错误");
        }

        $commonCode = $user->common_code;
        $remark = "外部扣费" . $remark;
        $amount = floatval(sprintf("%.2f", $apiAmount / 100));
        UserCommonService::getInstance()->apiPayUpdateUserBalance($commonCode, $shopId, 0, BillType::MODIFY, ($amount * -1), $remark, $thirdOrderCode);
        $sysAccount = SysAccountService::getInstance()->getSysAccountByShopIdAndSign($shopIdZong, $shopId, AccountType::MEMBER_PAY);
        if (empty($sysAccount)) {
            throw new MyException(Code::FAIL, "不支持该支付方式");
        }
        $accountId = $sysAccount->id;
        $billSource = BillSourceService::getInstance()->getResourceBySign($shopId, \app\common\utils\dataDictionary\BillSource::BURU);
        $billSourceId = $billSource->id;
        SysAccountService::getInstance()->updateAccountAmount($shopIdZong, $shopId, 0, $accountId,
            BillType::MODIFY, $amount * -1, $remark, $billSourceId, $commonCode, 0, null, []);
        MemberHookService::getInstance()->updateUserBalance($shopId, $commonCode, $amount * -1);
    }

    /**
     * 通过api退款
     * @param $shopIdZong
     * @param $shopId
     * @param $hotelCode
     * @param $apiAmount
     * @param $remark
     * @param $timestamp
     * @param $thirdOrderCode
     * @param $thirdRefundCode
     * @param $sign
     * @throws MyException
     */
    public function apiRefundUpdateBalance($shopIdZong, $shopId, $hotelCode, $apiAmount, $remark, $timestamp, $thirdOrderCode, $thirdRefundCode, $sign)
    {
        if (time() - $timestamp > 120) {
            throw new MyException(Code::FAIL, "请求过期");
        }
        if ($apiAmount < 0) {
            throw new MyException(Code::FAIL, "退款金额错误");
        }
        $this->checkMemberSign($shopId,
            ['hotel_code' => $hotelCode, 'amount' => $apiAmount, 'remark' => $remark, 'timestamp' => $timestamp, 'third_order_code' => $thirdOrderCode, "third_refund_code" => $thirdRefundCode],
            $sign);
        if (empty($thirdOrderCode) || empty($thirdRefundCode)) {
            throw new MyException(Code::FAIL, "第三方订单号错误");
        }
        $record = UserBalanceRecordService::getInstance()->findRecordByThirdOrderNo($shopId, $thirdOrderCode);
        if (empty($record)) {
            throw new MyException(Code::FAIL, "第三方订单号错误");
        }
        $refundRecord = UserBalanceRecordService::getInstance()->getRecordByThirdRefundCode($shopId, $thirdRefundCode);
        if (!empty($refundRecord)) {
            throw new MyException(Code::FAIL, "第三方退款单号重复");
        }
        $commonCode = $record->common_code;
        $user = $this->getUserCommonByCommonCode($commonCode);
        $commonCode = $user->common_code;
        $remark = "外部退款" . $remark;
        $amount = floatval(sprintf("%.2f", $apiAmount / 100));
        UserCommonService::getInstance()->apiRefundUpdateUserBalance($commonCode, $shopId, 0, BillType::MODIFY, $amount, $remark, $thirdOrderCode, $thirdRefundCode);
        $sysAccount = SysAccountService::getInstance()->getSysAccountByShopIdAndSign($shopIdZong, $shopId, AccountType::MEMBER_PAY);
        $accountId = $sysAccount->id;
        $billSource = BillSourceService::getInstance()->getResourceBySign($shopId, \app\common\utils\dataDictionary\BillSource::BURU);
        $billSourceId = $billSource->id;
        SysAccountService::getInstance()->updateAccountAmount($shopIdZong, $shopId, 0, $accountId,
            BillType::MODIFY, $amount, $remark, $billSourceId, $commonCode, 0, null, []);
        MemberHookService::getInstance()->updateUserBalance($shopId, $commonCode, $amount);
    }

    private function checkMemberSign($shopId, $params, $sign)
    {
        $shopSecret = ShopSecretService::getInstance()->getShopSecret($shopId);
        if (empty($shopSecret)) {
            throw new MyException(Code::FAIL, "会员支付功能未开通");
        }
        $secret = $shopSecret->secret;
        //签名步骤一：按字典序排序参数
        ksort($params);
        $string = $this->ToUrlParams($params);
        //签名步骤二：在string后加入KEY
        $string .= "&secret=$secret";
        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        if ($result != $sign) {
            throw new MyException(Code::FAIL, "签名验证错误");
        }
    }

    /**
     * 格式化参数格式化成url参数
     * @param $params
     * @return string
     */
    private function ToUrlParams($params)
    {
        $buff = "";
        foreach ($params as $k => $v) {
            if ($k != "sign" && $v != "" && !is_array($v)) {
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }

    /**
     * 电脑管理的扣除
     * @param $shopIdZong
     * @param $shopId
     * @param $memberCode
     * @param $commonCode
     * @param $amount
     * @param $remark
     * @param $adminId
     * @throws MyException
     */
    public function adminUpdateBalance($shopIdZong, $shopId, $memberCode, $commonCode, $amount, $type, $remark, $adminId)
    {
        if ($memberCode) {
            $userCode = $this->getUserCode($memberCode);
            $user = $this->getUserByCode($shopIdZong, $userCode);
            $commonCode = $user->common_code;
        } else {
            $user = $this->getUserCommonByCommonCode($commonCode);
        }
        if (empty($user)) {
            throw new MyException(Code::FAIL, "会员码错误");
        }
        $user = $this->getUserCommonByCommonCode($commonCode);
        $balance = $user->balance;
        $realBalance = $user->real_balance;
        $giftBalance = $user->gift_balance;
        $totalBalance = $balance + $amount;
        $changeAmount = 0;
        $giftAmount = 0;
        $changeReason = "管理员修改余额";
        if ($type == 1) {
            $changeAmount += $amount;
            $changeReason .= "，本金余额" . $amount;
        } else {
            $giftAmount += $amount;
            $changeReason .= "，赠送余额" . $amount;
        }
        $changeReason = $remark . '【' . $changeReason . '】';
        $recordId = UserBalanceRecordService::getInstance()->addUserBalanceRecord($shopId, $commonCode, 0, BillType::MODIFY,
            $totalBalance, $amount, $changeAmount, $giftAmount, $changeReason, $adminId, 0);
        $totalChangeAmount = $realBalance + $changeAmount;
        $totalGiftAmount = $giftBalance + $giftAmount;
        $this->updateUserBalanceByCommonCode($commonCode, $totalBalance, $totalChangeAmount, $totalGiftAmount);
        $sysAccount = SysAccountService::getInstance()->getSysAccountByShopIdAndSign($shopIdZong, $shopId, AccountType::MEMBER_PAY);
        $accountId = $sysAccount->id;
        $billSource = BillSourceService::getInstance()->getResourceBySign($shopId, \app\common\utils\dataDictionary\BillSource::BURU);
        $billSourceId = $billSource->id;
        SysAccountService::getInstance()->updateAccountAmount($shopIdZong, $shopId, $adminId, $accountId,
            BillType::MODIFY, $amount, $remark, $billSourceId, $commonCode, 0, null, []);
        MemberHookService::getInstance()->updateUserBalance($shopId, $commonCode, $amount);
        AdminLogService::getInstance()->saveAdminLog(LogLevel::EXCEPTION, $changeReason, $type, $adminId);
//		$logContent = "修改用户余额";
//		UserLogService::getInstance()->saveUserLog($commonCode, LogLevel::EXCEPTION, $logContent, 2, $adminId, $shopId);
    }

    /**
     * 管理员修改用积分
     * @param $shopId
     * @param $commonCode
     * @param $point
     * @param $remark
     * @param $adminId
     * @throws MyException
     */
    public function adminUpdateUserPoint($shopId, $commonCode, $point, $remark, $adminId)
    {
        $user = $this->getUserCommonByCommonCode($commonCode);
        $oldPoint = $user->point;
        $newPoint = $oldPoint + $point;
        if ($newPoint < 0) {
            throw new MyException(Code::FAIL, "积分不足");
        }
        $this->updateUserPoint($commonCode, $shopId, 0, BillType::MODIFY, ($point), $remark, $adminId);
        $logContent = "修改用户积分 $oldPoint 修改为 $newPoint";
        if (!empty($remark)) {
            $logContent .= "原因： $remark";
        }
        UserLogService::getInstance()->saveUserLog($commonCode, LogLevel::EXCEPTION, $logContent, 2, $adminId, $shopId);
    }

    /**
     * 支付码由 位置码 2位 会员码 18位 时间码 6位组成
     * 位置码取值范围 00- 23 时间码根据位置码插入到会员码中，位于支付码开头
     * 例如位置吗 为 05 会员码 202 303151559281602 时间戳秒 1686392901
     * 时间码 时间戳后6位 392901
     * 则支付码为 05202392901303151559281602
     * @param $memberCode
     * @return string
     * @throws MyException
     */
    private function getUserCode($memberCode)
    {
        if (strlen($memberCode) != 26) {
            throw new MyException(Code::FAIL, "会员码错误");
        }
        $indexCode = substr($memberCode, 0, 2);
        $payTimeStr = substr($memberCode, $indexCode + 2, 6);
        $userCodeStart = substr($memberCode, 2, $indexCode);
        $userCodeEnd = substr($memberCode, $indexCode + 6 + 2);
        $userCode = $userCodeStart . $userCodeEnd;
        $now = time();
        $payTime = substr("$now", 0, 4) . $payTimeStr;
        $tempTemp = $now - $payTime;
        if ($tempTemp > 60 || $tempTemp < 0) {
            throw new MyException(Code::FAIL, "会员码过期，请刷新会员码");
        }
        return $userCode;
    }

    public function getBirthdayUserList($shopIdZong, $date, $page, $limit)
    {
        $model = new UserCommonModel();
        return $model->getBirthdayUserList($shopIdZong, $date, $page, $limit);
    }


    public function updateHistoryAmount($shopIdZong)
    {
        $model = new UserCommonModel();
        $userList = $model->where('shop_id_zong', $shopIdZong)->where('balance', '>', 0)->select();
        foreach ($userList as $user) {
            $commonCode = $user->common_code;
            $userBalance = $user->balance;
            //$realBalance = $user->real_balance;
            //        $giftBalance = $user->gift_balance;
            $amountRes = MemberRechargeBillService::getInstance()->getUserRechargeAmount($commonCode);
            //sum_money,sum(a.give_money) as sum_give_money,sum(a.total_money) as sum_total_money
            $sum_money = $amountRes['sum_money'];
            $sum_give_money = $amountRes['sum_give_money'];
            $sum_total_money = $amountRes['sum_total_money'];
            if ($sum_total_money) {
                $realBalance = $sum_money / $sum_total_money * $userBalance;
                $giftBalance = $userBalance - $realBalance;
                $this->editUserCommon(['id' => $user['id'], 'real_balance' => sprintf("%.2f", $realBalance), 'gift_balance' => sprintf("%.2f", $giftBalance)]);
            }
        }
    }


}




