<?php

namespace app\admin\controller;

use app\common\model\shopStatement\Handover as HandoverModel;
use app\common\model\sysSetting\BillType;
use app\common\model\user\UserShopBalanceRecord as UserShopBalanceRecordModel;
use app\common\returnConfig\Code;
use app\common\returnConfig\Msg;
use app\common\returnConfig\ResultData;
use app\common\service\admin\RoleService;
use app\common\service\boss\BossPermissionService;
use app\common\service\catering\CateringBillService;
use app\common\service\catering\CateringTypePrinterService;
use app\common\service\common\ChinaCityService;
use app\common\service\coupon\CouponRecordService;
use app\common\service\distribution\DistributionBillService;
use app\common\service\distribution\DistributionRuleService;
use app\common\service\distribution\DistributionUserService;
use app\common\service\equipment\WifiService;
use app\common\service\express\ExpressService;
use app\common\service\finance\AccountFundUtil;
use app\common\service\finance\SxfLaunchLedgerService;
use app\common\service\finance\SysAccountService;
use app\common\service\finance\SysCashJournalService;
use app\common\service\hardware\LiftConfigService;
use app\common\service\hardware\LockService;
use app\common\service\hardware\printer\PrinterTemplateService;
use app\common\service\hardware\printer\PrinterUseService;
use app\common\service\hardware\printer\PrintUtil;
use app\common\service\hardware\RoomHardwareService;
use app\common\service\hardware\TtLockService;
use app\common\service\hook\HookService;
use app\common\service\hook\MemberHookService;
use app\common\service\hook\RoomHookService;
use app\common\service\initSysData\InitSysDataService;
use app\common\service\intermediary\IntermediaryRoomPriceService;
use app\common\service\intermediary\IntermediaryRoomPriceServiceService;
use app\common\service\intermediary\IntermediaryService;
use app\common\service\message\MessageRoleService;
use app\common\service\message\MessageTypeService;
use app\common\service\Pay\AdaPayUtil;
use app\common\service\Pay\JLPayUtilService;
use app\common\service\Pay\PayRecordService;
use app\common\service\Pay\sxf\SxfPayService;
use app\common\service\Pay\sxf\SxfRequestUtil;
use app\common\service\Pay\WechatPayNotifyService;
use app\common\service\Pay\WechatPayService;
use app\common\service\platform\PlatformMiniProgramService;
use app\common\service\platform\PlatformService;
use app\common\service\pointMall\PointMallBillService;
use app\common\service\recharge\MemberRechargeBillService;
use app\common\service\recharge\RechargeFundService;
use app\common\service\resource\ResourceStorageSettingService;
use app\common\service\room\apiStay\StandardRoomService;
use app\common\service\room\BreakfastCouponRecordService;
use app\common\service\room\RoomBillDetailPreService;
use app\common\service\room\RoomBillDetailService;
use app\common\service\room\RoomBillDetailTypeService;
use app\common\service\room\RoomBillLogService;
use app\common\service\room\RoomBillPriceDateService;
use app\common\service\room\RoomBillService;
use app\common\service\room\RoomBillStatusService;
use app\common\service\room\RoomBillUserService;
use app\common\service\room\RoomBillUtil;
use app\common\service\room\RoomBookCountService;
use app\common\service\room\RoomCleanRecordService;
use app\common\service\room\RoomClearStatusService;
use app\common\service\room\RoomFundUtil;
use app\common\service\room\RoomPriceService;
use app\common\service\room\RoomPriceServiceService;
use app\common\service\room\RoomPriceUtil;
use app\common\service\room\RoomSellTypeService;
use app\common\service\room\RoomService;
use app\common\service\room\RoomStatusRecordService;
use app\common\service\room\RoomTypeService;
use app\common\service\room\selfStay\WxStayService;
use app\common\service\room\team\TeamRoomService;
use app\common\service\room\team\TeamSelectRoomUtil;
use app\common\service\room\team\TeamStandardRoomBillService;
use app\common\service\shop\ShopService;
use app\common\service\shop\ShopUiService;
use app\common\service\shop\ShopUiTypeService;
use app\common\service\shopStatement\HandoverRecordService;
use app\common\service\shopStatement\HandoverService;
use app\common\service\shopStatement\ShopStatementDayService;
use app\common\service\shopStatement\ShopStatementDetailService;
use app\common\service\shopStatement\ShopStatementUtil;
use app\common\service\shuttleBus\ShuttleBusBillService;
use app\common\service\sms\AlismsService;
use app\common\service\store\StoreBillService;
use app\common\service\sxf\SxfConfigService;
use app\common\service\sxf\SxfIncomeNotifyService;
use app\common\service\sxf\SxfShopService;
use app\common\service\sysSetting\AdapayService;
use app\common\service\sysSetting\BillSourceService;
use app\common\service\sysSetting\BillTypeService;
use app\common\service\sysSetting\ConsumeTypeService;
use app\common\service\sysSetting\JlPayService;
use app\common\service\sysSetting\MiniProgramConfigService;
use app\common\service\sysSetting\MiniProgramPageQrService;
use app\common\service\sysSetting\NightCheckService;
use app\common\service\sysSetting\RichTextService;
use app\common\service\sysSetting\ShopDateService;
use app\common\service\sysSetting\ShopSecretService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\sysSetting\SysSettingService;
use app\common\service\sysSetting\UserSourceService;
use app\common\service\sysSetting\WxPayConfService;
use app\common\service\thridSystem\ThirdSystemApiService;
use app\common\service\thridSystem\ThirdSystemService;
use app\common\service\thridSystem\wlys\RoomBill;
use app\common\service\thridSystem\wlys\RoomStatus;
use app\common\service\ui\MiniProgramPagesService;
use app\common\service\ui\UiComponentLibService;
use app\common\service\user\MemberGradeBillService;
use app\common\service\user\MemberGradeService;
use app\common\service\user\MemberUtilService;
use app\common\service\user\UserBalanceRecordService;
use app\common\service\user\UserCommonService;
use app\common\service\user\UserOcrService;
use app\common\service\user\UserShopBalanceRecordService;
use app\common\service\user\UserShopBalanceService;
use app\common\sysConfig\MyException;
use app\common\test\Adapay;
use app\common\test\Test;
use app\common\utils\aliyun\AliOss;
use app\common\utils\CacheManager;
use app\common\utils\CmsPost\AgencyPost;
use app\common\utils\CmsPost\HotelPost;
use app\common\utils\CodeGenerator;
use app\common\utils\dataDictionary\AccountType;
use app\common\utils\dataDictionary\BillSource;
use app\common\utils\dataDictionary\DistributionType;
use app\common\utils\dataDictionary\RoomBillDetailType;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomHardWare;
use app\common\utils\dataDictionary\RoomLogLevel;
use app\common\utils\dataDictionary\RoomLogType;
use app\common\utils\dataDictionary\RoomRecordStatus;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\StoreBillStatus;
use app\common\utils\dataDictionary\SysSetting;
use app\common\utils\dataDictionary\ThirdSystem;
use app\common\utils\dataDictionary\ThirdSystemApi;
use app\common\utils\dataDictionary\UserSource;
use app\common\utils\dataDictionary\WxNotify;
use app\common\utils\DebugUtil;
use app\common\utils\FileUtil;
use app\common\utils\JavaSet;
use app\common\utils\jialian\Contants;
use app\common\utils\jialian\HttpClient;
use app\common\utils\jialian\OrderStatus;
use app\common\utils\jialian\SignUtils;
use app\common\utils\PositionUtil;
use app\common\utils\QR\WxQrUtil;
use app\common\utils\request\RequestUtil;
use app\common\utils\StringUtil;
use app\common\utils\tencent\OcrUtil;
use app\common\utils\tencent\PayConfig;
use app\common\utils\tencent\PayUtil;
use app\common\utils\testClear\ClearData;
use app\common\utils\TimeUtil;
use app\common\utils\updateVersion\DbUtil;
use app\common\utils\updateVersion\UpdateDb;
use app\common\utils\updateVersion\UpdateVersion;
use app\common\utils\weather\WeatherUtil;
use app\common\utils\ZipUtil;
use CURLFile;
use DateTime;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;
use think\Cache;
use think\Config;
use think\Db;
use think\Exception;
use think\Log;
use think\Request;

class Index
{


    public function index(Request $request)
    {
        try {
            $params = $request->param();
            Db::startTrans();
            $res = null;
//https://hotel-v3.hanwuxi.cn/sys/dev/data/public/index.php/admin/Index/index
//            TeamSelectRoomUtil::getInstance()->teamCheckIn($teamId, $userId);
//            $res = BreakfastCouponRecordService::getInstance()->getBreakfastCouponRecordList(1146, 300413, '',
//                "XiKUkOntiU5xKrsuYDHUAhpJWkZGYJmZ", 0, null, null, null,null, 1, 10);
//            list($roomTypeName, $billAmount, $commissionAmount, $overTimePrice, $originalCashPladge, $cashPledge, $priceArr, $grade, $servicePrice, $serviceName) =
//                RoomPriceUtil::getInstance()->getStandardRoomPriceByDate(1135, "89TDwysCdwTjoH8ps4YqCxkJIhA82PCZ", 1754905599, 1,
//                    14, 13, null, null, 2, 484);
//            $res = TimeUtil::getInstance()->getDateList(1756278000, 1756439999);
//            $res = LiftConfigService::getInstance()->getLiftConfigList(1135,null,null);
//            $res = PlatformMiniProgramService::getInstance()->getAuthorizerRefreshToken("queryauthcode@@@EWo6YhDCciy4bIm_Dabi4JgD6lFhod3EW9B0t84wtgO5owQU-ReQiDL6cBizWTrzgYzUnRDg_EJulWS_jaslYw");
            UpdateVersion::getInstance()->updateV3_3_66();
//            $res = WechatPayService::getInstance()->query(994,'Lu1YgRT4eq4KlA4T',1);
//            DebugUtil::log($params);
//            MemberGradeBillService::getInstance()->refund(30);
//            SxfLaunchLedgerService::getInstance()->addSxfLaunchLedger(36,2322);
            $file = $request->file('file');
            $res = SxfPayService::getInstance()->uploadFilePic(1, $file);
            Db::commit();
            ResultData::complexResult($res, Code::SUCCESS, Msg::getSuccess());
        } catch (Exception $exception) {
            Db::rollback();
            ResultData::returnErrorMsg($exception);
        }
    }

    public function fixDistribution()
    {
        $shopIdZong = null;
        $list = DistributionBillService::getInstance()->getDistributionBillList($shopIdZong, null, null, null, null, null, null, null, null, null, null);
        foreach ($list as $item) {
            $itemBillType = $item->bill_type;
            $itemBillId = $item->bill_id;
            if ($itemBillType == \app\common\utils\dataDictionary\BillType::BOOK_ROOM) {
                $roomBill = RoomBillService::getInstance()->getBillById($itemBillId);
                if (empty($roomBill)) {
                    DistributionBillService::getInstance()->delDistributionBillById($item->id);
                }
            } elseif ($itemBillType == \app\common\utils\dataDictionary\BillType::CASH_RECHARGE) {
                $memberRechargeBill = MemberRechargeBillService::getInstance()->getBillById($itemBillId);
                if (empty($memberRechargeBill)) {
                    DistributionBillService::getInstance()->delDistributionBillById($item->id);
                }
            } elseif ($itemBillType == \app\common\utils\dataDictionary\BillType::STORE) {
                $storeBill = StoreBillService::getInstance()->getStoreBillById($itemBillId);
                if (empty($storeBill)) {
                    DistributionBillService::getInstance()->delDistributionBillById($item->id);
                }
            } elseif ($itemBillType == \app\common\utils\dataDictionary\BillType::CATERING) {
                $cateringBill = CateringBillService::getInstance()->getCateringBillById($itemBillId);
                if (empty($cateringBill)) {
                    DistributionBillService::getInstance()->delDistributionBillById($item->id);
                }
            }
        }

        $userList = DistributionUserService::getInstance()->getDistributionAdminUserList($shopIdZong, null, null, null, null, null, null);
        foreach ($userList as $item) {
            $id = $item['id'];
            $amount = DistributionBillService::getInstance()->getTotalDistributionAmount($shopIdZong, null, $id, null, null, null);
            DistributionUserService::getInstance()->editDistributionUser(['id' => $id, 'distribution_amount' => $amount, 'total_distribution_amount' => $amount]);
        }

    }

    private function fixUserRechargeDistribution()
    {
        $shopIdZong = null;
        $startTime = 1754485200;
        $list = MemberRechargeBillService::getInstance()->getMemberRechargeBillList($shopIdZong, null, null, null, null,
            null, null, $startTime, null, null, null);
        foreach ($list as $item) {
            $itemBillId = $item['id'];
            $itemShopIdZong = $item['shop_id_zong'];
            $record = DistributionBillService::getInstance()->getDistributionBillByBillId($itemShopIdZong, $itemBillId, \app\common\utils\dataDictionary\BillType::CASH_RECHARGE);
            if (empty($record)) {
                DistributionBillService::getInstance()->addMemberRecharge($itemShopIdZong, $itemShopIdZong, $itemBillId, null);
            }
        }

    }

    private function fixAccount()
    {
        $list = Db::query("select * from hp_3_shop");
        foreach ($list as $item) {
            $shopId = $item['id'];
            $shopPid = $item['shop_pid'];
            $shopIdZong = $shopPid == 0 ? $shopId : $shopPid;
            $accountList = SysAccountService::getInstance()->getSysShopAccountList($shopIdZong, $shopId, null, null, null, null, null, null);
            foreach ($accountList as $account) {
                $accountId = $account['id'];
                $recordList = Db::query("select * from hp_3_sys_cash_journal where account_id = :accountId order by id asc", ['accountId' => $accountId]);
                $totalAmount = 0;
                foreach ($recordList as $index => $record) {
                    $tempChange = $record['change'];
                    $tempId = $record['id'];
                    $totalAmount += $tempChange;
                    SysCashJournalService::getInstance()->editSysCashJournal(['id' => $tempId, 'amount' => $totalAmount,]);
                }
                SysAccountService::getInstance()->editSysAccount(['id' => $accountId, 'amount' => $totalAmount]);
            }
        }
    }

    private function fixHandoverRecord()
    {
        $list = Db::query("select * from hp_3_shop");
        foreach ($list as $item) {
            $shopId = $item['id'];
            $shopPid = $item['shop_pid'];
            $shopIdZong = $shopPid == 0 ? $shopId : $shopPid;
            $handoverList = Db::query("select * from hp_3_handover where shop_id=:shopId order by id asc ", ['shopId' => $shopId]);
            foreach ($handoverList as $index => $handover) {
                $handoverId = $handover['id'];
                $startTime = $handover['start_time'];
                $endTime = $handover['end_time'];
                $handover_version = $handover['handover_version'];
                if ($index == 0) {
                    $lastHandoverId = 0;
                } else {
                    $lastHandoverId = $handoverList[$index - 1]['id'];
                }
                $handoverRecordList = Db::query("select * from hp_3_handover_record where handover_id=:handoverId order by id asc ", ['handoverId' => $handoverId]);
                if ($handoverRecordList) {
                    $id = $handoverRecordList[0]['id'];
                    HandoverService::getInstance()->getHandoverFixData($shopIdZong, $shopId, $id, $lastHandoverId, $handover_version, $startTime, $endTime);
                }

            }
        }
    }

    private function putJsonExt()
    {
        $platform = PlatformService::getInstance()->getPlatform();
        $hostUrl = $platform->host_url;
        $cotnetStr = [
            "extEnable" => true,
            "extAppid" => "",
            "directCommit" => false,
            "ext" => ["host_url" => $hostUrl],
            "plugins" => [
                "myPlugin" => [
                    "version" => "3.1.0",
                    "provider" => "wx43d5971c94455481"
                ],
                "yayaLock" => [
                    "version" => "1.1.87",
                    "provider" => "wx252c9e1868833f3d"
                ]
            ]
        ];
        $filePath = Config::get('RESOURCE_PATH') . DIRECTORY_SEPARATOR . 'wxxcx';
        $fileName = "ext.json";
        file_put_contents($filePath . DIRECTORY_SEPARATOR . $fileName, json_encode($cotnetStr));
    }

    private function sxfTest($authCode)
    {
        $shopId = 36;
//        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
//        $mno = $config['mno'];
//        $orgId = $config['org_id'];
//        $privateKey = $config['private_key'];
//        $sxf = new SxfRequestUtil($orgId, $privateKey);
        $outTradeNo = StringUtil::getRandomStrs(16, true, true, true);
//        $ordNo = StringUtil::getRandomStrs(16, true, true, true);
//        DebugUtil::log($outTradeNo);
//        return $sxf->reverseScan($mno, $outTradeNo, 0.01, $authCode, "测试支付");
//        $outTradeNo = "E0Fdp7rzx2yE9uEa";
//        return $sxf->tradeQuery($mno, $outTradeNo);
//        return $sxf->jsapiScan($mno, $outTradeNo, 0.01, "测试支付", "opWHH5XJLzBqoFxxqncl3J7VpKfE");
//        $refundReason = "测试退款";
//        return $sxf->refund($mno, $ordNo, $outTradeNo, 0.01, $refundReason);
//        return $sxf->activePlusScan($mno, $ordNo, 0.01, "ALIPAY", "测试支付");
        return SxfPayService::getInstance()->activePlusScan($shopId, $outTradeNo, 0.01, "ALIPAY", "测试支付");
    }

    private function verifySign($param)
    {
        $shopId = 36;
        $config = SxfConfigService::getInstance()->getShopSxfConfig($shopId);
        $mno = $config['mno'];
        $orgId = $config['org_id'];
        $privateKey = $config['private_key'];
        $sxf = new SxfRequestUtil($orgId, $privateKey);
        return $sxf->verifySign($param);
    }

    private function cancelTeamBill()
    {
        $teamId = 0;
        $shopId = 0;
        $bills = RoomBillService::getInstance()->getTeamBill($shopId, null, $teamId);
        foreach ($bills as $bill) {
            $billId = $bill->id;
            RoomStatusRecordService::getInstance()->delRecordByBillId($billId);
            RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'bill_status' => 7]);
        }
    }

    //{"url":"https://api.uyuexing.com/api/vt/tpDiscount/chargePile/uyxCharge","parkCode":"D8CB8A357EB1R78NE","secret":"a0c70974-9118-42f9-81e6-ef7581f7cfb4"}

    public function fixStoreBill()
    {
        $shopId = 793;
        $shopIdZong = 793;
        $list = StoreBillService::getInstance()->getList($shopId, null, StoreBillStatus::FINISHED, null, null, null, null, null, null);
        foreach ($list as $bill) {
            $billId = $bill->id;
            $accountId = $bill->account_id;
            $commonCode = $bill->common_code;
            $billSourceId = $bill->bill_source;
            $amount = $bill->bill_amount;
            $old = SysCashJournalService::getInstance()->getSysCashJournalByItemBillId($shopId, \app\common\utils\dataDictionary\BillType::STORE, $accountId, $billId, null);
            if (empty($old)) {
                SysAccountService::getInstance()->updateAccountAmount($shopIdZong, $shopId, 0, $accountId,
                    \app\common\utils\dataDictionary\BillType::STORE, $amount, '', $billSourceId, $commonCode, $billId, 0, []);
            }
        }

    }

    private function fixShop()
    {
        try {
            $shopId = 1082;
            $shopPid = 0;
            if ($shopPid == 0) {
                $shopIdZong = $shopId;
            } else {
                $shopIdZong = $shopPid;
            }
            $old = ShopService::getInstance()->getShopInfoById($shopId, true);
            if (!$old) {
                $itemShopData = [
                    'id' => 1082,
                    'customer_id' => 596,
                    'shop_pid' => $shopPid,
                    'shop_name' => "派霓迪酒店",
                    'limit_time' => **********,
                    'last_login_time' => 0,
                    'pic_list' => [],
                ];
                $itemShopData['facility'] = [];
                $itemShopData['tags'] = [];
                $itemShopData['last_login_time'] = time();
                ShopService::getInstance()->recoveryShopData($itemShopData);
                if ($shopPid == 0) {//总店
                    InitSysDataService::getInstance()->initMainShop($shopIdZong);
                } else {//分店
                    InitSysDataService::getInstance()->initBranchShop($shopIdZong, $shopId);
                }
                RoleService::getInstance()->checkRoles($shopId);
            }

        } catch (Exception $exception) {
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($exception->getMessage(), 'error');
        }
        Db::table("hp_3_user_common")->where('shop_id_zong', 1082)->update(['growth' => 0, 'user_source' => 985]);

    }


    private function batchWifi()
    {
        $shopId = 1;
        $rooms = RoomService::getInstance()->getSimpleRoomList($shopId, null, null);
        $roomWifis = WifiService::getInstance()->getWifiList($shopId, null, null, null, null);
//        foreach ($rooms as $room) {
//            $roomId = $room->id;
//            $roomName = $room->room_number;
//            $wifiName = "ChinaNet-$roomName";
//            $flag = false;
//            $wifiId = 0;
//            foreach ($roomWifis as $roomWifi){
//                if ($roomWifi->wifi_name == $wifiName){
//                    $wifiId = $roomWifi->id;
//                    $flag = true;
//                    break;
//                }
//            }
//            if (!$flag){
//                WifiService::getInstance()->updateWifi(
//                    ['shop_id'=>$shopId,'status'=>1,'password'=>"66138888",'wifi_name'=>$wifiName],
//                    $shopId);
//                $hardware = RoomHardwareService::getInstance()->getRoomHardwareByHardWareId($shopId,"wifi",$wifiId);
//                if (empty($hardware)){
//                    RoomHardwareService::getInstance()->addRoomWifi($shopId,$roomId,$wifiId);
//                }
//            }
//        }
        DebugUtil::log(count($rooms));
        DebugUtil::log(count($roomWifis));
        foreach ($roomWifis as $roomWifi) {
            $wifiId = $roomWifi->id;
            foreach ($rooms as $room) {
                $roomName = $room->room_number;
                $wifiName = "ChinaNet-$roomName";
                if ($roomWifi->wifi_name == $wifiName) {
                    $roomId = $room->id;
                    $hardware = RoomHardwareService::getInstance()->getRoomHardwareByHardWareId($shopId, "wifi", $wifiId, 0);
                    if (empty($hardware)) {
                        RoomHardwareService::getInstance()->addRoomWifi($shopId, $roomId, $wifiId);
                    }
                    break;
                }
            }
        }

    }


    private function finishToStaying()
    {
        $billId = 1017;
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $roomId = $bill->room_id;
        RoomBillService::getInstance()->editRoomBill(['id' => $billId, 'leave_time' => null, 'bill_status' => 4]);
        $record = RoomStatusRecordService::getInstance()->getCurrentRoomStatusRecord($billId, $roomId);
        RoomStatusRecordService::getInstance()->editRoomStatusRecord(['id' => $record->id, 'end_time_plan' => $bill->leave_time_plan, 'end_time_actual' => null]);
    }


    private function fixThirdApi()
    {
        $shops = ShopService::getInstance()->getAllSysShops(null);
        foreach ($shops as $shop) {
            $shopId = $shop->id;
            ThirdSystemService::getInstance()->initData($shopId);
        }
    }

    private function fixConsumeType()
    {
        $shops = ShopService::getInstance()->getAllSysShops(null);
        foreach ($shops as $shop) {
            $shopId = $shop->id;
            ConsumeTypeService::getInstance()->initData($shopId);
        }
    }


    private function fixDetail()
    {
        $startDate = "2024-03-01";
        $endDate = "2024-04-11";
        $index = 0;
        while (date("Y-m-d", strtotime($startDate) + $index * 24 * 3600) != $endDate) {
            $tempDate = date("Y-m-d", strtotime($startDate) + $index * 24 * 3600);
            $shopId = 743;
            $shopDate = ShopDateService::getInstance()->getShopDateByDate($shopId, $tempDate);
            if (empty($startDate)) {
                continue;
            }
            $startTime = $shopDate->start_time;
            $endTime = $shopDate->end_time;
            $handoverVersion = $tempDate . '_01';
            DebugUtil::log($handoverVersion);
            if (!$endTime) {
                continue;
            }
            $index++;
            ShopStatementUtil::getInstance()->fixShopStatementDetail($shopId, $tempDate, $handoverVersion, $startTime, $endTime);
        }
    }

    private function teamTest()
    {
        DebugUtil::log("teamTest");
        $userId = 10297;
        $shopIdZong = 717;
        $teamId = 140;
        $shareCode = "ebOxNl";
        TeamSelectRoomUtil::getInstance()->confirmTeam($userId, $teamId, $shareCode, -1, null);
        TeamStandardRoomBillService::getInstance()->payTeamRoomAmount($shopIdZong, $userId, $teamId, 0, 2);
    }


    public function fixPoint()
    {
        $phone = "18884054608";
        $shopIdZong = 793;
        $user = UserCommonService::getInstance()->getUserByPhone($shopIdZong, $phone);
        $commonCode = $user->common_code;
//		$amount = RoomBillDetailService::getInstance()->getRoomBillDetailAmountAboutTime(null, 2, null, null);
        $amount = Db::table("hp_3_room_bill_detail")->where('common_code', $commonCode)->where('type', 2)->sum("amount");
        $pointSetting = SysSettingService::getInstance()->getSysSettingBySign($shopIdZong, SysSetting::POINT_GIVE_CONSUME);
        $pointSettingProperty = $pointSetting->property;
        $finalPoint = intval($amount * $pointSettingProperty['amount'] / $pointSettingProperty['point']);
        DebugUtil::log($finalPoint);
        UserCommonService::getInstance()->updateUserPoint($commonCode, $shopIdZong, 0, 99, $finalPoint, "积分补录", 0);
    }


    private function en()
    {
        $content = file_get_contents('/www/wwwroot/try-hotel.hanwuxi.cn/sys/v3.2.09/data/application/common/filter/ApiController.php');
        $start_1 = stripos($content, 'eval') + 14;
        $start_2 = strlen($content) - $start_1 - 8;
        $code = substr($content, $start_1, $start_2);
        $code = base64_decode($code);
        $start1 = 9;
        $start2 = stripos($code, ';');
        $O0O000 = substr($code, $start1, $start2 - $start1 - 1);
        $code = base64_decode(strtr(substr($O0O000, 52 * 2), substr($O0O000, 52, 52), substr($O0O000, 0, 52)));
        return $code;
    }

    private function makeSign()
    {
        $shopId = 36;
        $shopSecret = ShopSecretService::getInstance()->getShopSecret($shopId);
        if (empty($shopSecret)) {
            throw new MyException(Code::FAIL, "会员支付功能未开通");
        }
        $secret = $shopSecret->secret;
        $params = ['hotel_code' => "Puyhm40aMBjP", "member_code" => "05202392901303151559281602", 'amount' => 1, 'remark' => "支付测试", 'time' => time()];
        //签名步骤一：按字典序排序参数
        ksort($params);
        $string = $this->ToUrlParams($params);
        //签名步骤二：在string后加入KEY
        $string .= "&secret=$secret";
        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }

    /**
     * 格式化参数格式化成url参数
     * @param $params
     * @return string
     */
    private function ToUrlParams($params)
    {
        $buff = "";
        foreach ($params as $k => $v) {
            if ($k != "sign" && $v != "" && !is_array($v)) {
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }


    public function fixUserBalance()
    {
        $commonCode = "";
        $shopIdZong = 0;
        $shops = ShopService::getInstance()->getAllShops($shopIdZong, 1, null, null, null);
        foreach ($shops as $shop) {
            $shopid = $shop->id;
            $shopUserBalance = UserShopBalanceService::getInstance()->getUserShopBalance($commonCode, $shopid);
            if ($shopUserBalance) {
                $sumRes = Db::table("hp_3_user_shop_balance_record")->where('common_code', $commonCode)->where('shop_id', $shopid)->sum("change");
                $sumAmount = $sumRes['balance'];
                if ($shopUserBalance->balance != $sumAmount) {
                    UserShopBalanceService::getInstance()->editUserShopBalance(['id' => $shopUserBalance->id, 'balance' => $sumAmount]);
                }
            }

        }
    }


    private function getBill()
    {
        $shopId = 956;
        $billCode = "";
        $shop = ShopService::getInstance()->getShopById(956);
        if (empty($shop)) {
            return;
        }
        $shopId = $shop->id;
        $shopPid = $shop->shop_pid;
        $hotelCode = $shop->hotel_code;
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        if ($thirdSystem->sign == ThirdSystem::JINGFEI) {
            return;
        }
        $thirdSystemId = $thirdSystem->id;
        $findOrderSetting = ThirdSystemApiService::getInstance()->getThirdSystemApiBySign($thirdSystemId, ThirdSystemApi::FIND_ROOM_BILL);
        $apiId = $findOrderSetting->id;
        $url = $findOrderSetting->api_url;
        $requestType = $findOrderSetting->request_type;
        $params = [];
        $params['timestamp'] = time();
        $params['key'] = $findOrderSetting->key;
        $params['hotel_code'] = $hotelCode;
        $params['bill_code'] = $billCode;
        $res = RequestUtil::getInstance()->requestData($url, $requestType, $params, true);
        DebugUtil::log($params);
        DebugUtil::log($res);
    }

    /*
     * {
  "name": "1045",
  "pwd": "e10adc3949ba59abbe56e057f20f883e"
}
    name: 2025
pwd: d60d16da8b1bec34f3508e8c6914cf94

     */


    private function insertMember()
    {
        $shopIdZong = 0;
        $content = json_decode(file_get_contents('users.json'), true);
        $users = $content['RECORDS'];
        $userSource = UserSourceService::getInstance()->getUserSourceBySign($shopIdZong, UserSource::BURU);
        $userSourceId = $userSource->id;
        $index = -1;
        foreach ($users as $user) {
            $index++;
            if ($index < 100) {

                continue;
            }
            if ($index > 200) {
                break;
            }
            $phone = $user['phone'];
            $name = $user['name'];
            $gender = $user['gender'];
            $balance = $user['balance'];
            $point = $user['point'];
            $growth = $user['growth'];
            $identification_number = $user['id_card'];
            $old = UserCommonService::getInstance()->getUserByPhone($shopIdZong, $phone);
            if (empty($old)) {
                $newUser = MemberUtilService::getInstance()->addNewUser($shopIdZong, $shopIdZong, $userSourceId, $name, $phone, $gender, $identification_number, 1);
                $commonCode = $newUser->common_code;
                if ($balance > 0) {
                    UserCommonService::getInstance()->updateUserBalance($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $balance, "旧系统转移", 0);
                }
                if ($point > 0) {
                    UserCommonService::getInstance()->updateUserPoint($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $point, "旧系统转移", 0);
                }
                if ($growth > 0) {
                    UserCommonService::getInstance()->updateUserGrowth($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $growth, "旧系统转移", 0);
                }
            } else {
                $commonCode = $old->common_code;
                if ($balance > 0) {
                    $change = Db::table('hp_3_user_balance_record')->where('change_reason', '旧系统转移')->find();
                    if (empty($change)) {
                        UserCommonService::getInstance()->updateUserBalance($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $balance, "旧系统转移", 0);
                    }
                }
                if ($point > 0) {
                    $change = Db::table('hp_3_user_point_record')->where('change_reason', '旧系统转移')->find();
                    if (empty($change)) {
                        UserCommonService::getInstance()->updateUserPoint($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $point, "旧系统转移", 0);
                    }
                }
                if ($growth > 0) {
                    $change = Db::table('hp_3_user_growth_record')->where('change_reason', '旧系统转移')->find();
                    if (empty($change)) {
                        UserCommonService::getInstance()->updateUserGrowth($commonCode, $shopIdZong, 0, \app\common\utils\dataDictionary\BillType::MODIFY, $growth, "旧系统转移", 0);
                    }
                }
            }
        }
    }


    private function fixGrade()
    {
        $shopId = 0;
        $users = Db::table('hp_3_user_common')->where('growth', '>', 0)->select();
        $userIds = [];
        foreach ($users as $user) {
            $userId = $user['id'];
            $userGrowth = $user['growth'];
            $userGrade = $user['grade'];
            $grade = MemberGradeService::getInstance()->getMemberGradeByGrowth($shopId, $userGrowth);
            $gradeId = $grade->id;
            if ($userGrade != $gradeId) {
                Db::table('hp_3_user_common')->where('id', $userId)->update(['grade' => $gradeId]);
            }
        }
    }

    public function codeGenerator(Request $request)
    {
        $table = request()->param('tableName');
        $dataBaseName = Config::get('database')['database'];
        CodeGenerator::getInstance()->generator($dataBaseName, 'hp_3', $table);
    }

    private function uploadFileToOss($version, $name, $localPath)
    {
        // 设置PHP执行时间限制，防止超时
        set_time_limit(600); // 10分钟
        ini_set('memory_limit', '512M'); // 增加内存限制

        $accessKeyId = 'LTAI5t8SXBR9hnR6LqzfLQ5p';
        $accessKeySecret = '******************************';
//        $endpoint = 'oss-cn-beijing-internal.aliyuncs.com';
        $endpoint = 'oss-cn-beijing.aliyuncs.com';
        $bucket = 'zqwhale2';
        $domain = "";

        // 检查文件是否存在
        if (!file_exists($localPath)) {
            throw new MyException(Code::FAIL, "上传文件不存在: $localPath");
        }

        // 记录文件大小
        $fileSize = filesize($localPath);
        DebugUtil::log("准备上传文件到OSS: $name, 文件大小: " . ($fileSize / 1024 / 1024) . " MB");

        $aliossUtil = new AliOss();

        try {
            // 使用优化的上传方法
            $filUrl = $aliossUtil->uploadFileOptimized($accessKeyId, $accessKeySecret, $endpoint,
                $bucket, 'version_web_v3pro/' . $version . '/' . $name, $localPath, true);

            DebugUtil::log("OSS上传成功: $filUrl");
            return $filUrl;
        } catch (Exception $e) {
            DebugUtil::log("OSS上传失败: " . $e->getMessage(), 'error');
            throw new MyException(Code::FAIL, "OSS上传失败: " . $e->getMessage());
        }
    }


    public function codeEncode(Request $request)
    {
        DebugUtil::log($request->param());
        $version = $request->param('version');
        $sysVersion = Config::get("sys_version");
//        DebugUtil::log("当前版本: $sysVersion, 准备升级版本: $version");
        $res = null;
        if (!$version || $version == 'dev') {
            throw new MyException(Code::FAIL, "版本号错误");
        }
//        if ($version && $version != $sysVersion) {
//            $configFile = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/dev/data/application/config.php";
//            $configFileContent = file_get_contents($configFile);
////            DebugUtil::log($configFileContent);
//            $configFileContent = str_replace($sysVersion, $version, $configFileContent);
////            DebugUtil::log($configFileContent);
//            file_put_contents($configFile, $configFileContent);
//        }
//        $this->uploadFileToOss($version, "v3.3.71.zip", "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/v3.3.71/v3.3.71.zip");
        $encode = $request->param('encode', false);
        $zip = $request->param('zip', true);
        $step = $request->param('step', 1);
        $res = $this->codeEncodeCommand($version);
//        $versionId = 808;
//        $filePath = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/" . $version . '/' . $version . '.zip';
//        $url = "https://hotelcms-v3pro.hanwuxi.cn/data/public/index.php/cms_web/product/uploadWebVersionFile";
//        $authtoken = "cms_0d281d24f8f3c7028f9f847c3d685e15_8350";
//        $res = $this->uploadWebVersionFile($url, $authtoken, $versionId, $version, $filePath);
//        DebugUtil::log("上传结果：" . json_encode($res));
        ResultData::complexResult($res);
    }


    private function RandAbc($length = "")
    { // 返回随机字符串
        $str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        return str_shuffle($str);
    }
    //.showdoc.cc/2265761017603803
    //vzvHZqRn

    private function encode($filename)
    {
//        DebugUtil::log("开始加密文件：$filename");
        $T_k1 = $this->RandAbc(); //随机密匙1
        $T_k2 = $this->RandAbc(); //随机密匙2
        $vstr = file_get_contents($filename);
        $v1 = base64_encode($vstr);
        $c = strtr($v1, $T_k1, $T_k2); //根据密匙替换对应字符。
        $c = $T_k1 . $T_k2 . $c;
        $q1 = "O00O0O";
        $q2 = "O0O000";
        $q3 = "O0OO00";
        $q4 = "OO0O00";
        $q5 = "OO0000";
        $q6 = "O00OO0";
        $s = '$' . $q6 . '=urldecode("%6E1%7A%62%2F%6D%615%5C%76%740%6928%2D%70%78%75%71%79%2A6%6C%72%6B%64%679%5F%65%68%63%73%77%6F4%2B%6637%6A");$'
            . $q1 . '=$' . $q6 . '{3}.$' . $q6 . '{6}.$' . $q6 . '{33}.$' . $q6 . '{30};$' . $q3 . '=$' . $q6 . '{33}.$' . $q6 . '{10}.$' . $q6 . '{24}.$' . $q6 . '{10}.$' . $q6 . '{24};$' . $q4 .
            '=$' . $q3 . '{0}.$' . $q6 . '{18}.$' . $q6 . '{3}.$' . $q3 . '{0}.$' . $q3 . '{1}.$' . $q6 . '{24};$' . $q5 . '=$' . $q6 . '{7}.$' . $q6 . '{13};$' . $q1 . '.=$' . $q6 . '{22}.
            $' . $q6 . '{36}.$' . $q6 . '{29}.$' . $q6 . '{26}.$' . $q6 . '{30}.$' . $q6 . '{32}.$' . $q6 . '{35}.$' . $q6 . '{26}.$' . $q6 . '{30};eval($' . $q1 .
            '("' . base64_encode('$' . $q2 . '="' . $c . '";eval(\'?>\'.$' . $q1 . '($' . $q3 . '($' . $q4 . '($' . $q2 . ',$' . $q5 . '*2),$' . $q4 . '($' . $q2 . ',$' . $q5 . ',$' . $q5 . '),$' . $q4 . '($' . $q2 . ',0,$' . $q5 . '))));') . '"));';


        $s = '<?php ' . "\n" . $s . "\n" . ' ?>';
        $fpp1 = fopen($filename, 'w');
        fwrite($fpp1, $s) or die('写文件错误');
    }

    private function copyFile($fromPath, $toPath)
    {
        FileUtil::getInstance()->delFile($toPath);
        FileUtil::getInstance()->smkdir($toPath);
        FileUtil::getInstance()->copyDir($fromPath, $toPath);
        if (file_exists($toPath . DS . 'data' . DS . 'application' . DS . 'admin' . DS . 'controller' . DS . 'Index.php')) {
            unlink($toPath . DS . 'data' . DS . 'application' . DS . 'admin' . DS . 'controller' . DS . 'Index.php');
        }
    }


    public function fixCity()
    {
        $list = Db::table('t_city')->select();
        $pid0 = 0;
        $pid1 = 0;
        $pid2 = 0;
        foreach ($list as $item) {
            $itemCityName = $item['city_name'];
            DebugUtil::log("_$itemCityName");
            $count = mb_strlen($itemCityName) - mb_strlen(str_replace(" ", "", $itemCityName));
            if ($count == 0) {
                Db::table('t_city')->where('id', $item['id'])->update(['pid' => $pid0]);
                $pid1 = $item['id'];
                $pid2 = 0;
            } elseif ($count == 1) {
                $pid2 = $item['id'];
                Db::table('t_city')->where('id', $item['id'])->update(['pid' => $pid1, 'city_name' => str_replace(" ", "", $itemCityName)]);
            } else {
                if ($pid2 == 0) {
                    $pid2 = $pid1;
                }
                Db::table('t_city')->where('id', $item['id'])->update(['pid' => $pid2, 'city_name' => str_replace(" ", "", $itemCityName)]);
            }
        }
    }


    /**
     * 命令行版本的代码加密处理
     */
    public function codeEncodeCommand($version, $encode = true, $zip = true, $step = 1)
    {
        $result = [
            'success' => false,
            'message' => '',
            'data' => []
        ];

        try {
//            $devEnv = Config::get("dev_env");
////            DebugUtil::log("当前环境：$devEnv");
//            if ($devEnv == 'local'){
//                $fromPath = "D:".DS.DS."developProject".DS."php".DS."hotel_pms";
//                $toPath = "D:".DS.DS."developProject".DS."php".DS."sys".DS.$version;
//                $zipPath = "D:".DS.DS."developProject".DS."php".DS."sys".DS.$version;
//            } else {
//            }
            $fromPath = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/dev";
            $toPath = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/$version";
            $zipPath = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/$version";

            $encodePath = $toPath . DS . 'data' . DS . "application" . DS . "common";
            $fileName = $version . '.zip';
            $count = 0;
            if ($encode) {
                if ($step == 1) {
                    $this->copyFile($fromPath, $toPath);
                    Test::getInstance()->clearLogs($zipPath);
                    Cache::clear();
                    $flag = false;
                    $dirs = [];
                    $excludeFile = [];
                    FileUtil::getInstance()->getDir($encodePath, $dirs);
                    foreach ($dirs as $dir) {
                        if (substr_compare($dir, '.php', -strlen('.php')) === 0) {
                            // 使用DIRECTORY_SEPARATOR来分割路径，兼容Windows和Linux
                            $strs = explode(DS, $dir);
                            $index = count($strs) - 1;
                            if (in_array($strs[$index], $excludeFile)) {
                                continue;
                            }
                            $this->encode($dir);
                            $count++;
                        }
                    }
                    $result['data']['encoded_files'] = $count;

                    if ($zip) {
                        ZipUtil::getInstance()->zipDirs($zipPath, $zipPath . DS . $fileName, $zipPath);
                    }
                }
                $ossUrl = null;
//                if ($step == 2) {
//                    $localPath = $zipPath . '/' . $fileName;
//                    $ossUrl = $this->uploadFileToOss($version, $fileName, $localPath);
//                    DebugUtil::log("OSS上传完成: " . $ossUrl);
//                    $result['data']['zip_file'] = $fileName;
//                    $result['data']['oss_url'] = $ossUrl;
//                    $versionId = 808;
//                    $filePath = "/www/wwwroot/hotel-v3.hanwuxi.cn/sys/" . $version . '/' . $version . '.zip';
//                    $url = "https://hotelcms-v3pro.hanwuxi.cn/data/public/index.php/cms_web/product/uploadWebVersionFile";
//                    $authtoken = "cms_0d281d24f8f3c7028f9f847c3d685e15_8350";
//                    $res = $this->uploadWebVersionFile($url, $authtoken, $versionId, $version, $filePath);
//                    DebugUtil::log("上传结果：" . json_encode($res));
//                }
//
//                if ($step == 3) {
//                    // 这里可以重新启用数据库操作
//                    $this->saveVersionRecord($version, $fileName, $ossUrl);
//                }

                $result['success'] = true;
                $result['message'] = "代码加密完成，共处理 {$count} 个文件";
                ///https://hotel-v3.hanwuxi.cn/sys/
                $url = "https://hotel-v3.hanwuxi.cn/sys/$version/$version.zip";
                return ['url' => $url, 'path' => $toPath . '/' . $fileName];
            }
        } catch (Exception $exception) {
            $result['success'] = false;
            $result['message'] = $exception->getMessage();
            $result['error'] = $exception->getTraceAsString();
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
        }

        return $result;
    }

    function uploadWebVersionFile($url, $authToken, $id, $version, $filePath)
    {
        // 检查文件路径是否存在
        if (!file_exists($filePath)) {
            return ["error" => "错误：文件路径 {$filePath} 不存在"];
        }
        try {
            $client = new Client();
            // 使用GuzzleHttp\Client发送请求
            $response = $client->request('POST', $url, [
                'multipart' => [
                    [
                        'name' => 'id',
                        'contents' => $id
                    ],
                    [
                        'name' => 'version',
                        'contents' => $version
                    ],
                    [
                        'name' => 'file',
                        'contents' => fopen($filePath, 'r'),
                        'filename' => basename($filePath)
                    ]
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Authtoken' => $authToken
                ],
                'verify' => true // 启用SSL证书验证
            ]);
            // 处理响应
            return [
                'httpCode' => $response->getStatusCode(),
                'response' => $response->getBody()->getContents()
            ];
        } catch (RequestException $e) {
            // 处理请求异常
            return [
                'error' => "请求错误: " . $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ];
        } catch (\Exception $e) {
            // 处理其他异常
            return [
                'error' => "错误: " . $e->getMessage()
            ];
        }
    }

    private function saveVersionRecord($version, $fileName, $ossUrl)
    {
        try {// 配置外部数据库连接
            $externalDbConfig = [
                'type' => 'mysql',
                'hostname' => 'rm-2zec858eqe8dm9s6u.rwlb.rds.aliyuncs.com',
                'database' => 'hotelcms_v3_pro',
                'username' => 'hotelcms_v3_pro',
                'password' => 'cOfUiLAtKigHP55u3',
                'hostport' => '3306',
                'charset' => 'utf8',
                'prefix' => '',
            ];

            // 检查当前版本是否已存在
            $existingVersion = Db::connect($externalDbConfig)->query("SELECT id FROM t_app_web_version WHERE version = ?", [$version]);

            // 获取 t_app_web_version_sql 表最后一条记录的id
            $lastSqlVersion = Db::connect($externalDbConfig)->query("SELECT id FROM t_app_web_version_sql ORDER BY id DESC LIMIT 1");
            $versionSql = $lastSqlVersion ? $lastSqlVersion[0]['id'] : 0;

            // 获取 t_app_wxapp_version 表最后一条记录的id
            $lastWxappVersion = Db::connect($externalDbConfig)->query("SELECT id FROM t_app_wxapp_version ORDER BY id DESC LIMIT 1");
            $versionWxapp = $lastWxappVersion ? $lastWxappVersion[0]['id'] : 0;

            $currentTime = time();

            if ($existingVersion) {
                // 记录已存在，执行更新操作
                $updateData = [
                    'version_sql' => $versionSql,
                    'version_wxapp' => $versionWxapp,
                    'version_wxapp_is_new' => 1,
                    'title' => '普通进化',
                    'desc' => '',
                    'desc_detail' => '',
                    'project_path' => '',
                    'oss_url' => $ossUrl,
                    'file_name' => $fileName,
                    'status' => 1,
                    'can_use' => 0,
                    'is_developing' => 1,
                    'rely_on_it' => 0,
                    'upload_oss_time' => 0,
                    'upload_total_time' => 0,
                    'last_edit_time' => $currentTime,
                    'upload_time' => $currentTime,
                    'use_num' => 0,
                    'use_percent' => 0
                ];

                Db::connect($externalDbConfig)->table('t_app_web_version')
                    ->where('version', $version)
                    ->update($updateData);
            } else {
                // 记录不存在，执行插入操作
                $insertData = [
                    'version' => $version,
                    'version_sql' => $versionSql,
                    'version_wxapp' => $versionWxapp,
                    'version_wxapp_is_new' => 1,
                    'title' => '普通进化',
                    'desc' => '',
                    'desc_detail' => '',
                    'project_path' => '',
                    'oss_url' => $ossUrl,
                    'file_name' => $fileName,
                    'status' => 1,
                    'can_use' => 0,
                    'is_developing' => 1,
                    'rely_on_it' => 0,
                    'upload_oss_time' => 0,
                    'upload_total_time' => 0,
                    'create_time' => $currentTime,
                    'last_edit_time' => $currentTime,
                    'upload_time' => $currentTime,
                    'use_num' => 0,
                    'use_percent' => 0
                ];

                Db::connect($externalDbConfig)->table('t_app_web_version')->insert($insertData);
            }
        } catch (Exception $exception) {

        }
    }
    //mysqld.exe --install MySql --defaults-file="D:\develop\mysql-5.7\my.ini"


}