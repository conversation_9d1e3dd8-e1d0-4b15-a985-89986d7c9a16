<?php


namespace app\common\service\room;

use app\common\model\room\RoomBookCount as RoomBookCountModel;
use app\common\service\shop\ShopService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\user\MemberGradeService;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\CacheTag;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomSaleType;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;

class RoomBookCountService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
//判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
//返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getRoomBookCountList($shopId, $page, $limit)
    {
        $model = new RoomBookCountModel();
        $list = $model->getRoomBookCountList($shopId, $page, $limit);
        if ($page && $limit) {
            $count = $model->getRoomBookCountCount($shopId);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function getRoomBookCountByDate($shopId, $roomTypeId, $date, $roomSaleTypeId)
    {
        $model = new RoomBookCountModel();
        return $model->getRoomBookCountByDate($shopId, $roomTypeId, $date, $roomSaleTypeId);
    }

    public function getRoomBookCountById($id)
    {
        $model = new RoomBookCountModel();
        return $model->getRoomBookCountById($id);

    }

    public function delRoomBookCountById($id)
    {
        $model = new RoomBookCountModel();
        return $model->delRoomBookCountById($id);

    }

    public function editRoomBookCount($params)
    {
        $model = new RoomBookCountModel();
        if (isset($params['id'])) {
            $model->editRoomBookCount($params);
            $id = $params['id'];
        } else {
            $model->saveRoomBookCount($params);
            $id = $model->id;
        }
        return $id;
    }

    public function updateRoomBookCountBatch($params)
    {
        $model = new RoomBookCountModel();
        $model->updateRoomBookCountBatch($params);
    }

    public function getRoomBookCountByDateFromCache($shopId, $roomTypeId, $date, $roomSellTypeId)
    {
        $key = "$shopId-$roomTypeId-$date-$roomSellTypeId";
//        CacheManager::clearCacheValue($key);
        if (CacheManager::has($key)) {
            return CacheManager::getCacheValue($key);
        }
        $room = $this->getRoomBookCountByDate($shopId, $roomTypeId, $date, $roomSellTypeId);
        if (!empty($room)) {
            CacheManager::setCacheValue($key, $room, CacheTag::ROOM_BILL_STATUS, null, 60);
        }
        return $room;
    }


    public function updateToCloseRoom($shopId, $roomTypeId, $date, $closeRoom, $roomSellTypeId)
    {
        $room = $this->getRoomBookCountByDateFromCache($shopId, $roomTypeId, $date, $roomSellTypeId);
        if (empty($room)) {
            $this->editRoomBookCount(['room_type_id' => $roomTypeId, 'date' => $date, 'close_room' => $closeRoom, 'room_sell_type_id' => $roomSellTypeId, 'shop_id' => $shopId]);
        } else {
            $this->editRoomBookCount(['id' => $room->id, 'close_room' => $closeRoom]);
        }
    }


    /**
     * 查询小程序房态
     * @param $shopIdZong
     * @param $shopId
     * @param $roomSellTypeId
     * @param $dateStart
     * @param $dateEnd
     * @return array
     */
    public function adminGetRoomCount($shopIdZong, $shopId, $roomSellTypeId, $dateStart, $dateEnd)
    {
        $tableHeard = array();
        $key = "$shopId-$dateStart-$dateEnd-room_count_head";
        $days = TimeUtil::getInstance()->getTimeDifference($dateStart, $dateEnd, 1) + 1;
        if (CacheManager::has($key)) {
            $tableHeard = CacheManager::getCacheValue($key);
        } else {
            $dates = TimeUtil::getInstance()->getAllTimesStart($dateStart, $dateEnd);
            $weeds = TimeUtil::getInstance()->getAllWeeks($dateStart, $dateEnd);
            for ($i = 0; $i < $days; $i++) {
                array_push($tableHeard, ['date' => $dates[$i], 'week' => $weeds[$i]]);
            }
//            CacheManager::clearCacheValue($key);
            CacheManager::setCacheValue($key, $tableHeard, null, null, 6 * 3600);
        }
        $data = [];
        $roomTypes = RoomTypeService::getInstance()->getRoomTypeListFromCache($shopId, 1, null, null, null);
        $grades = MemberGradeService::getInstance()->getMemberGradeListFromCache($shopIdZong, 1, 1);
        $roomSaleTypes = RoomSaleTypeService::getInstance()->getRoomSaleTypeIdFromCache($roomSellTypeId);
        foreach ($roomTypes as $roomTypeIndex => $roomType) {
            $roomTypeId = $roomType->id;
            $roomTypeName = $roomType->name;
            $defaultRoomCount = $roomType->room_count;
            $tempData = ['room_type_id' => $roomTypeId, 'room_type_name' => $roomTypeName, 'id_combine' => $roomTypeId, 'room_sell_type_id' => $roomSellTypeId];
            foreach ($tableHeard as $itemHeard) {
                $itemDate = $itemHeard['date'];
                $itemData = $this->getRoomBookCountByDateFromCache($shopId, $roomTypeId, $itemDate, $roomSellTypeId);
                if (empty($itemData)) {
                    $closeRoom = 0;
                } else {
                    $closeRoom = $itemData->close_room;
                }
                $tempData[$itemDate] = ['close_room' => $closeRoom, 'status_type' => 'room_type', 'date' => $itemDate];
            }
//			array_push($data, $tempData);
            $childrenData = [];
            foreach ($roomSaleTypes as $saletypeIndex => $roomSaleType) {
                $itemRoomSaleTypeId = $roomSaleType->id;
                $itemRoomSaleTypeName = $roomSaleType->name;
                foreach ($grades as $gradeIndex => $grade) {
                    $itemGradeId = $grade->id;
                    $itemGradeName = $grade->grade_name;
                    $id_combine = "$roomTypeId-$itemRoomSaleTypeId-$itemGradeId";
                    if ($gradeIndex > 0) {
                        $itemRoomSaleTypeName = "";
                    }
                    $tempChildData = [
                        "id_combine" => $id_combine,
                        "room_sale_type_name" => $itemRoomSaleTypeName,
                        "grade_name" => $itemGradeName];
                    foreach ($tableHeard as $itemHeard) {
                        $itemDate = $itemHeard['date'];
                        $detailData = RoomBookCountDetailService::getInstance()->getRoomBookCountDetail($shopId, $itemRoomSaleTypeId, $roomSellTypeId, $roomTypeId, $itemGradeId, $itemDate);
                        $id = null;
                        if (empty($detailData)) {
                            $isFull = 0;
                            $saleCount = $defaultRoomCount;
                            $saleModel = 1;
                        } else {
                            $isFull = $detailData->is_full;
                            $saleCount = $detailData->sale_count;
                            $saleModel = $detailData->sale_model;
                            $id = $detailData->id;
                        }
                        $tempChildData[$itemDate] = ['is_full' => $isFull, 'sale_model' => $saleModel, 'status_type' => 'member_grade', 'room_type_name' => $roomTypeName,
                            'sale_count' => $saleCount, 'room_type_id' => $roomTypeId, 'room_sell_type_id' => $roomSellTypeId,
                            'room_sale_type_id' => $itemRoomSaleTypeId, 'grade_id' => $itemGradeId, 'id' => $id, 'date' => $itemDate,
                            'room_sale_type_name' => $roomSaleType->name, 'grade_name' => $grade->grade_name];
                    }
                    $childrenData[] = $tempChildData;
                }
            }
            $tempData['children'] = $childrenData;
            $data[] = $tempData;
        }
        return $data;
    }

    public function filterRoomBookCountDetailData($data, $itemRoomSaleTypeId, $roomSellTypeId, $roomTypeId, $itemGradeId, $itemDate)
    {
        $returnData = null;
        foreach ($data as $detailData) {
            if ($detailData->room_sale_type_id == $itemRoomSaleTypeId &&
                $detailData->room_sell_type_id == $roomSellTypeId &&
                $detailData->room_type_id == $roomTypeId &&
                $detailData->grade_id == $itemGradeId &&
                $detailData->date == $itemDate) {
                $returnData = $detailData;
                break;
            }
        }
        return $returnData;
    }


    /**
     * 小程序获取房量
     * @param $shopId
     * @param $roomSellTypeId
     * @param $roomSaleTypeId
     * @param $roomTypeId
     * @param $gradeId
     * @param $startTime
     * @param $endTime
     * @return int|mixed
     */
    public function wxGetRoomCount($shopId, $roomSellTypeId, $roomSaleTypeId, $roomTypeId, $gradeId, $startTime, $endTime)
    {
        $startDate = date('Y-m-d', $startTime);
        $endDate = date('Y-m-d', $endTime);
        if (StringUtil::checkEmpty($endTime)) {
            $endDate = $startDate;
        }
        $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
        $settingEnterHour = $enterTimeSetting->property['value'];
        $settingEnterHourTime = strtotime("$startDate $settingEnterHour");
        if ($startTime < $settingEnterHourTime) {
            $startDate = TimeUtil::getInstance()->getDate($startDate, -1);
        }
        $days = TimeUtil::getInstance()->getTimeDifference($startDate, $endDate, 1);
        if ($days == 0) {
            $days = 1;
        }
        $thirdRoomCountSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::THIRD_ROOM_COUNT);
        $hourRoomCount = 0;
        if ($thirdRoomCountSetting && $thirdRoomCountSetting->property['status'] == 1) {
            $countValue = $thirdRoomCountSetting->property['value'];
            $minRoomCount = RoomBillUtil::getInstance()->getThirdRoomCount($shopId, $startTime, $endTime, $roomTypeId);
            $closeRoom = 0;
            if ($minRoomCount < $countValue) {
                $minRoomCount = 0;
                $closeRoom = 1;
            }
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopPid = $shop->shop_pid;
            $shopIdZong = $shopPid == 0 ? $shopId : $shopPid;
            $this->updateRoomCount($shopIdZong, $shopId, [$startDate], $roomTypeId, $closeRoom, $roomSellTypeId);
            RoomBookCountDetailService::getInstance()->updateRoomCountDetailByDate($shopId, $roomTypeId, $startDate, $roomSellTypeId, $minRoomCount, $closeRoom);
        } else {
            $defaultRoomCount = RoomStatusRecordService::getInstance()->getMultiDayCount($shopId, $roomTypeId, $startTime, $days, false);
            $minRoomCount = $defaultRoomCount;
            for ($i = 0; $i < $days; $i++) {
//                DebugUtil::log("开始时间：$startDate,结束时间：$endDate");
//                DebugUtil::log(date("Y-m-d H:i:s", $startTime));
//                DebugUtil::log(date("Y-m-d H:i:s", $endTime));
                $countData = $this->getRoomBookCountByDateFromCache($shopId, $roomTypeId, $startDate, $roomSellTypeId);
                if ($countData && $countData->close_room == 1) {
                    //整个房型关房
                    return 0;
                }

                $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($roomSaleTypeId);
                $roomSaleTypeSign = $roomSaleType->sign;
                if ($roomSaleTypeSign == RoomSaleType::STANDARD || $roomSaleTypeSign == RoomSaleType::LONG_STANDARD) {
                    $hourRoomCountSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::HOUR_ROOM_COUNT);
                    if ($hourRoomCountSetting && $hourRoomCountSetting->property['status'] == 1) {
                        $saleTypeIds = [];
                        $hourSaleTypeList = RoomSaleTypeService::getInstance()->getRoomSaleTypeListBySign($shopId,RoomSaleType::HOUR);
                        foreach ($hourSaleTypeList as $hourSaleType){
                            $saleTypeIds = $hourSaleType->id;
                        }
                        $bookCount = RoomBillService::getInstance()->getBillCount($shopId, $roomTypeId, [RoomBillStatus::TO_BE_STAY,
                            RoomBillStatus::TO_BE_CANCELED], $startTime, $endTime, $saleTypeIds);
                        $stayingCount = RoomBillService::getInstance()->getBillCount($shopId, $roomTypeId, [RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT],
                            $startTime, $endTime, $saleTypeIds);
//                        DebugUtil::log("$bookCount - $stayingCount");
                        $hourRoomCount = $bookCount + $stayingCount;
                    }
                }
                $roomCountDetail = RoomBookCountDetailService::getInstance()->getRoomBookCountDetail($shopId, $roomSaleTypeId, $roomSellTypeId, $roomTypeId, $gradeId, $startDate);
                if (!empty($roomCountDetail)) {
                    //1限量 仅售卖保留房间数，保留房售完停止售卖，2不限量 保留房售完后，按非立即确认房量畅卖
                    $saleModel = $roomCountDetail->sale_model;
                    $isFull = $roomCountDetail->is_full;
                    if ($isFull) {
                        return 0;
                    }
                    $sale_count = $roomCountDetail->sale_count;
                    if ($saleModel == 2) {
                        $sale_count = $defaultRoomCount;
                    }
                } else {
                    $sale_count = $defaultRoomCount;
                }
                if ($sale_count < $minRoomCount) {
                    $minRoomCount = $sale_count;
                }
                $startDate = TimeUtil::getInstance()->getDate($startDate, 1);
            }
        }
        return $minRoomCount + $hourRoomCount;
    }


    /**
     * 修改小程序房型关房
     * @param $shopIdZong
     * @param $shopId
     * @param $dates
     * @param $roomTypeId
     * @param $closeRoom
     * @param $roomSellTypeId
     */
    public function updateRoomCount($shopIdZong, $shopId, $dates, $roomTypeId, $closeRoom, $roomSellTypeId)
    {
        foreach ($dates as $date) {
            $itemData = $this->getRoomBookCountByDateFromCache($shopId, $roomTypeId, $date, $roomSellTypeId);
            if (empty($itemData)) {
                $this->editRoomBookCount(['shop_id' => $shopId, 'room_type_id' => $roomTypeId, 'date' => $date, 'close_room' => $closeRoom, 'room_sell_type_id' => $roomSellTypeId]);
            } else {
                $this->editRoomBookCount(['id' => $itemData->id, 'close_room' => $closeRoom]);
            }
            RoomBookCountDetailService::getInstance()->updateByRoomTypeId($shopIdZong, $shopId, $roomSellTypeId, $roomTypeId, $date, $closeRoom);
        }
        CacheManager::clearAllCacheValue(CacheTag::ROOM_PRICE, $shopId);

    }

}