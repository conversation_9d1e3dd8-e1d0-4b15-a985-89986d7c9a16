<?php


namespace app\common\utils\dataDictionary;

/**
 * hook类型
 * Class RoomBillHook
 * @package app\common\utils\dataDictionary
 */
class RoomBillHook
{
	/**
	 * 添加订房订单
	 */
	const ADD_ROOM_ORDER = 'ADD_ROOM_ORDER';

	/**
	 * 预定转入住
	 */
	const BOOK_TO_STAY = 'BOOK_TO_STAY';
	/**
	 * 预定房间
	 */
	const BOOK_ROOM = 'BOOK_ROOM';

	/**
	 * 排房
	 */
	const NO_SHOW = 'NO_SHOW';

	/**
	 * 排房
	 */
	const ARRANGED_ROOM = 'ARRANGED_ROOM';
	/**
	 * 确认订单
	 */
	const CONFIRM_ROOM_BILL = 'CONFIRM_ROOM_BILL';

	/**
	 * 取消订单
	 */
	const CANCEL_ROOM_BILL = 'CANCEL_ROOM_BILL';

	/**
	 * 退房
	 */
	const CHECK_OUT = 'CHECK_OUT';

	/**
	 * 用户认证
	 */
	const AUTHENTICATION = 'AUTHENTICATION';

	/**
	 * 续房
	 */
	const EXTEND_ROOM = 'EXTEND_ROOM';

	/**
	 * 修改信息回调
	 */
	const EDIT_USER_INFO = 'EDIT_USER_INFO';

	/**
	 * 换房
	 */
	const CHANGE_ROOM = 'CHANGE_ROOM';

	/**
	 * 换房
	 */
	const NO_CLEAR_CHECK_OUT = 'NO_CLEAR_CHECK_OUT';
	/**
	 * 同意取消
	 */
	const AGREE_CANCEL_ROOM_BILL = 'AGREE_CANCEL_ROOM_BILL';
	/**
	 * 拒绝退房
	 */
	const REFUSE_CHECK_OUT = 'REFUSE_CHECK_OUT';

	/**
	 * 拒绝取消
	 */
	const REFUSE_CANCEL = 'REFUSE_CANCEL';

	/**
	 * 微信订房
	 */
	const WX_BOOK_ROOM = 'WX_BOOK_ROOM';

	/**
	 * 微信申请退房
	 */
	const WX_CHECK_OUT = 'WX_CHECK_OUT';

	/**
	 * 微信申请取消
	 */
	const WX_CANCEL = 'WX_CANCEL';

	/**
	 * 编辑联系人
	 */
	const UPDATE_LINK_INFO = 'UPDATE_LINK_INFO';

	/**
	 * 团队入住
	 */
	const UPDATE_ROOM_USER = 'UPDATE_ROOM_USER';
    /**
     * 团队入住
     */
    const UPDATE_ROOM_USER_BOOKING = 'UPDATE_ROOM_USER_BOOKING';

    /**
     * 团队退房
     */
    const UPDATE_ROOM_USER_TO_CHECK_OUT = 'UPDATE_ROOM_USER_TO_CHECK_OUT';

    /**
     * 团队添加通知润
     */
    const UPDATE_TEAM_ROOM_USER = 'UPDATE_TEAM_ROOM_USER';

	/**
	 * 编辑入住人
	 */
	const ADD_ROOM_USER = 'ADD_ROOM_USER';


	/**
	 * 编辑入住人
	 */
	const DEL_ROOM_USER = 'DEL_ROOM_USER';

	/**
	 * 编辑入住人
	 */
	const  LEAVE_ROOM_USER = 'LEAVE_ROOM_USER';

	/**
	 * 修改房价
	 */
	const UPDATE_ROOM_PRICE = 'UPDATE_ROOM_PRICE';
	/**
	 * 编辑财务
	 */
	const UPDATE_ROOM_DETAIL = 'UPDATE_ROOM_DETAIL';

	/**
	 * 微信自助入住
	 */
	const WX_STAY = "WX_STAY";

    /**
     * 微信自助入住
     */
    const WX_TEAM_STAY = "WX_TEAM_STAY";

	/**
	 * 订单车牌
	 */
	const CAR_NUMBER = "CAR_NUMBER";

	/**
	 * 续房
	 */
	const WX_EXTEND_ROOM = 'WX_EXTEND_ROOM';

    /**
     * 机器推送hook
     */
    const MACHINE_USER_INFO_HOOK = 'MACHINE_USER_INFO_HOOK';

    /**
     * 机器推送hook
     */
    const TEAM_UPDATE_HOOK = 'TEAM_UPDATE_HOOK';

}