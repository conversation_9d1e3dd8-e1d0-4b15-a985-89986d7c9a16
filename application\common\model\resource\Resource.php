<?php


namespace app\common\model\resource;


use app\common\utils\StringUtil;
use think\Model;

class Resource extends Model
{
	//资源表
	//id
	//shop_id 酒店id
	//name 资源名称
	//type 类型 目前 image 后面可扩充video等
	//url url
	//create_time
	//level 资源等级
	//type_id 类型id
	//storage_type 1本地，2阿里oss，3腾讯oss，4七牛云
	//update_time
	//remark 备注
	private $fields = ['id', 'shop_id', 'name', 'type', 'url', 'create_time', 'level', 'type_id', 'storage_type', 'update_time', 'remark', 'status', 'is_public'];
	private $sqlStr = 'a.id,
		a.shop_id,
		a.name,
		a.type,
		a.url,
		a.create_time,
		a.level,
		a.type_id,
		a.storage_type,
		a.update_time,
		a.remark,
		a.status,
		a.is_public';
	protected $autoWriteTimestamp = true;

	protected $type = [
		'id' => 'integer',
		'shop_id' => 'integer',
		'create_time' => 'integer',
		'level' => 'integer',
		'type_id' => 'integer',
		'storage_type' => 'integer',
		'update_time' => 'integer',
		'status' => 'integer',
		'is_public' => 'integer',
	];


	public function getResourceList($shopIdZong, $shopId, $typeId, $type, $status, $page, $limit) {
		$this->alias('a')->field($this->sqlStr . ',b.name as type_name')
			->join('resource_type b', 'a.type_id = b.id')->where('b.show', 1);
		if (!StringUtil::checkEmpty($typeId)) {
			$this->where('a.type_id', $typeId);
		} else {
			if ($shopId == 0) {
				$this->where('a.shop_id', $shopId);
			} else {
				if ($shopId == 0) {
					$this->where('a.shop_id', $shopId);
				} else {
					$this->where('a.shop_id', $shopId)
						->whereor(function ($query) use ($shopId) {
							$query->where('a.shop_id', 0)->where('a.is_public', 1);
						});
				}
			}
		}
		if (!StringUtil::checkEmpty($type)) {
			$this->where('a.type', $type);
		}
		if (!StringUtil::checkEmpty($status)) {
			$this->where('a.status', $status);
		}
		if ($page && $limit) {
			$this->page($page, $limit);
		}
		return $this->order('a.id', 'desc')->select();
	}


	public function getResourceCount($shopIdZong, $shopId, $typeId, $type, $status) {
		$this->alias('a')->join('resource_type b', 'a.type_id = b.id')
			->where('b.show', 1);
		if (!StringUtil::checkEmpty($typeId)) {
			$this->where('a.type_id', $typeId);
		} else {
			if ($shopId == 0) {
				$this->where('a.shop_id', $shopId);
			} else {
				$this->where('a.shop_id', $shopId)
					->whereor(function ($query) use ($shopId) {
						$query->where('a.shop_id', 0)->where('a.is_public', 1);
					});
			}
		}
		if (!StringUtil::checkEmpty($type)) {
			$this->where('a.type', $type);
		}
		if (!StringUtil::checkEmpty($status)) {
			$this->where('a.status', $status);
		}
		return $this->count();
	}


	public function saveResource($params) {
		return $this->allowField($this->fields)
			->save($params);
	}


	public function editResource($params) {
		return $this->allowField($this->fields)
			->save($params, ['id' => $params['id']]);
	}

	public function getResourceById($id) {
		return $this->alias('a')->field($this->sqlStr)->where('a.id', $id)->find();
	}
    public function getResourceByName($name){
        return $this->alias('a')->field($this->sqlStr)->where('a.name', $name)->find();
    }

}