<?php


namespace app\wx\controller;


use app\common\returnConfig\Code;
use app\common\service\Catering\CateringBillService;
use app\common\service\catering\CateringFundService;
use app\common\service\Pay\PayRecordService;
use app\common\service\Pay\WechatPayService;
use app\common\service\pointMall\PointMallBillFundDetailService;
use app\common\service\recharge\RechargeFundService;
use app\common\service\room\BreakfastCouponRecordService;
use app\common\service\room\RoomBillDetailPreService;
use app\common\service\room\RoomBillDetailService;
use app\common\service\room\team\TeamFundService;
use app\common\service\room\team\TeamSelectRoomUtil;
use app\common\service\shop\ShopService;
use app\common\service\store\StoreBillService;
use app\common\service\sxf\SxfLedgerService;
use app\common\service\sysSetting\MiniProgramConfigService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\sysSetting\WxPayConfService;
use app\common\service\user\MemberGradeBillService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\CateringBillStatus;
use app\common\utils\dataDictionary\PointMallBillStatus;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\StoreBillStatus;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use app\common\utils\tencent\PayUtil;
use think\Controller;
use think\Db;
use think\Exception;
use think\Request;
use WxPayException;

//require_once  EXTEND_PATH.'wechatpay_sdk/lib/WxPay.Notify.php';
require_once EXTEND_PATH . 'wechatpay_sdk/lib/WxPay.Api.php';
require_once EXTEND_PATH . 'wechatpay_sdk/WxPay.JsApiPay.php';
require_once EXTEND_PATH . 'wechatpay_sdk/WxPay.Config.php';

class WxPayNotify extends Controller
{
    private static $outTradeNoKey = "setOutTradeNoCache_";

    private $outTradeNo = "";

    /**
     * 微信小程序订房下单
     * @param Request $request
     * @throws WxPayException
     */
    public function bookRoomNotify(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $shopId = $arr['attach'];
                $mch_id = $arr['mch_id'];
                $transaction_id = $arr['transaction_id'];
                $outTradeNo = $arr['out_trade_no'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        $billId = $payRecord->bill_id;
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        RoomBillDetailPreService::getInstance()->checkPreBills($billId, $outTradeNo, $transaction_id);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            Db::rollback();
            if ($exception instanceof MyException) {
                DebugUtil::log($exception->msg, 'error');
            } else {
                DebugUtil::log($exception->getMessage(), 'error');
            }
            DebugUtil::log($exception->getTraceAsString(), 'error');
//            $this->refundBill($shopId,$outTradeNo,$totalFee);
            $this->returnData(2);
        }
    }

    public function bookRoomNotifyFix(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        DebugUtil::log(json_encode($arr), 'debug');
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $mch_id = $arr['mch_id'];
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        MemberGradeBillService::getInstance()->buyMemberGradeCheckPreBills($billId, $outTradeNo, $transaction_id);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    /**
     * 团队入住选房回调
     * @param Request $request
     * @throws WxPayException
     */
    public function teamPayAmountNotify(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $attach = $arr['attach'];
        $attachList = explode("_", $attach);
        $shopId = $attachList[0];
        $fundId = $attachList[1];
        $userCount = $attachList[2];
        $totalFee = $arr['total_fee'] / 100;
        $mch_id = $arr['mch_id'];
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    $teamId = $payRecord->team_id;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }

                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        TeamFundService::getInstance()->notifyUpdatePayStatus($fundId, 1, $userCount);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    /**
     * 微信小程序购买会员
     * @param Request $request
     * @throws WxPayException
     */
    public function buyMemberGrade(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        DebugUtil::log(json_encode($arr), 'debug');
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $mch_id = $arr['mch_id'];
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        MemberGradeBillService::getInstance()->buyMemberGradeCheckPreBills($billId, $outTradeNo, $transaction_id);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    /**
     * 微信小程序购买会员
     * @param Request $request
     * @throws WxPayException
     */
    public function buyMemberGradeFix(Request $request)
    {
        $arr = $request->param();
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        DebugUtil::log(json_encode($arr), 'debug');
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $mch_id = $arr['mch_id'];
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        MemberGradeBillService::getInstance()->buyMemberGradeCheckPreBills($billId, $outTradeNo, $transaction_id);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    /**
     * 申请退房
     * @param Request $request
     * @throws WxPayException
     */
    public function applyCheckOut(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $totalFee = $arr['total_fee'] / 100;
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $billId = $payRecord->bill_id;
                        $detail_id = $payRecord->detail_id;
                        RoomBillDetailPreService::getInstance()->applyCheckOut($billId, $outTradeNo, $transaction_id);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }


    /**
     * 会员充值回调
     * @param Request $request
     * @throws WxPayException
     */
    public function memberRecharge(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $attach = $arr['attach'];
        $attachList = explode("_", $attach);
        $shopId = $attachList[0];
        $settingId = null;
        if (count($attachList) == 1) {
            $settingId = $attachList[1];
        }
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
            $this->outTradeNo = $outTradeNo;
            $mch_id = $arr['mch_id'];
            if ($this->checkRepetition($outTradeNo) == 1) {
                Db::commit();
                $this->returnData(1);
            } else {
                $checkFlag = $this->checkSign($shopId, $xml);
                if (!$checkFlag) {
                    Db::rollback();
                    throw new MyException(Code::FAIL, "微信支付回调验证失败");
                }
                $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                $payStatus = $payRecord->pay_status;
                if ($payStatus == 0) {
                    $payAmount = $payRecord->amount;
                    if ($totalFee != $payAmount) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                    }
                    PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                    $billId = $payRecord->bill_id;
                    RechargeFundService::getInstance()->updateMemberRechargeBillStatus($billId, $settingId);
                }
                $this->returnData(1);
                Db::commit();
            }
        } else {
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }

    }


    /**
     * 超市支付回调
     * @param Request $request
     * @throws WxPayException
     */
    public function storeBillNotify(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $this->outTradeNo = $outTradeNo;
                $mch_id = $arr['mch_id'];
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        StoreBillService::getInstance()->notifyUpdateBillStatus($billId, $transaction_id, StoreBillStatus::TO_BE_CONFIRMED);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }


    /**
     * 点餐支付回调
     * @param Request $request
     * @throws WxPayException
     */
    public function cateringBillNotify(Request $request)
    {
//		DebugUtil::log("cateringBillNotify");
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        DebugUtil::log("step 3");
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        CateringFundService::getInstance()->notifyUpdateBillStatus($billId, $transaction_id, CateringBillStatus::TO_BE_CONFIRMED, $outTradeNo);
                    }
                    $this->returnData(1);
                }
                Db::commit();
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    public function pointMallBillNotify(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $shopId = $arr['attach'];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        PointMallBillFundDetailService::getInstance()->notifyUpdateBillStatus($outTradeNo, $transaction_id, 1);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }


    public function wxSelfPayCheckIn(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $attach = $arr['attach'];
        $shopAndRoomId = explode('_', $attach);
        $shopId = $shopAndRoomId[0];
        $roomId = $shopAndRoomId[1];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {
                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $record = PayRecordService::getInstance()->findByOutTradeNo($outTradeNo);
                        $commonCode = $record->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfCheckInPayNotify($billId, $outTradeNo, $transaction_id, $roomId, $commonCode);

                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    public function wxSelfPayCheckOut(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $attach = $arr['attach'];
        $shopAndRoomId = explode('_', $attach);
        $shopId = $shopAndRoomId[0];
        $days = $shopAndRoomId[0];
        $type = $shopAndRoomId[0];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $record = PayRecordService::getInstance()->findByOutTradeNo($outTradeNo);
                        $commonCode = $record->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfExtendROomPayNotify($billId, $outTradeNo, $transaction_id, $days, $type, $commonCode);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }


    /**
     * 自助续房
     * @param Request $request
     * @throws WxPayException
     */
    public function wxSelfPayExtendRoom(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $attach = $arr['attach'];
        $shopAndRoomId = explode('_', $attach);
        $shopId = $shopAndRoomId[0];
        $days = $shopAndRoomId[1];
        $type = $shopAndRoomId[2];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    $payRecord = PayRecordService::getInstance()->getPayRecord($outTradeNo);
                    $billId = $payRecord->bill_id;
                    $payStatus = $payRecord->pay_status;
                    if ($payStatus == 0) {
                        $payAmount = $payRecord->amount;
                        if ($totalFee != $payAmount) {
                            Db::rollback();
                            throw new MyException(Code::FAIL, "微信支付订单金额不一致");
                        }
                        PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                        $commonCode = $payRecord->common_code;
                        RoomBillDetailPreService::getInstance()->wxSelfExtendROomPayNotify($billId, $outTradeNo, $transaction_id, $days, $type, $commonCode);
                    }
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }

    /**
     * 自助续房
     * @param Request $request
     * @throws WxPayException
     */
    public function breakfastNotify(Request $request)
    {
        $xml = file_get_contents("php://input");
        $dataBase = new \WxPayDataBase();
        $arr = $dataBase->FromXml($xml);
        DebugUtil::log(json_encode($arr), 'debug');
        $attach = $arr['attach'];
        $shopAndRoomId = explode('_', $attach);
        $shopId = $shopAndRoomId[0];
        $days = $shopAndRoomId[1];
        $type = $shopAndRoomId[2];
        $transaction_id = $arr['transaction_id'];
        $outTradeNo = $arr['out_trade_no'];
        $totalFee = $arr['total_fee'] / 100;
        Db::startTrans();
        try {
            if ($arr['return_code'] == 'SUCCESS' && $arr['result_code'] == 'SUCCESS') {
                $mch_id = $arr['mch_id'];
                $this->outTradeNo = $outTradeNo;
                if ($this->checkRepetition($outTradeNo) == 1) {
                    Db::commit();
                    $this->returnData(1);
                } else {

                    $checkFlag = $this->checkSign($shopId, $xml);
                    if (!$checkFlag) {
                        Db::rollback();
                        throw new MyException(Code::FAIL, "微信支付回调验证失败");
                    }
                    PayRecordService::getInstance()->updatePayStatus($outTradeNo, $transaction_id, 1,$mch_id);
                    BreakfastCouponRecordService::getInstance()->breakfastCouponNotify($outTradeNo, $transaction_id);
                    $this->returnData(1);
                    Db::commit();
                }
            } else {
                Db::rollback();
                $this->returnData(2);
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($arr);
            Db::rollback();
            $this->refundBill($shopId, $outTradeNo, $totalFee);
            $this->returnData(2);
        }
    }


    /**
     * 检查接收参数
     * @param $shopId
     * @param $xml
     * @return bool
     * @throws MyException
     */
    private function checkSign($shopId, $xml)
    {
        $shop = ShopService::getInstance()->getShopById($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        $setting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId,ShopSysSetting::BRANCH_SHOP_WX_ACCOUNT);
        $status = $setting->property['status'];
        if ($status == 1){
            $wxConfig = WxPayConfService::getInstance()->getShopWxPayConf($shopId, 2);
        } else {
            $wxConfig = WxPayConfService::getInstance()->getShopWxPayConf($shopIdZong, 1);
        }
        $miniProgressConfig = MiniProgramConfigService::getInstance()->getMiniProgramConfigByShopId($shopIdZong, 1);
        $config = new \WxPayConfig(
            $miniProgressConfig->app_id,
            $wxConfig->key,
            $wxConfig->mch_id,
            $miniProgressConfig->app_secret,
            '',
            $shopIdZong,
            $wxConfig->type
        );
        //如果返回成功则验证签名
        try {
            $result = \WxPayNotifyResults::Init($config, $xml);
            return true;
        } catch (WxPayException $e) {
            $msg = $e->errorMessage();
            DebugUtil::log($msg, 'error');
            return false;
        }
    }

    /**
     * 检查是否重复处理
     * 个别订单出现回调已处理，并且业务成功但是还是在第二次回调时处理了导致收款重复
     * 可能是因为定时任务导致堵塞，两个请求同时处理导致的
     * @param $outTradeNo
     * @return boolean
     */
    private function checkRepetition($outTradeNo)
    {
        $type = 2;
        if (CacheManager::has($outTradeNo)) {
            $type = CacheManager::getCacheValue($outTradeNo);
        }
        return $type;
    }

    /**
     * @param $type 1:success 2:fail
     * @param string $msg 返回信息
     * @return string
     */
    private function returnData($type)
    {
        if ($type == 1) {
            $res = 'SUCCESS';
            $msg = 'OK';
            DebugUtil::log($this->outTradeNo . " returnData");
            if (!empty($this->outTradeNo)) {
                DebugUtil::log($this->outTradeNo);
                if (!CacheManager::has($this->outTradeNo, null, null)) {
                    CacheManager::setCacheValue($this->outTradeNo, $type, null, null, 60);
                }
            }
        } else {
            $res = "FAIL";
            $msg = '失败';
        }
        DebugUtil::log($res, $type == 1 ? "debug" : 'error');
        echo "<xml><return_code><![CDATA[$res]]></return_code><return_msg><![CDATA[$msg]]></return_msg></xml>";
    }

    /**
     * 多次回调失败退款
     * @param $shopId
     * @param $outTradeNo
     * @param $money
     * @return void
     * @throws MyException
     */
    private function refundBill($shopId, $outTradeNo, $money)
    {
        DebugUtil::log("refund 1");
        $key = self::$outTradeNoKey . $outTradeNo;
        if (!CacheManager::has($key)) {
            DebugUtil::log("refund 2");
            CacheManager::setCacheValue($key, time(), null, null, 1800);
        } else {
            DebugUtil::log("refund 3");
            $firstTime = CacheManager::getCacheValue($key);
            if (time() - $firstTime > 30) {
                //订房失败自动退款
                $outRefundNo = StringUtil::getRandomStrs(16, true, true, true);
                DebugUtil::log("处理失败自动退款 $outTradeNo $outRefundNo", "debug");
                WechatPayService::getInstance()->refound($shopId, $outTradeNo, $money, $money, $outRefundNo);
            }
        }
    }
}