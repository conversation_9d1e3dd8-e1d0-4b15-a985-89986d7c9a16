<?php


namespace app\common\service\room\team;

use app\common\returnConfig\Code;
use app\common\service\common\NationService;
use app\common\service\coupon\CouponRecordService;
use app\common\service\hook\HookResultService;
use app\common\service\hook\RoomHookService;
use app\common\service\intermediary\IntermediaryRoomPriceServiceService;
use app\common\service\resource\ResourceService;
use app\common\service\room\apiStay\StandardRoomService;
use app\common\service\room\BreakfastCouponRecordService;
use app\common\service\room\RoomBillDetailService;
use app\common\service\room\RoomBillDetailTypeService;
use app\common\service\room\RoomBillLogService;
use app\common\service\room\RoomBillPriceDateService;
use app\common\service\room\RoomBillService;
use app\common\service\room\RoomBillUserService;
use app\common\service\room\RoomBillUtil;
use app\common\service\room\RoomFundUtil;
use app\common\service\room\RoomPriceServiceService;
use app\common\service\room\RoomService;
use app\common\service\room\RoomStatusRecordService;
use app\common\service\room\RoomTypeService;
use app\common\service\shop\ShopService;
use app\common\service\sysSetting\ShopSysSettingService;
use app\common\service\thridSystem\ThirdSystemApiService;
use app\common\service\thridSystem\ThirdSystemService;
use app\common\service\thridSystem\wlys\RoomBill;
use app\common\service\user\UserCommonService;
use app\common\service\user\UserOcrService;
use app\common\service\user\UserUtil;
use app\common\service\user\UserWxService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\RoomBillDetailType;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomLogLevel;
use app\common\utils\dataDictionary\RoomLogType;
use app\common\utils\dataDictionary\RoomRecordStatus;
use app\common\utils\dataDictionary\ShopSysSetting;
use app\common\utils\dataDictionary\ThirdSystem;
use app\common\utils\dataDictionary\ThirdSystemApi;
use app\common\utils\DebugUtil;
use app\common\utils\ExcelUtil;
use app\common\utils\QR\WxQrUtil;
use app\common\utils\request\RequestUtil;
use app\common\utils\StringUtil;
use app\common\utils\TimeUtil;

/**
 * Name:
 * Description:
 * Author: ShanGui
 * Date 2023-09-14
 */
class TeamSelectRoomUtil
{
    private static $object = null;
    private $shop_id_zong;

    public function __construct()
    {
    }

    public static function getInstance()
    {
        //判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
        //返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    private static $SELECT_USER_KEY = "team_select_user_key_";
    private static $SELECT_USER_GENDER_KEY = "select_user_gender_key";
    private static $TEAM_ROOM_LIST_KEY = "team_room_list_key_";


    public function setRoomUserList($shopId, $teamId, $commonCode, $selectUserCount)
    {
        $roomListKey = self::$TEAM_ROOM_LIST_KEY . $teamId;
        if (CacheManager::has($roomListKey)) {
            $roomList = CacheManager::getCacheValue($roomListKey);
            if (!empty($roomList)) {
                //commonCode不在列表中则添加
                $key = array_search($commonCode, $roomList);
                if ($key === false) {
                    $roomList[$commonCode] = ['common_code=' => $commonCode, 'team_id' => $teamId, 'shop_id' => $shopId, 'select_user_count' => $selectUserCount];
                }
            }
        } else {
            $roomList[$commonCode] = ['common_code=' => $commonCode, 'team_id' => $teamId, 'shop_id' => $shopId, 'select_user_count' => $selectUserCount];
        }
        CacheManager::setCacheValue($roomListKey, $roomList, null, null, 30);
    }

    public function delRoomUserList($teamId, $commonCode)
    {
        $roomListKey = self::$TEAM_ROOM_LIST_KEY . $teamId;
        if (CacheManager::has($roomListKey)) {
            $roomList = CacheManager::getCacheValue($roomListKey);
            if (!empty($roomList)) {
                $key = array_search($commonCode, $roomList);
                if ($key !== false) {
                    unset($roomList[$key]);
                }
            }
            CacheManager::setCacheValue($roomListKey, $roomList, null, null, 30);
        }
    }


    public function autoSelectBillNew($shopId, $teamId, $commonCode, $selectUserCount, $single)
    {
        $userOrc = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        if (empty($userOrc)) {
            return 0;
        }
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $dirty_check_in = $team['dirty_check_in'];
        $selectBillId = 0;
        $refuseCommonCodes = $this->getRefuseCommonCodes($teamId, $commonCode);
        $userGender = $userOrc->gender == "男" ? 1 : 2;
        $roomBillList = RoomBillService::getInstance()->getTeamBill($shopId, [RoomBillStatus::TO_BE_STAY, RoomBillStatus::STAYING, RoomBillStatus::TO_CHECK_OUT], $teamId);
        $preBillId = 0;//预选订单，优先把房间排满，把空房流到后面。以防有两人同住
        $genderCache = null;
        $roomCount = count($roomBillList);
        $startIndex = rand(0, ceil($roomCount / 3));
        if ($startIndex == $roomCount) {
            $startIndex = 0;
        }
        for ($i = $startIndex; $i < $roomCount; $i++) {
            $item = $roomBillList[$i];
            $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
            $settingEnterHour = $enterTimeSetting->property['value'];
            $enterDate = date("Y-m-d", $item->enter_time_plan);
            $settingEnterHourTime = strtotime("$enterDate $settingEnterHour");
            $tempBillId = $item->id;
            $roomId = $item->room_id;

            if (time() <= $settingEnterHourTime || time() > $item->leave_time_plan) {
                //只允许入住当天的
                DebugUtil::log("not in same day $tempBillId $settingEnterHourTime");
                continue;
            }
            if (!$roomId) {
                DebugUtil::log("no roomId $tempBillId");
                continue;
            }
            if (!$this->roomUsable($shopId, $roomId)) {
                continue;
            }
            //当前房间即将分配的人数
            $userCount = $this->getCurrentRoomUserCountNew($tempBillId);
            //同一个房间的人数，未分配和已分配
            $sameRoomUserCount = $this->getSameRoomUser($teamId, $commonCode);
            $singleUser = $this->isSingle($tempBillId);
            if ($singleUser) {
                DebugUtil::log("single $tempBillId");
                continue;
            }

            $rooTypeId = $item->room_type_id;
            $bedCount = $this->getBedCount($tempBillId, $rooTypeId);
            $maxUserCount = $this->getMaxUserCount($rooTypeId);
            DebugUtil::log("autoSelectBill $tempBillId ：$selectUserCount; $userCount ; $bedCount;$sameRoomUserCount ;$maxUserCount");
            $genderCacheKey = self::$SELECT_USER_GENDER_KEY . $tempBillId;
            if (CacheManager::has($genderCacheKey)) {
                //上一个分配房间的人的性别
                $genderCache = CacheManager::getCacheValue($genderCacheKey);
            }
            //单间已经有人入住无法再分配房间
            DebugUtil::log($bedCount == 1 && $userCount > 0);
            DebugUtil::log($single === 1 && $bedCount >= 1);
            DebugUtil::log($single === 0 && $bedCount == 1);
            DebugUtil::log($single);
            if (($bedCount == 1 && $userCount > 0) || ($single === 1 && $bedCount > 1) || ($single === 0 && $bedCount == 1)) {
                DebugUtil::log("full1:$tempBillId");
                continue;
            }
            //已选择这个房间的人数+ 即将入住的人数 $selectUserCount 不包括自己
            //$bedCount=1单间，单间已经有人住则无法再分配
            //即将入住的人数 大于最大人数
            DebugUtil::log("$userCount + $selectUserCount >= $maxUserCount");
            DebugUtil::log("$userCount > $bedCount");
            DebugUtil::log("$userCount + $sameRoomUserCount > $maxUserCount");
            if ($userCount + $selectUserCount >= $maxUserCount ||
                $userCount > $bedCount ||
                $userCount + $sameRoomUserCount > $maxUserCount) {
                DebugUtil::log("full2:$tempBillId");
                continue;
            }
            //判断是否是包间
            if ($userCount == 0) {
//                //订单人数为0，无人选择
                if ($dirty_check_in == 0) {
                    $cleanStatus = $this->getBillRoomCleanStatus($tempBillId);
                    if ($cleanStatus != 1) {
                        DebugUtil::log("cleanStatus1: $cleanStatus $tempBillId");
                        continue;
                    }
                }
                $preBillId = $tempBillId;//作为候选订单
            } else {
                $sameGender = true;
                $teamRoomUserList = TeamRoomUserService::getInstance()->getRoomUserByBillId($tempBillId);
                foreach ($teamRoomUserList as $tempUser) {
                    if (!empty($refuseCommonCodes) && in_array($commonCode, $refuseCommonCodes)) {
                        //拒绝过
                        DebugUtil::log("full5:$tempBillId");
                        continue;
                    }
                    $itemGender = $tempUser->gender;
                    if ($itemGender != $userGender) {
                        DebugUtil::log("selectStep 2:$tempBillId");
                        $sameGender = false;
                    }
                }
                if ($genderCache && $genderCache != $userGender) {
                    DebugUtil::log("selectStep 3:$tempBillId");
                    $sameGender = false;
                }
                if ($dirty_check_in == 0) {
                    $cleanStatus = $this->getBillRoomCleanStatus($tempBillId);
                    if ($cleanStatus != 1) {
                        DebugUtil::log("cleanStatus2: $cleanStatus $tempBillId");
                        continue;
                    }
                }
                if ($sameGender) {
                    if ($preBillId != 0) {
                    }
                    $preBillId = $tempBillId;
                }
            }
        }
        DebugUtil::log("$selectBillId -- $preBillId");
        if ($selectBillId == 0 && $preBillId != 0) {
            $selectBillId = $preBillId;
        }
        if ($selectBillId != 0) {
            $genderCacheKey = self::$SELECT_USER_GENDER_KEY . $selectBillId;
            CacheManager::setCacheValue($genderCacheKey, $userGender, null, null, 30);
        }
        $this->setCurrentBillUserCommonCode($selectBillId, $commonCode);
        return $selectBillId;
    }

    private function setCurrentBillUserCommonCode($selectBillId, $commonCode)
    {
        $selectBillIdKey = self::$SELECT_USER_KEY . $selectBillId;
        if (CacheManager::has($selectBillIdKey)) {
            $selectUsers = CacheManager::getCacheValue($selectBillIdKey);
            //存在缓存
            if (!in_array($commonCode, $selectUsers)) {
                $selectUsers[] = $commonCode;
                CacheManager::setCacheValue($selectBillIdKey, $selectUsers, null, null, 15);
            }
        } else {
            //存在缓存 1第一个人，2缓存过期
            $selectUsers = [];
            $selectUsers[] = $commonCode;
            CacheManager::setCacheValue($selectBillIdKey, $selectUsers, null, null, 15);
        }
    }

    /**
     * 查询床铺数量
     * @param $tempBillId
     * @param $rooTypeId
     * @return array|bool|float|int|mixed|object|\stdClass|null
     */
    private function getBedCount($tempBillId, $rooTypeId)
    {
        $bedCountKey = "roomTypeBedCountKey__$tempBillId";//获取订单缓存的床铺数量
        if (CacheManager::has($bedCountKey)) {
            $bedCount = CacheManager::getCacheValue($bedCountKey);
            if ($bedCount) {
                return $bedCount;
            } else {
                CacheManager::clearCacheValue($bedCountKey);
            }
        }
        $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($rooTypeId);
        $bedCount = $tempRoomType->bed_count;
        $tempBedCountKey = "team_bed_count_$tempBillId";
        CacheManager::setCacheValue($tempBedCountKey, $bedCount, null, null, 600);
        return $bedCount;
    }

    /**
     * 查询最大入住人数
     * @param $rooTypeId
     * @return array|bool|float|int|mixed|object|\stdClass|null
     */
    private function getMaxUserCount($rooTypeId)
    {
        $maxUserCountKey = "maxUserCountKey_$rooTypeId";
        if (CacheManager::has($maxUserCountKey)) {
            $maxUserCount = CacheManager::getCacheValue($maxUserCountKey);
        } else {
            $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($rooTypeId);
            $maxUserCount = $tempRoomType->max_user_count;
            CacheManager::setCacheValue($maxUserCountKey, $maxUserCount, null, null, 600);
        }
        return $maxUserCount;
    }

    private function delCurrentBillUserCommonCode($selectBillId, $commonCode)
    {
        $selectBillIdKey = self::$SELECT_USER_KEY . $selectBillId;
        if (CacheManager::has($selectBillIdKey)) {
            $selectUsers = CacheManager::getCacheValue($selectBillIdKey);
            //存在缓存
            if (in_array($commonCode, $selectUsers)) {
                $key = array_search($commonCode, $selectUsers);
                unset($selectUsers[$key]);
            }
        }
    }

    /**
     * 查询拒绝过的用户
     * @param $teamId
     * @param $commonCode
     * @return array
     */
    public function getRefuseCommonCodes($teamId, $commonCode)
    {
        $refuseCommonCodes = [];
        //接受分享的二维码
        $teamQrTos = TeamRoomQrService::getInstance()->getTeamRoomQrByCommonCode($teamId, $commonCode);
        if (!empty($teamQrTos)) {
            foreach ($teamQrTos as $teamQrTo) {
                $status = $teamQrTo->status;
                $commonCodeFrom = $teamQrTo->common_code_from;
                if ($status == 3 || $status == 4) {
                    $refuseCommonCodes[] = $commonCodeFrom;
                }
            }
        }
        //邀请分享的二维码
        $teamQrFroms = TeamRoomQrService::getInstance()->getTeamRoomQrByCommonCodeFrom($teamId, $commonCode);
        if (!empty($teamQrFroms)) {
            foreach ($teamQrFroms as $teamQrFrom) {
                $status = $teamQrFrom->status;
                $itemCommonCode = $teamQrFrom->common_code;
                if ($status == 3 || $status == 4) {
                    $refuseCommonCodes[] = $itemCommonCode;
                }
            }
        }
        return $refuseCommonCodes;
    }

    private function getCurrentRoomUserCount($billId)
    {
        $teamRoomUserList = TeamRoomUserService::getInstance()->getRoomUserByBillId($billId);
        $currentCount = count($teamRoomUserList);
        foreach ($teamRoomUserList as $tempUser) {
            $itemUserShareCode = $tempUser->share_code;
            if (!empty($itemUserShareCode)) {
                $count = TeamRoomUserService::getInstance()->getTeamUserCountByShareCode($itemUserShareCode);
                if ($count > $currentCount) {
                    $currentCount = $count;
                }
            }
        }
        return $currentCount;
    }

    /**
     * 当前房间分配了多少人,包括选择了该房间未还未分配的
     * @param $billId
     * @return int
     */
    private function getCurrentRoomUserCountNew($billId)
    {
        $teamRoomUserList = TeamRoomUserService::getInstance()->getRoomUserByBillId($billId);
        $commonCodes = [];
        foreach ($teamRoomUserList as $tempUser) {
            $itemId = $tempUser->id;
            $itemUserShareCode = $tempUser->share_code;
            $itemCommonCode = $tempUser->common_code;
            $identification_number = $tempUser->identification_number;
            $accnt = $tempUser->accnt;
            $single = $tempUser->single;
            $key = $accnt . $identification_number . $itemCommonCode . '_' . $itemId;
//            if ($itemCommonCode) {
//            }
            $commonCodes[] = $key;
            if (!empty($itemUserShareCode)) {
                $tempUsers = TeamRoomUserService::getInstance()->getTeamRoomUserListByShareCode($itemUserShareCode);
                foreach ($tempUsers as $itemUser) {
                    $itemUserCommonCode = $itemUser->common_code;
                    $itemUser_accnt = $tempUser->accnt;
                    $itemUser_identification_number = $tempUser->identification_number;
                    $item_key = $itemUser_accnt . $itemUser_identification_number . $itemUserCommonCode;
                    if (!in_array($item_key, $commonCodes)) {
                        $commonCodes[] = $item_key;
                    }
                }
            }
        }
        DebugUtil::log(json_encode($commonCodes));
        $selectBillIdKey = self::$SELECT_USER_KEY . $billId;
        if (CacheManager::has($selectBillIdKey)) {
            $cacheUserCommonCodes = CacheManager::getCacheValue($selectBillIdKey);
            $commonCodes = array_merge($commonCodes, $cacheUserCommonCodes);
            $commonCodes = array_unique($commonCodes);
            DebugUtil::log(json_encode($commonCodes));
        }

        return count($commonCodes);
    }

    public function isSingle($billId)
    {
        $teamRoomUserList = TeamRoomUserService::getInstance()->getRoomUserByBillId($billId);
        foreach ($teamRoomUserList as $tempUser) {
            $single = $tempUser->single;
            if ($single == 1) {
                return true;
            }
        }
        return false;
    }

    public function getSameRoomUser($teamId, $commonCode)
    {
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (empty($teamUser)) {
            return 0;
        }
        $shareCode = $teamUser->share_code;
        if (empty($shareCode)) {
            return 1;
        }
        return TeamRoomUserService::getInstance()->getTeamUserCountByShareCode($shareCode);
    }

    /**
     * 同住人房间id
     * @param $teamId
     * @param $commonCode
     * @return int
     */
    public function getShareRoomBillId($teamId, $commonCode)
    {
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (empty($teamUser)) {
            return 0;
        }
        $shareCode = $teamUser->share_code;
        if (empty($shareCode)) {
            return 0;
        }
        $users = TeamRoomUserService::getInstance()->getTeamRoomUserListByShareCode($shareCode);
        $billId = 0;
        foreach ($users as $user) {
            $tempCommonCode = $user->common_code;
            if ($tempCommonCode != $commonCode) {
                $tempBillId = $user->bill_id;
                if ($tempBillId) {
                    $billId = $tempBillId;
                    break;
                }
            }
        }
        return $billId;
    }


    /**
     * 1、分享人
     *    1）选择无同住人传confirm=0.share=0支付，人脸认证完成，authentication=1，分配房间。
     *    2）1.选择有同住人传confirm=1，share=1,支付房费。认证，authentication=1。
     *                如果点击分享链接，则生成share_code,限制时间share_limit,
     *                有share_code 代表分享过，等待对方同意。无则提示分享
     *        2.对方未点击链接，user数组只有自己
     *        3.对方同意，users添加对方，confirm=1。赋值bill_id
     *        4.点击拒绝与对方同住share=-1,可选择继续分享或者无同住人，share_code=null,share_limit=null,可再次选择分享或者无同住人
     * 2、被分享人
     *        1）点击链接，同意同住。confirm = 1，
     *        2）users数组添加自己。支付完成。
     *        3）认证完成confirm=1进入房卡，赋值bill_id
     *
     * 查询团队信息
     * @param $id
     * @param $userId
     * @param $shareCode
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     */
    public function getTeamInfoById($id, $userId, $shareCode)
    {
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        TeamRoomQrService::getInstance()->checkQr($commonCode, $shareCode);
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($id, $commonCode);
        if (empty($teamUser)) {
            $userCommon = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
            if (empty($userCommon)) {
                throw new MyException(Code::FAIL, "请先授权手机号");
            }
        } else {
            $tempBillId = $teamUser->bill_id;
            if ($tempBillId) {
                $this->getBillRoomCleanStatus($tempBillId);
            }
        }
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($id);
        $teamId = $team->id;
        $teamUsers = TeamRoomUserService::getInstance()->getTeamAllUser($teamId, $commonCode, $shareCode);
        $team->users = $teamUsers;
        $currentUserOcr = UserOcrService::getInstance()->getUserOcrByCommonCode($commonCode);
        if ($currentUserOcr) {
            $currentUserOcrName = $currentUserOcr->name;
            TeamSelectRoomUtil::getInstance()->checkUserInTeam($commonCode, $teamId, $currentUserOcrName);
        }
        $qrInfo = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
        if ($qrInfo) {
            $qrCommonCode = $qrInfo->common_code;
            $userName = null;
            $userGender = null;
            $fromUserName = null;
            $fromUserGender = null;
            $userOcr = UserOcrService::getInstance()->getUserOcrByCommonCode($qrCommonCode);
            if ($userOcr) {
                $userName = $userOcr->name;
                $userGender = $userOcr->gender;
            }
            $commonCodeFrom = $qrInfo->common_code_from;
            $fromUserOcr = UserOcrService::getInstance()->getUserOcrByCommonCode($commonCodeFrom);
            if ($fromUserOcr) {
                $fromUserName = $fromUserOcr->name;
                $fromUserGender = $fromUserOcr->gender;
            }
            $qrInfo->user_name = $userName;
            $qrInfo->user_gender = $userGender;
            $qrInfo->from_user_name = $fromUserName;
            $qrInfo->from_user_gender = $fromUserGender;
        }
        $team->qr_info = $qrInfo;
        return $team;
    }

    /**
     * 检查是否到入住时间
     * @param $teamId
     * @param $commonCode
     * @return bool
     * @throws MyException
     */
    public function checkCheckInTime($teamId, $commonCode)
    {
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (empty($teamUser)) {
            throw new MyException(Code::FAIL, "不在团队中");
        }
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySign($shopId, ShopSysSetting::ENTER_TIME);
        $settingEnterHour = $enterTimeSetting->property['value'];
        $flag = false;
        $billId = $teamUser->bill_id;
        if ($billId) {
            $bill = RoomBillService::getInstance()->getBillByIdFromCache($billId);
            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;

        } else {
            $enterTimePlan = $team->enter_time_plan;
            $leaveTimePlan = $team->leave_time_plan;
        }
        $enterDate = date("Y-m-d", $enterTimePlan);
        $settingEnterHourTime = strtotime("$enterDate $settingEnterHour");
        DebugUtil::log("设置入住时间：$enterDate $settingEnterHour");
        if (time() >= $settingEnterHourTime && time() <= $leaveTimePlan) {
            $flag = true;
        }
        return $flag;

    }


    /**
     * 确认团队
     * @param $userId
     * @param $teamId
     * @param $qrCode
     * @param $confirm
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function confirmTeam($userId, $teamId, $qrCode, $billId, $single)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        if (empty($team) || $team->status != 1) {
            throw new MyException(Code::FAIL, "团队不存在");
        }
        $teamType = $team->team_type;
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        $currentUserOcr = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        if ($currentUserOcr) {
            $currentUserOcrName = $currentUserOcr->name;
            TeamSelectRoomUtil::getInstance()->checkUserInTeam($commonCode, $teamId, $currentUserOcrName);
        }
        $this->addTeamUser($commonCode, $teamId, $teamType, null, $single);
        if ($billId) {
            $teamBill = RoomBillService::getInstance()->getBillByIdFromCache($billId);
            if (!empty($teamBill)) {
                $room_type_id = $teamBill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($room_type_id);
                $bed_count = $roomType->bed_count;
                $users = TeamRoomUserService::getInstance()->getRoomUserByBillId($billId);
                $countUser = count($users);
                foreach ($users as $tempUser) {
                    if ($tempUser->common_code == $commonCode) {
                        $countUser--;
                    }
                }
                if ($countUser >= $bed_count) {
                    throw new MyException(Code::FAIL, "该房间已满");
                }
                $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
                TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUser->id, 'bill_id' => $billId]);
                $this->getBillRoomCleanStatus($billId);
            }
        }
        $team->users = TeamRoomUserService::getInstance()->getTeamAllUser($teamId, $commonCode, $qrCode);
        return $team;
    }

    /**
     * 选择是否有指定同住人
     * @param $userId
     * @param $teamId
     * @param $confirm
     * @param $qrCode
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     */
    public function chooseStayUser($userId, $teamId, $confirm, $qrCode)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        if (empty($team) || $team->status != 1) {
            throw new MyException(Code::FAIL, "团队不存在");
        }
        $shareUsable = $team->share_usable;
        if ($shareUsable == 0) {
            $confirm = 0;
        }
        $shopId = $team->shop_id;
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if ($teamUser == null && $confirm == -1) {
            //通过扫分享码、链接进入拒绝同住
            if (!empty($qrCode)) {
                TeamRoomQrService::getInstance()->updateCommonCode($qrCode, $commonCode, $confirm);
                $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($qrCode);
                $commonCodeFrom = $qr->common_code_from;
                $sourceCommonUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCodeFrom);
                $sourceCommonUserId = $sourceCommonUser->id;
                DebugUtil::log("$commonCode 拒绝了邀请：$commonCodeFrom");
                TeamRoomUserService::getInstance()->refuseSameRoom($sourceCommonUserId);
                TeamRoomQrService::getInstance()->updateCommonCode($qrCode, $commonCode, $confirm);
            }
            $team->users = [];
            return $team;
        }
        $teamUserAccept = $teamUser->accept;
        $teamUserId = $teamUser->id;
        $teamUserBillId = $teamUser->bill_id;
        $authentication = $teamUser->authentication;
        $oldShare = $teamUser->share;
        $maxUserCount = 2;
        if (!empty($qrCode)) {
            TeamRoomQrService::getInstance()->checkQr($commonCode, $qrCode);
            //更新二维码状态
            TeamRoomQrService::getInstance()->updateCommonCode($qrCode, $commonCode, $confirm);
            $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($qrCode);
            $limitTime = $qr->limit_time;
            $commonCodeFrom = $qr->common_code_from;
            //判断当前用户是否能接受邀请，不能接受邀请，qr的status=4
            DebugUtil::log("teamUserAccept:$teamUserAccept");
            if ($teamUserAccept == 0) {
                TeamRoomQrService::getInstance()->updateStatus($qr->id, 4);
                TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'share_code' => null, 'limit_time' => null]);
                $team->users = TeamRoomUserService::getInstance()->getTeamAllUser($teamId, $commonCode, $qrCode);
                return $team;
            }
            //判断相互点链接
            $sourceCommonUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCodeFrom);
            $sourceCommonUserId = $sourceCommonUser->id;
            $sourceCommonUserBillId = $sourceCommonUser->bill_id;
            //当前用户分配的房间是单间
            if ($sourceCommonUser->share_code && $confirm == 1) {
                $fromQr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($sourceCommonUser->share_code);
                $fromQrCode = $fromQr->code;
                $romCommonCodeFrom = $fromQr->common_code_from;
                if ($romCommonCodeFrom == $commonCode) {
                    TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'share_code' => $sourceCommonUser->share_code]);
                    $team->users = TeamRoomUserService::getInstance()->getTeamAllUser($teamId, $commonCode, $fromQrCode);
                    return $team;
                }
            }
            if ($teamUserBillId && $sourceCommonUserBillId && ($teamUserBillId != $sourceCommonUserBillId)) {
                throw new MyException(Code::SUCCESS, "您已分配房间不能接受邀请");
            }
            if ($confirm == 1) {
                DebugUtil::log("$commonCode 同意了邀请：$commonCodeFrom");
                //同意同住
                $sourceBillId = $sourceCommonUser->bill_id;
                if ($sourceBillId != 0) {
                    $bill = RoomBillService::getInstance()->getBillById($sourceBillId);
                    $roomTypeId = $bill->room_type_id;
                    $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                    $maxUserCount = $roomType->max_user_count;
                    $tempUserCount = RoomBillUserService::getInstance()->getRoomBillUserCount($sourceBillId);
                    if ($tempUserCount >= $maxUserCount) {
                        throw new MyException(Code::FAIL, "当前房间人数已满");
                    }
                }
                $payData = $this->getPayAmount($teamId, $userId);
                if ($payData['total_amount'] <= 0) {
                    TeamRoomUserService::getInstance()->updateTeamUserConfirm($teamUserId, 1);
                }
                $tempTeamUser = TeamRoomUserService::getInstance()->getTeamRoomUserByShareCode($qrCode);
                if (empty($tempTeamUser)) {
                    throw new MyException(Code::FAIL, "分享已失效");
                }

                TeamRoomUserService::getInstance()->updateShare($teamUserId, $confirm, $limitTime, $qrCode);
            } elseif ($confirm == -1) {
                //拒绝其他人的分享
                DebugUtil::log("$commonCode 拒绝了邀请：$commonCodeFrom");
                TeamRoomUserService::getInstance()->refuseSameRoom($sourceCommonUserId);
            } else {
                DebugUtil::log("0无同住人入住");
                //0w无同住人入住
            }
            //次数用户已经上传身份证照片，需要根据身份证信息的姓名将团队人名单跟当前用户做绑定
        } else {
            if ($oldShare == 0) {
                $confirm = 0;
            }
            TeamRoomUserService::getInstance()->updateShare($teamUserId, $confirm, null, null);
            //如果已认证，则直接入住
            if ($confirm == 0 && $authentication == 1) {
                $this->setTeamRoomUserSingle($shopId, $teamId, $commonCode);
            }
        }
        $team->users = TeamRoomUserService::getInstance()->getTeamAllUser($teamId, $commonCode, $qrCode);
        return $team;
    }


    /**
     * 被拒绝后是否继续分享，不继续则分配房间
     * @param $teamId
     * @param $userId
     * @param $continue
     */
    public function continueShare($teamId, $userId, $continue)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $wxUser = UserWxService::getInstance()->getUserById($userId, true);
        $commonCode = $wxUser->common_code;
        $res = null;
        if ($continue == 0) {
            $this->setTeamRoomUserSingle($shopId, $teamId, $commonCode);
        } else {
            //继续分享
            $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
            if ($teamUser->share == -1) {
                TeamRoomUserService::getInstance()->updateShare($teamUser->id, 1, null, null);
            }
        }
    }

    /**
     * 当前用户已入住，再点其他人分享的链接，分配到同一个房间
     * @param $userId
     * @param $teamId
     * @param $shareCode
     * @throws MyException
     */
    public function staySameRoom($userId, $teamId, $shareCode)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $wxUser = UserWxService::getInstance()->getUserById($userId, true);
        $commonCode = $wxUser->common_code;
        $shopIdZong = $wxUser->shop_id_zong;
        $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $billId = $teamUser->bill_id;
        $teamUserId = $teamUser->id;
        $authentication = $teamUser->authentication;
        $commonCodeFrom = $qr->common_code_from;
        $accept = $teamUser->accept;
        if ($accept == 0) {
            throw new MyException(Code::SUCCESS, "您已分配房间，无法和对方同住");
        }
        if ($billId) {
            //已入住
            $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCodeFrom);
            $fromBillId = $teamUser->bill_id;
            if ($billId != $fromBillId) {
                throw new MyException(Code::SUCCESS, "您已分配房间，无法和对方同住");
            }

        }
        if ($authentication != 1) {
            throw new MyException(Code::FAIL, "请完成人脸认证");
        }
        $teamFund = TeamFundService::getInstance()->getTeamFundByCommonCode($teamId, $commonCode, 1);
        if (empty($teamFund)) {
            throw new MyException(Code::FAIL, "请完成支付");
        }
        $room_amount = $teamFund->room_amount;
        $cash_pledge = $teamFund->cash_pledge;
        $accountId = $teamFund->account_id;
        $user_coupon_id = $teamFund->coupon_id;
        $outTradeNo = $teamFund->out_trade_no;
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $billAccnt = $bill->accnt;
        $userCount = RoomBillUserService::getInstance()->getRoomBillUserCount($billId);
        $roomTypeId = $bill->room_type_id;
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $maxUserCount = $roomType->max_user_count;
        if ($userCount >= $maxUserCount) {
            throw new MyException(Code::FAIL, "房间人数已满");
        }
        $roomId = $bill->room_id;
        $remark = "团队入住";
        $memo = "团队入住";
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::BOOK_ROOM);
        $room_bill_type_id = $detailType->id;
        RoomBillDetailService::getInstance()->addRoomBillDetail($billId, $roomId, $room_amount, 2, 1, $remark, 0, $accountId,
            $room_bill_type_id, $user_coupon_id, 1, 0, $memo, 0, 0, $outTradeNo, $commonCode);
        if ($user_coupon_id) {
            $couponRecord = CouponRecordService::getInstance()->getCouponRecordById($user_coupon_id);
            $coupon_id = $couponRecord->coupon_id;
            RoomBillDetailService::getInstance()->addRoomBillDetail($billId, $roomId, $cash_pledge, 2, 1, $remark, 0, $accountId,
                $room_bill_type_id, $coupon_id, 1, 0, $memo, 0, 0, null, $commonCode);
        }
        $this->addRoomUserFromTeamUser($shopIdZong, $teamId, $commonCodeFrom, $billId, $billAccnt);
        TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'share_code' => $shareCode]);
    }

    /**
     * 同住人支付房费后一起排房
     * @param $shopId
     * @param $teamId
     * @param $shareCode
     * @param $commonCode
     */
    public function setTeamUserRoom($shopId, $teamId, $shareCode, $commonCode)
    {
        DebugUtil::log("setTeamUserCommonCode: $commonCode");
        $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
        $commonCodeFrom = $qr->common_code_from;
        $fromCommonUser = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCodeFrom);
        $shopIdZong = $fromCommonUser->shop_id_zong;
        if ($commonCodeFrom == $commonCode) {
            $commonUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $qr->common_code);
        } else {
            $commonUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        }
        $commonUserBillId = $commonUser->bill_id;
        $fromUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCodeFrom);
        $users = TeamRoomUserService::getInstance()->getTeamRoomUserListByShareCode($shareCode);
        $userCount = count($users);
        $tempUserBillId = $fromUser->bill_id;
        if ($commonUserBillId && $tempUserBillId && ($commonUserBillId != $tempUserBillId)) {
            throw new MyException(Code::SUCCESS, "您已分配房间不能接受邀请");
        }
        $setBillId = 0;
        if ($commonUserBillId) {
            $setBillId = $commonUserBillId;
        } elseif ($tempUserBillId) {
            $setBillId = $tempUserBillId;
        }
        if ($setBillId) {
            //已经排房
            $main = 0;
            $roomMainUser = RoomBillUserService::getInstance()->getRoomBillMainUser($setBillId);
            if (empty($roomMainUser)) {
                $main = 1;
            }
            foreach ($users as $tempUser) {
                $tempUserId = $tempUser->id;
                $tempCommonCode = $tempUser->common_code;
                $team_authentication = $tempUser->authentication;
                if ($tempCommonCode != $commonCodeFrom) {
                    $main = 0;
                }
                //房间添加入住人
                $bill = RoomBillService::getInstance()->getBillById($setBillId);
                $accnt = $bill->accnt;
                DebugUtil::log("setBillId: $setBillId :  $accnt");
                DebugUtil::log(json_encode($bill));
                $this->addRoomUserFromTeamUser($shopIdZong, $teamId, $tempCommonCode, $setBillId, $accnt);
                //转移支付财务
                $this->updateTeamUserFund($teamId, $shopId, $setBillId, $tempCommonCode);
                if ($team_authentication == 1) {
                    TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($tempUserId, 0);
                    $canCheckIn = $this->checkCheckInTime($teamId, $commonCode);
                    if ($canCheckIn) {
                        $this->billToStaying($setBillId, $tempCommonCode);
                    }
                }
            }
        } else {
            $flag = false;
            //查询房间最大人数为当前人数的房间
            $selectBillId = $this->selectBillIdNew($shopId, $teamId, $commonCode, $userCount - 1);
            if (!$selectBillId) {
                throw new MyException(Code::FAIL, "没有可分配房间");
            }
            DebugUtil::log("setTeamUserRoom: $selectBillId");
            $main = 0;
            $roomMainUser = RoomBillUserService::getInstance()->getRoomBillMainUser($selectBillId);
            if (empty($roomMainUser)) {
                $main = 1;
            }
            foreach ($users as $tempUser) {
                $tempUserId = $tempUser->id;
                $tempCommonCode = $tempUser->common_code;
                $team_authentication = $tempUser->authentication;
                //房间添加入住人
                if ($tempUser->authentication != 1) {
                    continue;
                }
                $bill = RoomBillService::getInstance()->getBillById($selectBillId);
                $billAccnt = $bill->accnt;
                $this->addRoomUserFromTeamUser($shopIdZong, $teamId, $tempCommonCode, $selectBillId, $billAccnt);
                //转移支付财务
                $this->updateTeamUserFund($teamId, $shopId, $selectBillId, $tempCommonCode);
                if ($team_authentication == 1) {
                    TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($tempUserId, 0);
                    $canCheckIn = $this->checkCheckInTime($teamId, $commonCode);
                    if ($canCheckIn) {
                        $this->billToStaying($selectBillId, $tempCommonCode);
                    }
                }
            }
        }
    }


    /**
     * 团队单人分配房间
     * @param $shopId
     * @param $teamId
     * @param $commonCode
     */
    public function setTeamRoomUserSingle($shopId, $teamId, $commonCode)
    {
        $commonUser = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
        $this->shop_id_zong = $commonUser->shop_id_zong;
        $shopIdZong = $this->shop_id_zong;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $teamRoomPrice = $teamUser->room_price;
        $cash_pledge = $teamUser->cash_pledge;
        $teamUserId = $teamUser->id;
        $share = $teamUser->share;
        $outTradeNo = null;
        $room_amount = 0;
        $user_coupon_id = 0;
        $accountId = 0;
        $date = date("Y-m-d");
        $hasFund = false;
        $selectBillId = $this->selectBillIdNew($shopId, $teamId, $commonCode, $share);
        if (!$selectBillId) {
            throw new MyException(Code::FAIL, "没有可分配房间");
        }
        $cleanStatus = $this->getBillRoomCleanStatus($selectBillId);
        if ($cleanStatus != 1) {
            throw new MyException(Code::FAIL, "当前房间正在清洁中，请稍后再试！");
        }
        if ($teamRoomPrice > 0) {
            $teamFund = TeamFundService::getInstance()->getTeamFundByCommonCode($teamId, $commonCode, 1);
            if ($teamFund) {
                $hasFund = true;
                $room_amount = $teamFund->room_amount;
                $cash_pledge = $teamFund->cash_pledge;
                $accountId = $teamFund->account_id;
                $user_coupon_id = $teamFund->coupon_id;
                $outTradeNo = $teamFund->out_trade_no;
            }
        }
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySignFromCache($shopId, RoomBillDetailType::BOOK_ROOM);
        $room_bill_type_id = $detailType->id;
        $roomDetail = RoomBillDetailService::getInstance()->getFundByCommonCode($selectBillId, $commonCode, $room_bill_type_id, 2, 1);
        if ($roomDetail) {
            return;
        }
        $bill = RoomBillService::getInstance()->getBillByIdFromCache($selectBillId);
        $billAccnt = $bill->accnt;
        $roomId = $bill->room_id;
        $this->addRoomUserFromTeamUser($shopIdZong, $teamId, $commonCode, $selectBillId, $billAccnt);
        //更新订单财务
        $remark = "团队入住";
        $memo = "团队入住";
        if ($teamRoomPrice > 0 && $hasFund) {
            RoomBillDetailService::getInstance()->addRoomBillDetail($selectBillId, $roomId, $room_amount, 2, 1, $remark, 0, $accountId,
                $room_bill_type_id, $user_coupon_id, 1, 0, $memo, 0, 0, $outTradeNo, $commonCode);
            if ($user_coupon_id) {
                $couponRecord = CouponRecordService::getInstance()->getCouponRecordById($user_coupon_id);
                $coupon_id = $couponRecord->coupon_id;
                RoomBillDetailService::getInstance()->addRoomBillDetail($selectBillId, $roomId, $cash_pledge, 2, 1, $remark, 0, $accountId,
                    $room_bill_type_id, $coupon_id, 1, 0, $memo, 0, 0, null, $commonCode);
            }
        }
        TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 0);
        $canCheckIn = $this->checkCheckInTime($teamId, $commonCode);
        if ($canCheckIn) {
            $this->billToStaying($selectBillId, $commonCode);
        }
    }

    private function billToStaying($selectBillId, $commonCode)
    {
        DebugUtil::log("$selectBillId, $commonCode");
        $roomUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($selectBillId, $commonCode);
        $isMain = $roomUser->is_main;
        $itemUserId = $roomUser->id;
        $userName = $roomUser->name;
        $userAccnt = $roomUser->accnt;
        //更新订单为入住中
        //房价日期
        //添加房费记录
        $bill = RoomBillService::getInstance()->getBillByIdFromCache($selectBillId);
        $enterTimePlan = $bill->enter_time_plan;
        $teamId = $bill->team_id;
        $billStatus = $bill->bill_status;
        $shopId = $bill->shop_id;
        $roomTypeId = $bill->room_type_id;
        $roomId = $bill->room_id;
        $leaveTimePlan = $bill->leave_time_plan;
        if (!$roomId) {
            $roomId = $this->autoSetRoomBillRoom($teamId, $selectBillId, $userName);
        }
        $room = RoomService::getInstance()->getRoomByIdFromCache($roomId);
        $roomNumber = $room->room_number;
        $roomTypeName = $room->room_type_name;
        $orderAccnt = $bill->accnt;
        $nowDate = date('Y-m-d');
        $currentTime = time();
        $enterTimeSetting = ShopSysSettingService::getInstance()->getSysSettingBySignFromCache($shopId, ShopSysSetting::ENTER_TIME);
        $settingEnterHour = $enterTimeSetting->property['value'];
        //大约预定日期当日6点
        $settingEnterHourTime = strtotime("$nowDate $settingEnterHour");
        $priceDate = date('Y-m-d');
        //小于当前最早入住时间，营业日算前一天
        if ($currentTime < $settingEnterHourTime) {
            $priceDate = TimeUtil::getInstance()->getDate($nowDate, -1);
        }

        RoomBillPriceDateService::getInstance()->bookingToStayCheckPrice($shopId, $selectBillId);
        $teamUserCount = RoomBillUserService::getInstance()->getCount($shopId, $selectBillId, null, null, null);
        //团队入住同时人脸识别转入住,在未完成人脸时数据未提交都是待入住状态，造成hook数据都走预定转入住
        //第一个人预定时，增加缓存。在未完成数据提交时通过缓存判断，第一个人入住失败则删除缓存
        $tempFundKey = "addConsumeRoomFund_$selectBillId" . $priceDate;
        if (!CacheManager::has($tempFundKey) && $isMain == 1) {
            RoomFundUtil::getInstance()->addConsumeRoomFund($shopId, $selectBillId, $priceDate, '', 0);
            CacheManager::setCacheValue($tempFundKey, $selectBillId, null, null, 10);
        }
        //没有开启通知直接跳过
        //更新房态
        $isStaying = false;
        if ($billStatus == RoomBillStatus::STAYING || $billStatus == RoomBillStatus::TO_CHECK_OUT) {
            $isStaying = true;
        } else {
            RoomStatusRecordService::getInstance()->bookToStay($shopId, $selectBillId, $roomId, $roomId, $enterTimePlan, $leaveTimePlan, RoomRecordStatus::STAYING);
            RoomBillUtil::getInstance()->bookToStay($selectBillId, $roomTypeId, $roomId);
        }
        //房价日期
        RoomBillUserService::getInstance()->editRoomBillUser(['id' => $itemUserId, 'user_status' => 1]);
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $teamUserAccnt = $teamUser->accnt;
        $teamUserId = $teamUser->id;
        TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 1);
        //主入住人已分配
        $intermediaryId = $bill->intermediary_id;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        BreakfastCouponRecordService::getInstance()->checkRoomBillBreakfastCouponRecord($shopIdZong, $shopId, $selectBillId, $commonCode, $intermediaryId);
        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "团队自助入住  $userName $roomTypeName $roomNumber", '团队自助入住', RoomLogType::STAY, $selectBillId);
        if (!$isStaying) {
            $changeRoomUserBillKey = "changeRoomUserBillKey_" . $commonCode;
            DebugUtil::log("changeRoomUserBillKey2:$changeRoomUserBillKey");
            if (CacheManager::has($changeRoomUserBillKey)) {
                $updateHookList = CacheManager::getCacheValue($changeRoomUserBillKey);
                DebugUtil::log($updateHookList);
                $updateUsers = [];
                if ($updateHookList) {
                    foreach ($updateHookList as $updateHookBill) {
                        $updateHookBillId = $updateHookBill['bill_id'];
                        $tempUsers = TeamRoomUserService::getInstance()->getRoomUserListByBillId($updateHookBillId);
                        $updateUsers = array_merge($updateUsers, $tempUsers);
                    }
                    if ($updateUsers) {
                        $team = TeamRoomService::getInstance()->getTeamRoomById($teamId);
                        $team['user_list'] = $updateUsers;
                        RoomHookService::getInstance()->teamRoomUpdateHook($shopId, $team);
                    }
                }
                CacheManager::clearCacheValue($changeRoomUserBillKey);
            }
            RoomHookService::getInstance()->wxTeamRoomBillCheckIn($selectBillId, $commonCode, 'mini_program_team');
        } else {
            RoomHookService::getInstance()->updateTeamRoomUserInfo($selectBillId, $commonCode);
        }
    }


    /**
     * 转移财务数据
     * @param $teamId
     * @param $shopId
     * @param $billId
     * @param $commonCode
     */
    private function updateTeamUserFund($teamId, $shopId, $billId, $commonCode)
    {
        $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::BOOK_ROOM);
        $room_bill_type_id = $detailType->id;
        $roomDetail = RoomBillDetailService::getInstance()->getFundByCommonCode($billId, $commonCode, $room_bill_type_id, 2, 1);
        if ($roomDetail) {
            return;
        }
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $roomId = $bill->room_id;
        $teamFund = TeamFundService::getInstance()->getTeamFundByCommonCode($teamId, $commonCode, 1);
        if ($teamFund) {
            $room_amount = $teamFund->room_amount;
            $cash_pledge = $teamFund->cash_pledge;
            $accountId = $teamFund->account_id;
            $user_coupon_id = $teamFund->coupon_id;
            $outTradeNo = $teamFund->out_trade_no;
            //更新订单财务
            $remark = "团队入住";
            $memo = "团队入住";
            RoomBillDetailService::getInstance()->addRoomBillDetail($billId, $roomId, $room_amount, 2, 1, $remark, 0, $accountId,
                $room_bill_type_id, $user_coupon_id, 1, 0, $memo, 0, 0, $outTradeNo, $commonCode);
            if ($user_coupon_id) {
                $couponRecord = CouponRecordService::getInstance()->getCouponRecordById($user_coupon_id);
                $coupon_id = $couponRecord->coupon_id;
                RoomBillDetailService::getInstance()->addRoomBillDetail($billId, $roomId, $cash_pledge, 2, 1, $remark, 0, $accountId,
                    $room_bill_type_id, $coupon_id, 1, 0, $memo, 0, 0, null, $commonCode);
            }
            $amount = RoomBillDetailService::getInstance()->getDetailSumAmount($billId, 2, null);
            RoomBillService::getInstance()->updateAlreadyPay($billId, $amount);
        }
    }

    /**
     * 将团队用户添加为入住人
     * @param $shop_id_zong
     * @param $teamId
     * @param $commonCode
     * @param $billId
     * @param $is_main
     * @param $authentication
     */
    private function addRoomUserFromTeamUser($shop_id_zong, $teamId, $commonCode, $billId, $billAccnt)
    {
        $cacheKey = "addRoomUserFromTeamUser_" . $teamId . "_" . $commonCode;
        $old = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($billId, $commonCode);
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $authentication = $teamUser->authentication;
        $user_image = $teamUser->user_image;
        $itemUserBillId = $teamUser->bill_id;
        if (!empty($old) || CacheManager::has($cacheKey)) {
            if ($old) {
                $oldAuthentication = $old->authentication;
                if ($oldAuthentication == 1 || $authentication == 1) {
                    RoomBillUserService::getInstance()->updateUserStatus($old->id, 1);
                    RoomBillUserService::getInstance()->editRoomBillUser(['id' => $old->id, 'authentication' => 1]);
                }
            }
            return;
        }
        $user = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
        $phone = $user->phone;
        if ($itemUserBillId == 0) {
            TeamRoomUserService::getInstance()->updateTeamUser(['id' => $teamUser->id, 'bill_id' => $billId]);;
        }
        $userOcr = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        $ocrGender = $userOcr->gender;
        if ($ocrGender == '男') {
            $gender = 1;
        } else {
            $gender = 2;
        }
        $name = $userOcr->name;
        $nationName = $userOcr->nation;
        $nation = NationService::getInstance()->getNationByNationName($nationName);
        $nationId = $nation->id;
        $birthday = $userOcr->birthday;
        $address = $userOcr->address;
        $identification_number = $userOcr->identification_number;
        $identification_image = $userOcr->id_image;
        $authentication = $teamUser->authentication;
        //0未入住，1在住，2离店
        $userStatus = 0;
        $is_main = 0;
        $mainUser = RoomBillUserService::getInstance()->getRoomBillMainUser($billId);
        if (empty($mainUser)) {
            $is_main = 1;
        } else {
            if ($mainUser->name == $name) {
                $is_main = 1;
            }
        }

        $mainUserKey = "main_user_$billId";
        if (CacheManager::has($mainUserKey)) {
            $is_main = 0;
        }
        CacheManager::setCacheValue($cacheKey, $commonCode, null, null, 10);
        $roomBillUserId = RoomBillUserService::getInstance()->addRoomBillUserFromTeam($shop_id_zong, $billId, $name, $phone,
            $gender, $is_main, 1, $identification_number, $nationId, $authentication, $user_image, $identification_image, $commonCode,
            $birthday, $address, 0, 0, $userStatus);
        if ($is_main == 1) {
            CacheManager::setCacheValue($mainUserKey, $commonCode, null, null, 10);
            if ($billAccnt) {
                $oldTeamUser = TeamRoomUserService::getInstance()->getTeamAllUserByAccnt($teamId, $billAccnt);
                if (empty($oldTeamUser)) {
                    RoomBillUserService::getInstance()->editRoomBillUser(['id' => $roomBillUserId, 'accnt' => $billAccnt]);
                } elseif (!empty($oldTeamUser) && $oldTeamUser->name == $name) {
                    RoomBillUserService::getInstance()->editRoomBillUser(['id' => $roomBillUserId, 'accnt' => $billAccnt]);
                }
            }
        }
    }

    /**
     * 添加团队入住人
     * @param $commonCode
     * @param $teamId
     * @param $billId
     * @param $shareCode
     * @param $confirm
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    private function updateUserShear($commonCode, $teamId, $billId, $shareCode, $confirm)
    {
        $oldUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $share = $confirm;
        $id = $oldUser->id;
        $updateDate = ['id' => $id, 'share' => $share];
        if ($share == -1) {
            $updateDate['share_code'] = null;
        }
        TeamRoomUserService::getInstance()->editTeamRoomUser($updateDate);
        if ($shareCode) {
            $alreadyPayAmount = TeamFundService::getInstance()->getTeamFundAmountByCommonCode($teamId, $commonCode, 1);
            if ($alreadyPayAmount > 0) {
                TeamRoomUserService::getInstance()->updateTeamUserConfirm($id, $share);
            }
            if ($share == -1) {
                TeamRoomUserService::getInstance()->updateShare($id, 2, null, null);
            } else {
                TeamRoomUserService::getInstance()->updateShare($id, $share, null, $shareCode);
            }
        } else {
            $updateDate = ['id' => $id, 'share' => $share];
            if ($share == -1) {
                $updateDate['share_code'] = null;
            }
            TeamRoomUserService::getInstance()->editTeamRoomUser($updateDate);
        }
    }


    /**
     * 添加用户到团队中
     * @param $commonCode
     * @param $teamId
     * @param $teamType
     * @param $billId
     * @param $qrCode
     */
    private function addTeamUser($commonCode, $teamId, $teamType, $billId, $single)
    {
        $commonUser = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
        $shopIdZong = $commonUser->common_code;
        $userOcr = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        $gender = null;
        if (empty($userOcr)) {
            $name = "";
            $gender = $commonUser->gender;
        } else {
            $name = $userOcr->name;
            $ocrGender = $userOcr->gender;
            if ($ocrGender == '男') {
                $gender = 1;
            } else {
                $gender = 2;
            }
        }
        $phone = $commonUser->phone;
        $oldUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (empty($oldUser)) {
            $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
            $shareUsable = $team->share_usable;
            if ($teamType == 3) {
                $share = 1;
            } else {
                $share = 2;
                if ($shareUsable == 0) {
                    $share = 0;
                }
            }
            $identification_type = 1;
            $identification_number = null;
            $nation_id = 1;
            $teamUserId = TeamRoomUserService::getInstance()->addTeamUser($teamId, $billId, $name, $phone, $identification_type,
                $identification_number, $gender, $nation_id, $commonCode, null, $share, $single);
            if ($userOcr) {
                $idUrl = $userOcr->id_image;
                TeamSelectRoomUtil::getInstance()->updateTeamUserInfoFromOcr($teamId, $commonCode, $idUrl);
                TeamRoomUserService::getInstance()->synchronizationTeamRoomUser($teamId, $commonCode);
            } else {
                TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'share' => $share]);
            }
            if (!StringUtil::checkEmpty($gender)) {
                TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'gender' => $gender]);
            }
        }
        if ($billId) {
            $this->saveRoomBillUser($shopIdZong, $phone, $teamId, $billId, $commonCode);
        }
    }


    /**
     * 团队订单未排房时自动分配房间
     * @param $billId
     * @return int
     */
    public function autoSetRoomBillRoom($teamId, $billId, $name)
    {
        $bill = RoomBillService::getInstance()->getBillByIdFromCache($billId);
        $oldRoomId = $bill->room_id;
        if ($oldRoomId) {
            return $billId;
        }
        $roomTypeId = $bill->room_type_id;
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $bedCount = $roomType->bed_count;
        $roomTypeCode = $roomType->room_type_code;
        $shopId = $bill->shop_id;
        $startTime = $bill->enter_time_plan;
        $endTime = $bill->leave_time_plan;
        $roomTypeCacheKey = md5("$shopId, $startTime, $endTime, $roomTypeCode");
        if (CacheManager::has($roomTypeCacheKey)) {
            $data = CacheManager::getCacheValue($roomTypeCacheKey);
        } else {
            $res = RoomBill::getInstance()->getRoomList($shopId, $startTime, $endTime, $roomTypeCode);
            $data = $res['data'];
            CacheManager::setCacheValue($roomTypeCacheKey, $data, null, null, 10);
        }
        $setRoom = false;
        $selectRoomId = 0;
        if (!empty($data)) {
            $userList = TeamRoomUserService::getInstance()->getRoomUserByBillId($billId);
            $countUser = count($userList);
            $inRoom = false;
            $hasSingle = false;
            foreach ($userList as $user) {
                $userName = $user->name;
                $tempSingle = $user->single;
                if ($userName == $name) {
                    $inRoom = true;
                }
                if ($tempSingle) {
                    $hasSingle = true;
                }
            }
            foreach ($data as $item) {
                $roomNumber = $item['room_number'];
                $cacheKey = "autoSetRoomBillRoom_" . $roomNumber;
                $itemUsable = false;
                if (isset($item['room_status']) && $item['room_status'] == "VC") {
                    $itemUsable = true;
                    StandardRoomService::getInstance()->apiUpdateRoomStatus($shopId, $roomNumber, 1);
                }
                if (!CacheManager::has($cacheKey)) {
                    $room = RoomService::getInstance()->getRoomByRoomNumber($shopId, $roomNumber);
                    if ($room) {
                        $roomId = $room->id;
                        if (!$itemUsable) {
                            if (!$this->roomUsable($shopId, $roomId)) {
                                DebugUtil::log("自动分配房间失败，房间" . $roomNumber . "不可用");
                                continue;
                            }
                        }
                        //如果改订单有包房，则跳过
                        if ($hasSingle) {
                            if (!$inRoom) {
                                DebugUtil::log("自动分配房间失败，包间" . $roomNumber . "不可用");
                                return 0;
                            }
                        } else {
                            if ($countUser >= $bedCount && !$inRoom) {
                                DebugUtil::log("自动分配房间失败，入住满" . $roomNumber . "不可用");
                                return 0;
                            }
                        }
                        $selectRoomId = $roomId;
                        $selectBill = RoomBillService::getInstance()->getTeamBillByRoomId($shopId, $teamId, $selectRoomId, null, null);
                        if ($selectBill) {
                            DebugUtil::log("自动分配房间失败，订单已经分配" . $roomNumber . "不可用");
                            continue;
                        }
                        $setRoom = true;
                        RoomService::getInstance()->updateRoomToClean($roomId);
                        RoomBillUtil::getInstance()->updateRoom($billId, $roomTypeId, $roomId);
                        CacheManager::setCacheValue($cacheKey, $roomNumber, null, null, 20);
                        break;
                    } else {
                        DebugUtil::log($roomNumber . "不存在");
                    }
                }
            }
        }
        if (!$setRoom) {
            throw new MyException(Code::FAIL, "房间正在打扫中,请稍后再试或联系前台");
        }
        RoomHookService::getInstance()->arrangedRoom($billId, 0);
        return $selectRoomId;
    }


    private function saveRoomBillUser($shopIdZong, $phone, $teamId, $billId, $commonCode)
    {
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $userImage = $teamUser->user_image;
        $identificationImage = $teamUser->identification_image;
        $userOrc = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        $accnt = $teamUser->accnt;
        if (!empty($userOrc)) {
            $nationName = $userOrc->nation;
            $nation = NationService::getInstance()->getNationByNationName($nationName);
            $nation_id = 0;
            if (!empty($nation)) {

                $nation_id = $nation->id;
            }
            $identification_number = $userOrc->identification_number;
            $name = $userOrc->name;
            DebugUtil::log("accnt: $name $accnt");
            $gender = $userOrc->gender == "男" ? 1 : 2;
            if ($billId) {
                $bill = RoomBillService::getInstance()->getBillById($billId);
                $billAccnt = $bill->accnt;
                DebugUtil::log("billAccnt: $name $billAccnt");
                $roomTypeId = $bill->room_type_id;
                $tempRoomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $maxUserCount = $tempRoomType->max_user_count;
                $oldUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($billId, $commonCode);
                if (empty($oldUser)) {
                    $exit = RoomBillUserService::getInstance()->getRoomBillMainUser($billId);
                    $userCount = RoomBillUserService::getInstance()->getRoomBillUserCount($billId);
                    if ($userCount >= $maxUserCount) {
                        throw new MyException(Code::FAIL, "房间已经住满，请重试");
                    }
                    if ($exit) {
                        $isMain = 0;
                    } else {
                        $isMain = 1;
                    }
                    $itemUserId = RoomBillUserService::getInstance()->addRoomBillUser($shopIdZong, $billId, $name, $phone, $gender, $isMain, 1, $identification_number,
                        $nation_id, 0, $userImage, $identificationImage, $commonCode, $userOrc->birthday, $userOrc->address, 0, 0, 0);
                    if ($isMain == 1) {
                        if (empty($accnt)) {
                            RoomBillUserService::getInstance()->editRoomBillUser(['id' => $itemUserId, 'accnt' => $billAccnt]);
                        } else {
                            RoomBillUserService::getInstance()->editRoomBillUser(['id' => $itemUserId, 'accnt' => $accnt]);
                        }
                    } else {
                        RoomBillUserService::getInstance()->editRoomBillUser(['id' => $itemUserId, 'accnt' => $accnt]);
                    }
                    DebugUtil::log("saveRoomBillUser 3: $itemUserId");
                    $user = RoomBillUserService::getInstance()->getRoomBillUserById($itemUserId);
                    return $user;
                } else {
                    return $oldUser;
                }
            }
        }
        return null;
    }

    public function getCheckTeamBillUser($teamId, $name)
    {
        $userList = RoomBillUserService::getInstance()->getAllTeamBillUser($teamId);
        foreach ($userList as $item) {
            $itemName = $item->name;
            if ($itemName == $name) {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成团队入住二维码
     * @param $userId
     * @param $teamId
     * @return array|false|\PDOStatement|string|\think\Model
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function getSameRoomCode($userId, $teamId)
    {
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $shopIdZong = $wxUser->shop_id_zong;
        $teamType = $team->team_type;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $teamShare = $teamUser->share;
        $share_limit = $teamUser->share_limit;
        $share_code = $teamUser->share_code;
        //已经分享过code需要被覆盖
        if ($teamShare > 0) {
            if ($share_limit == 0) {
                $share_limit = time() + 60 * 15;
                $share_code = StringUtil::getRandomStrs(6, true, true, true, false);
                TeamRoomUserService::getInstance()->updateShare($teamUser->id, 1, $share_limit, $share_code);
            } else {
                //已分享，若对方已同意，则提示已同意
                $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($teamShare);
                if ($qr) {
                    $oldUsers = TeamRoomUserService::getInstance()->getTeamRoomUserByShareCode($share_code);
                    foreach ($oldUsers as $oldUser) {
                        if ($oldUser->common_code != $commonCode && $oldUser->share != -1) {
                            $userName = $oldUser - name;
                            throw new MyException(Code::FAIL, "$userName 已同意，不能再分享");
                        }
                    }
                }
                TeamRoomQrService::getInstance()->delOldCode($share_code);
                $share_limit = time() + 60 * 15;
                $share_code = StringUtil::getRandomStrs(6, true, true, true, false);
                TeamRoomUserService::getInstance()->updateShare($teamUser->id, 1, $share_limit, $share_code);
            }
        } else {
            $share_limit = time() + 60 * 15;
            $share_code = StringUtil::getRandomStrs(6, true, true, true, false);
            TeamRoomUserService::getInstance()->updateShare($teamUser->id, 1, $share_limit, $share_code);
        }
        return TeamRoomQrService::getInstance()->addReamRoomQr($shopIdZong, $shopId, $teamId, $commonCode, $share_code, $share_limit);

    }

    /**
     * 取消分享
     * @param $userId
     * @param $teamId
     */
    public function cancelShare($userId, $teamId)
    {
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $shopIdZong = $wxUser->shop_id_zong;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUser->id, 'share' => 0]);
        TeamRoomUserService::getInstance()->setOverTime($teamUser->id);
        $qrs = TeamRoomQrService::getInstance()->getTeamRoomQrByCommonCodeFrom($teamId, $commonCode);
        foreach ($qrs as $qr) {
            $tempStatus = $qr->status;
            if ($tempStatus == 1) {
                //0未点击，1已点击，2过期，3被分享人拒绝，4分享人拒绝
                TeamRoomQrService::getInstance()->updateStatus($qr->id, 4);
            }
        }
        $this->setTeamRoomUserSingle($shopId, $teamId, $commonCode);
    }

    /**
     * 更新团队入住人信息
     * @param $userId
     * @param $teamId
     * @param $commonCode
     */
    public function updateTeamUserInfoFromOcr($teamId, $commonCode, $identification_image)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $teamType = $team->team_type;
        $ocr = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        $name = $ocr->name;
        $identification_number = $ocr->identification_number;
        $gender = $ocr->gender == "男" ? 1 : 2;
        $nationName = $ocr->nation;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if ($teamType == 2 && empty($teamUser)) {
            //有名单，先上传身份证，后确认团队
            $teamUser = TeamRoomUserService::getInstance()->getRoomUserListByName($teamId, $name);
            if (empty($teamUser)) {
                throw new MyException(Code::FAIL, "您不在团队中，请联系前台!");
            }
            $teamUserId = $teamUser->id;
            $nation = NationService::getInstance()->getNationByNationName($nationName);
            $nation_id = 1;
            if (!empty($nation)) {
                $nation_id = $nation->id;
            }
            TeamRoomUserService::getInstance()->updateTeamUserInfo($teamUserId, null, $name, null,
                1, $identification_number, $gender, $nation_id, null, $commonCode, null, $identification_image);
        }
    }

    /**
     * 有名单团队，判断入住人姓名是否在名单中
     * @param $commonCode
     * @param $teamId
     * @param $name
     * @return void
     * @throws MyException
     */
    public function checkUserInTeam($commonCode, $teamId, $name)
    {
        DebugUtil::log("checkUserInTeam: $commonCode, $teamId, $name");
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        //team_type 1无名单，2有名单，3旅游团
        $teamType = $team->team_type;
        if ($teamType == 2) {
            $teamUser = TeamRoomUserService::getInstance()->getRoomUserByName($teamId, $name, null);
            if (empty($teamUser)) {
//                $tempUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
//                if (!$tempUser) {
//                }
                throw new MyException(Code::FAIL, "您不在团队中，请联系前台");
            }
        }
    }

    /**
     * 重新选房后删除旧数据
     * @param $teamId
     * @param $commonCode
     */
    public function delOldRoomUserInfo($teamId, $commonCode)
    {
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $oldBillId = $teamUser->bill_id;
        $oldRoomUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($oldBillId, $commonCode);
        if ($oldRoomUser) {
            RoomBillUserService::getInstance()->delRoomBillUser($oldRoomUser->id);
        }
        RoomBillPriceDateUserService::getInstance()->delRoomBillPriceDateUserByBillIdAndCommonCode($oldBillId, $commonCode);
    }


    /**
     * 查询团队支付金额
     * @param $teamId
     * @param $userId
     * @return array
     */
    public function getPayAmount($teamId, $userId)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        DebugUtil::log(json_encode($team));
        DebugUtil::log("teamId: $teamId");
        $pay_room_price = $team->pay_room_price;
        $shopId = $team->shop_id;
        $wxUser = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $wxUser->common_code;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (empty($teamUser)) {
            $data = [
                'room_price' => [],
                'cash_pledge' => 0,
                'already_pay_amount' => 0,
                'total_amount' => 0,
            ];
            return $data;
        }
        $roomPrice = $teamUser->room_price;
        $cashPledge = $teamUser->cash_pledge;
        $prices = $teamUser->prices;
        $setting = ShopSysSettingService::getInstance()->getSysSettingBySignFromCache($shopId, ShopSysSetting::SELF_CHECK_IN_PAY_CASH_PLEDGE);
        $status = $setting->property['status'];
        $totalAmount = 0;
        DebugUtil::log("pay_room_price: $pay_room_price");
        if ($pay_room_price == 1) {
            //收房费
            $totalAmount += $roomPrice;
        }
        if ($status != 0) {
            $cashPledge = 0;
        }
        $totalAmount += $cashPledge;
        $alreadyPayAmount = TeamFundService::getInstance()->getTeamFundAmountByCommonCode($teamId, $commonCode, 1);
        $totalAmount -= $alreadyPayAmount;
        $data = [
            'room_price' => $prices,
            'cash_pledge' => $cashPledge,
            'already_pay_amount' => $alreadyPayAmount,
            'total_amount' => $totalAmount,
        ];
        return $data;
    }


    /**
     * @param $userId
     * @param $teamId
     * @param $file
     * @throws MyException
     */
    public function faceContrast($userId, $teamId, $file)
    {
        $payAmountData = $this->getPayAmount($teamId, $userId);
        if ($payAmountData['total_amount'] > 0) {
            throw new MyException(Code::FAIL, "请先支付");
        }
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        $shopIdZong = $user->shop_id_zong;
        $fileRes = ResourceService::getInstance()->uploadFile($file);
        $path = $fileRes['path'];
        $url = $fileRes['url'];
        $key = "faceContrast_$teamId _$userId";
        if (CacheManager::has($key) && CacheManager::getCacheValue($key)) {
            $res = true;
        } else {
            $res = UserUtil::getInstance()->faceContrast($userId, $shopId, $path);
        }
        if ($res) {
            CacheManager::setCacheValue($key, $res, null, null, 60);
            $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
            $userOrc = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
            $identification_image = $userOrc->id_image;
            $ocrGender = $userOrc->gender;
            if ($ocrGender == "男") {
                $gender = 1;
            } else if ($ocrGender == "女") {
                $gender = 2;
            } else {
                $gender = 0;
            }
            TeamRoomUserService::getInstance()->updateTeamUser(['id' => $teamUser->id, 'user_image' => $url,
                'authentication' => 1, 'identification_image' => $identification_image, 'gender' => $gender]);
//            $this->getSameRoomCode($userId, $teamId);
        } else {
            throw new MyException(Code::FAIL, "人脸对比失败");
        }
    }

    //{"url":"https://parkbay.bj-jtjt.cn/ke_access/derate","merchId":"110105300292","signKey":"524666714cd641e4b0ade6c009c867a7","time":"120"}

    public function teamCheckIn($teamId, $userId)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        $canCheckIn = $this->checkCheckInTime($teamId, $commonCode);
        if (!$canCheckIn) {
            throw new MyException(Code::FAIL, "不在入住时间段内");
        }
        $commonUser = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
        $phone = $commonUser->phone;
        $shopIdZong = $user->shop_id_zong;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $userOrc = UserOcrService::getInstance()->getUserOcrByCommonCodeFromCache($commonCode);
        $identification_image = $userOrc->id_image;
        $identification_number = $userOrc->identification_number;
        $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCommonCode($teamId, $commonCode);
        $share = $teamUser->share;
        $teamBillId = $teamUser->bill_id;
        $teamShareCode = $teamUser->share_code;
        $confirm = $teamUser->confirm;
        $url = $teamUser->user_image;
        $authentication = $teamUser->authentication;
        if ($authentication != 1) {
            throw new MyException(Code::FAIL, "请先完成人脸识别");
        }
        $tempUserSelectRoom = false;
        DebugUtil::log("checkIn teamBillId: $teamBillId");
        if (!$teamBillId) {
            //当前用户没有分配房间
            //0未点击，1已点击，2过期，3被分享人拒绝，4分享人拒绝
            if ($qr && $qr->status == 1) {
                if ($confirm != 2) {
                    DebugUtil::log(json_encode($qr));
                    throw new MyException(Code::FAIL, "请等待分享人确认!");
                }
                $arCommonCodeFrom = $qr->common_code_from;
                $teamUserFrom = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $arCommonCodeFrom);
                $teamBillId = $teamUserFrom->bill_id;
                //分享人分配了房间
            }
        } else {
            $tempUserSelectRoom = true;
        }
        //已经分配或者指定了房间
        if ($teamBillId) {
            $cleanStatus = $this->getBillRoomCleanStatus($teamBillId);
            if ($cleanStatus != 1) {
                throw new MyException(Code::FAIL, "当前房间正在清洁中，请稍后再试");
            }
            $roomUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($teamBillId, $commonCode);
            if (empty($roomUser)) {
                $this->saveRoomBillUser($shopIdZong, $phone, $teamId, $teamBillId, $commonCode);
                $roomUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($teamBillId, $commonCode);
            } else {
                RoomBillUserService::getInstance()->editRoomBillUser(['id' => $roomUser->id, 'user_image' => $url,
                    'authentication' => 1, 'identification_image' => $identification_image, "identification_number" => $identification_number]);
            }
            if (!$tempUserSelectRoom) {
                TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUser->id, 'bill_id' => $teamBillId]);
            }
            if ($share == 0) {
                //排房入住
                $this->toStayRoomBill($teamId, $shopId, $teamBillId, $commonCode, $roomUser->id);
            } elseif ($share == 1) {
                $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($teamShareCode);
                if (empty($qr)) {
//                        DebugUtil::log("face: 7");
                } else {
                    $qrFromCommonCode = $qr->common_code_from;
                    $fromRoomUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($teamBillId, $qrFromCommonCode);
                    if ($fromRoomUser) {
                        $this->toStayRoomBill($teamId, $shopId, $teamBillId, $commonCode, $roomUser->id);
                    }
                }
            }
        } else {
            DebugUtil::log("step 3");
            //0未点击，1已点击，2过期，3被分享人拒绝，4分享人拒绝
            DebugUtil::log("$shopId, $teamId, $commonCode");
            $this->setTeamRoomUserSingle($shopId, $teamId, $commonCode);

//            if ((empty($qr) || $qr->status != 1) && (empty($fromQr)) && $share == 0) {
//                //单人团队入住
//                $this->setTeamRoomUserSingle($shopId, $teamId, $commonCode);
//            } else {
//                if ($qr && $qr->status == 1) {
//                    if ($confirm != 2) {
//                        throw new MyException(Code::FAIL, "请等待分享人确认");
//                    }
//                }
//            }
        }
    }


    /**
     * 更换房间并入住
     * @param $userId
     * @param $teamId
     * @return void
     * @throws MyException
     */
    public function changeRoomCheckIn($userId, $teamId)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomById($teamId);
        $is_dirty_room_change = $team->is_dirty_room_change;
        if ($is_dirty_room_change != 1) {
            throw new MyException(Code::FAIL, "当前团队不允许更换房间");
        }
        $shopId = $team->shop_id;
        $user = UserWxService::getInstance()->getUserById($userId);
        $shopIdZong = $user->shop_id_zong;
        $commonCode = $user->common_code;
        $userCommon = UserCommonService::getInstance()->getUserCommonByCommonCodeFromCache($commonCode);
        $phone = $userCommon->phone;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $teamUserId = $teamUser->id;
        $oldUserBillId = $teamUser->bill_id;
        $tempUserAccnt = $teamUser->accnt;
        if ($oldUserBillId) {
            $oldBill = RoomBillService::getInstance()->getBillByIdFromCache($oldUserBillId);
            $roomTypeId = $oldBill->room_type_id;
        } else {
            $allRoomBillList = RoomBillService::getInstance()->getTeamBill($shopId, null, $teamId);
            $roomTypeId = $allRoomBillList[0]->room_type_id;
        }
        TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $teamUserId, 'bill_id' => 0]);
        $teamBillId = $this->selectBillIdNew($shopId, $teamId, $commonCode, 0);
        if (!$teamBillId) {
            $teamBillId = $this->getSelectedBillId($shopId, $teamId, $oldUserBillId, $roomTypeId);
            //没有匹配到空房
        }
        if (!$teamBillId) {
            throw new MyException(Code::FAIL, "房间已满、请联系前台！");
        }
        DebugUtil::log("teamBillId: $teamBillId");
        //将还未如入住的团队成员的billId替换成脏房的bill_id
        $changeBillList = TeamRoomUserService::getInstance()->getRoomUserByBillId($teamBillId);
        $changeAccntList = [];
        $oldAccntList = [];
        if ($tempUserAccnt) {
            $oldAccntList[] = $tempUserAccnt;
        }
        $updateBillUserList = [];
        foreach ($changeBillList as $changeBillUser) {
            if ($changeBillUser->accnt && !in_array($changeBillUser->accnt, $changeAccntList)) {
                $changeAccntList[] = $changeBillUser->accnt;
            }
        }
        $tempUserData = ['id' => $teamUserId, 'bill_id' => $teamBillId];
        if ($changeAccntList) {
            $tempUserData['accnt'] = $changeAccntList[0];
            array_shift($changeAccntList);
        }
        if ($oldUserBillId) {

            $oldBillList = TeamRoomUserService::getInstance()->getRoomUserByBillId($oldUserBillId);
            foreach ($oldBillList as $oldIndex => $changeBillUser) {
                $tempUserId = $changeBillUser->id;
                $tempBillId = $changeBillUser->bill_id;
                $tempCommonCode = $changeBillUser->common_code;
                $itemAccnt = $changeBillUser->accnt;
                if ($itemAccnt && !in_array($itemAccnt, $oldAccntList)) {
                    $oldAccntList[] = $itemAccnt;
                }
                $tempUser = RoomBillUserService::getInstance()->getRoomBillUserByCommonCode($tempBillId, $tempCommonCode);
                if ($tempUser && $tempUser->user_status >= 1) {
                    continue;
                }
                $tempChangeData = [
                    'id' => $tempUserId,
                    'bill_id' => $teamBillId,
                ];
                if (count($oldBillList) <= count($changeAccntList)) {
                    $tempChangeData['accnt'] = $changeAccntList[$oldIndex];
                }
                TeamRoomUserService::getInstance()->editTeamRoomUser($tempChangeData);
            }
        }
        TeamRoomUserService::getInstance()->editTeamRoomUser($tempUserData);
        //改变脏房的billId,替换成干净房的bill_id
        foreach ($changeBillList as $changeIndex => $changeBillUser) {
            $tempUserId = $changeBillUser->id;
            $tempBillId = $changeBillUser->bill_id;
            $tempChangeData = [
                'id' => $tempUserId,
                'bill_id' => $oldUserBillId,
            ];
            if (count($changeBillList) <= count($oldAccntList)) {
                $tempChangeData['accnt'] = $oldAccntList[$changeIndex];
            }
            TeamRoomUserService::getInstance()->editTeamRoomUser($tempChangeData);
        }
        $updateBillUserList[] = ['bill_id' => $teamBillId];
        if ($changeBillList) {
            $updateBillUserList[] = ['bill_id' => $oldUserBillId];
        }
        $changeRoomUserBillKey = "changeRoomUserBillKey_" . $commonCode;
        DebugUtil::log("changeRoomUserBillKey1:$changeRoomUserBillKey");
        CacheManager::setCacheValue($changeRoomUserBillKey, $updateBillUserList, null, null, 30);
        $roomBillUser = $this->saveRoomBillUser($shopIdZong, $phone, $teamId, $teamBillId, $commonCode);
        $roomBillUserId = $roomBillUser->id;
        $this->toStayRoomBill($teamId, $shopId, $teamBillId, $commonCode, $roomBillUserId);
    }

    /**
     * 选择占用的房间
     * @param $shopId
     * @param $teamId
     * @param $commonCode
     * @return int
     * @throws MyException
     */
    private function selectBillIdNew($shopId, $teamId, $commonCode, $selectUserCount)
    {
        //已经分配了房间
        $tempTeamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        if (!empty($tempTeamUser)) {
            //已经分配过
            $oldBillId = $tempTeamUser->bill_id;
            if ($oldBillId) {
                return $oldBillId;
            }
        }
        $single = $tempTeamUser->single;
        //接受了他人分享
        $selectBillId = $this->getShareRoomBillId($teamId, $commonCode);
        if ($selectBillId) {
            return $selectBillId;
        }
        //无房间自动重试5次
        $selectBillId = 0;
        $times = 5;
        while ($times > 0) {
            $selectBillId = TeamSelectRoomUtil::getInstance()->autoSelectBillNew($shopId, $teamId, $commonCode, $selectUserCount, $single);
            $oldSelectQr = TeamRoomSelectQrService::getInstance()->getTeamRoomSelectQrByBillId($teamId, $selectBillId);
            if ($selectBillId == 0 || !empty($oldSelectQr)) {
                usleep(rand(10000, 200000));
                $times--;
            } else {
                //分配成功
                break;
            }
        }
        DebugUtil::log("selectBillId1: $selectBillId");
        if (!$selectBillId) {
            $user = UserOcrService::getInstance()->getUserOcrByCommonCode($commonCode);
            $userName = $user->name;
            $teamBills = RoomBillService::getInstance()->getTeamBill($shopId, [RoomBillStatus::TO_BE_STAY], $teamId);
            DebugUtil::log(count($teamBills));
            foreach ($teamBills as $teamBill) {
                $tempBillAccnt = $teamBill->accnt;
                $itemRoomBillId = $teamBill->id;
                $roomId = $teamBill->room_id;
                DebugUtil::log("roomId: $roomId");
                $enterTimeDate = date("Y-m-d", $teamBill->enter_time_plan);
                if ($enterTimeDate != date("Y-m-d")) {
                    DebugUtil::log("continue: 1");
                    //只允许入住当天的
                    continue;
                }
                if ($roomId == 0) {
                    $cacheRoomIdKey2 = "room_id_key $teamId, $itemRoomBillId";
                    if (CacheManager::has($cacheRoomIdKey2)) {
                        DebugUtil::log("cache bill");
                        continue;
                    }
                    CacheManager::setCacheValue($cacheRoomIdKey2, $itemRoomBillId, null, null, 7);
                    $returnBillRoomId = $this->autoSetRoomBillRoom($teamId, $itemRoomBillId, $userName);
                    DebugUtil::log("autoSetRoomBillRoom: $returnBillRoomId");
                    if ($returnBillRoomId) {
                        $selectBillId = $itemRoomBillId;
                        break;
                    }
                }
            }
            DebugUtil::log("autoSetRoomBillRoom2: $selectBillId");
//            if (!$selectBillId) {
//                $errorMsg = "房间已满，请重试";
//                if (count($teamBills) > 0) {
//                    $errorMsg = "房间正在打扫中,请稍后再试或联系前台!";
//                }
//                throw new MyException(Code::FAIL, $errorMsg);
//            }
        }
        return $selectBillId;
    }

    //获取已选择的房间
    public function getSelectedBillId($shopId, $teamId, $oldBillId, $roomTypeId)
    {
        $bills = RoomBillService::getInstance()->getTeamBill($shopId, [RoomBillStatus::TO_BE_STAY], $teamId);
        $prevBillId = 0;
        foreach ($bills as $bill) {
            $itemBillId = $bill->id;
            if ($itemBillId == $oldBillId) {
                continue;
            }
            $teamUsers = TeamRoomUserService::getInstance()->getTeamRoomUserBillList($teamId, null, $roomTypeId,
                RoomBillStatus::TO_BE_STAY, null, null);
            $roomId = $bill->room_id;
            if (!$roomId) {
                continue;
            }
            $this->flashRoomStatusByRoomId($shopId, $roomId);
            $notEmptyCommonCode = false;
            foreach ($teamUsers as $teamUser) {
                $tempCommonCode = $teamUser->common_code;
                if ($tempCommonCode) {
                    $notEmptyCommonCode = true;
                    break;
                }
            }
            if (!$notEmptyCommonCode) {
                return $itemBillId;
            } else {
                $prevBillId = $itemBillId;
            }
        }
        return $prevBillId;
    }


    public function getRoomCleanStatus($shopId, $roomNumber)
    {
        $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
        $sign = $thirdSystem->sign;
        $thirdSystemId = $thirdSystem->id;
        if ($sign == ThirdSystem::JINGFEI) {
            return 1;
        }
        $api = ThirdSystemApiService::getInstance()->getThirdSystemApiBySign($thirdSystemId, ThirdSystemApi::QUERY_ROOM_CLEAN_STATUS);
        $key = $api->key;
        $apiStatus = $api->status;
        if ($apiStatus == 0) {
            return 1;
        }
        $url = $api->api_url;
        $requestType = $api->request_type;
        $startTime = microtime(true);
        $cleanStatus = 0;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $hotelCode = $shop->hotel_code;
        $params = [];
        $params['key'] = $key;
        $params['hotel_code'] = $hotelCode;
        $params['room_number'] = $roomNumber;
        $params['timestamp'] = time();
        $res = RequestUtil::getInstance()->requestData($url, $requestType, $params, true);
        DebugUtil::log($res);
        $result = json_decode($res, true);
        if ($result['status'] == 0) {
            $cleanStatus = $result['data'];
        }
        DebugUtil::log("$roomNumber : $cleanStatus");
        $endTime = microtime(true);
        DebugUtil::log("getRoomCleanStatusTimes: " . ($endTime - $startTime));
        return $cleanStatus;
    }

    public function flashRoomStatus($shopId, $roomId, $roomNumber)
    {
        $cacheKey = "api_room_status__" . $shopId . "_" . $roomId;
        if (CacheManager::has($cacheKey)) {
            DebugUtil::log("getRoomCleanStatus: $cacheKey");
            DebugUtil::log(CacheManager::getCacheValue($cacheKey));
            return CacheManager::getCacheValue($cacheKey);
        }
        $cleanStatus = $this->getRoomCleanStatus($shopId, $roomNumber);
        CacheManager::setCacheValue($cacheKey, $cleanStatus, null, null, 300);
        return $cleanStatus;
    }

    public function flashRoomStatusByRoomId($shopId, $roomId)
    {
        $cacheKey = "api_room_status__" . $shopId . "_" . $roomId;
        if (CacheManager::has($cacheKey)) {
            DebugUtil::log("getRoomCleanStatus: $cacheKey");
            DebugUtil::log(CacheManager::getCacheValue($cacheKey));
            return CacheManager::getCacheValue($cacheKey);
        }
        $room = RoomService::getInstance()->getRoomByIdFromCache($roomId);
        $roomNumber = $room->room_number;
        $cleanStatus = $this->getRoomCleanStatus($shopId, $roomNumber);
        CacheManager::setCacheValue($cacheKey, $cleanStatus, null, null, 300);
        return $cleanStatus;
    }


    public function getBillRoomCleanStatus($billId)
    {
        if (!$billId) {
            return 0;
        }
        $roomBill = RoomBillService::getInstance()->getBillByIdFromCache($billId);
        $shopId = $roomBill->shop_id;
        $roomId = $roomBill->room_id;
        if (!$roomId) {
            return 0;
        }
        $room = RoomService::getInstance()->getRoomByIdFromCache($roomId);
        if (empty($room)) {
            return 0;
        }
        $roomNumber = $room->room_number;
        return $this->flashRoomStatus($shopId, $roomId, $roomNumber);
    }

    public function toStayRoomBill($teamId, $shopId, $selectBillId, $commonCode, $roomBillUserId)
    {
        $teamFund = TeamFundService::getInstance()->getTeamFundByCommonCode($teamId, $commonCode, 1);
        $bill = RoomBillService::getInstance()->getBillByIdFromCache($selectBillId);
        $roomId = $bill->room_id;
        DebugUtil::log("toStayRoomBill roomId: $roomId");
        if (empty($roomId)) {
            //未排房
            $userOcr = UserOcrService::getInstance()->getUserOcrByCommonCode($commonCode);
            $userName = $userOcr->name;
            //自动排房
            $roomId = $this->autoSetRoomBillRoom($teamId, $selectBillId, $userName);
            DebugUtil::log("autoSetRoomBillRoomBillId: $roomId");
            if ($roomId == 0) {
                throw new MyException(Code::FAIL, "房间正在打扫中、请稍后再试或联系前台。");
            }
        }
        if (!$this->roomUsable($shopId, $roomId)) {
            throw new MyException(Code::FAIL, "房间正在打扫中、请稍后再试或联系前台！");
        }
        if (!empty($teamFund)) {
            $room_amount = $teamFund->room_amount;
            $cash_pledge = $teamFund->cash_pledge;
            $accountId = $teamFund->account_id;
            $user_coupon_id = $teamFund->coupon_id;
            $teamId = $teamFund->team_id;
            $outTradeNo = $teamFund->out_trade_no;
            $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::BOOK_ROOM);
            $room_bill_type_id = $detailType->id;
            $roomDetail = RoomBillDetailService::getInstance()->getFundByCommonCode($selectBillId, $commonCode, $room_bill_type_id, 2, 1);
            if (empty($roomDetail)) {
                //更新订单财务
                $remark = "团队入住";
                $memo = "团队入住";
                $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeBySign($shopId, RoomBillDetailType::BOOK_ROOM);
                $room_bill_type_id = $detailType->id;
                RoomBillDetailService::getInstance()->addRoomBillDetail($selectBillId, $roomId, $room_amount, 2, 1, $remark, 0, $accountId,
                    $room_bill_type_id, $user_coupon_id, 1, 0, $memo, 0, 0, $outTradeNo, $commonCode);
                if ($user_coupon_id) {
                    $couponRecord = CouponRecordService::getInstance()->getCouponRecordById($user_coupon_id);
                    $coupon_id = $couponRecord->coupon_id;
                    RoomBillDetailService::getInstance()->addRoomBillDetail($selectBillId, $roomId, $cash_pledge, 2, 1, $remark, 0, $accountId,
                        $room_bill_type_id, $coupon_id, 1, 0, $memo, 0, 0, null, $commonCode);
                }
            }
        }
        RoomBillUserService::getInstance()->editRoomBillUser(['id' => $roomBillUserId, 'authentication' => 1]);
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        $teamUserId = $teamUser->id;
        TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 0);
        $canCheckIn = $this->checkCheckInTime($teamId, $commonCode);
        if ($canCheckIn) {
            $this->billToStaying($selectBillId, $commonCode);
        }
    }


    /**
     * 检查团队 分享他人同住超时
     * @param $shopId
     */
    public function checkOverTime($shopId)
    {
        $list = TeamRoomUserService::getInstance()->getOverTimeUser($shopId);
        foreach ($list as $item) {
            $itemId = $item->id;
            $teamId = $item->team_id;
            $commonCode = $item->common_code;
            $shareCode = $item->share_code;
            $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
            if (!empty($qr)) {
                //0未点击，1已点击，2过期，3被分享人拒绝，4分享人拒绝
                if ($qr->status == 0 || $qr->status == 1) {
                    //过期则让出房间，有限安排其他人
                    TeamRoomQrService::getInstance()->updateStatus($qr->id, 2);
                }
                TeamRoomUserService::getInstance()->setOverTime($itemId);
            }
        }
    }

    /**
     * 分享二维码的人，二次确认同意接受人同住
     * @param $teamId
     * @param $userId
     * @param $confirm 0拒绝 2同意
     * @throws MyException
     */
    public function confirmSame($teamId, $userId, $confirm)
    {
        DebugUtil::log("teamId: $teamId");
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        $shopId = $team->shop_id;
        $user = UserWxService::getInstance()->getUserById($userId);
        $commonCode = $user->common_code;
        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
        DebugUtil::log(json_encode($teamUser));
        $shareCode = $teamUser->share_code;
        if (empty($shareCode)) {
            throw new MyException(Code::FAIL, "邀请已过期，请重新分享");
        }
        $users = TeamRoomUserService::getInstance()->getTeamRoomUserListByShareCode($shareCode);
//        DebugUtil::log(json_encode($users));
        DebugUtil::log("confirmSame: 1");
        if ($confirm == 2) {
            DebugUtil::log("confirmSame: 2");
            foreach ($users as $itemUser) {
                $tempUserCommonCode = $itemUser->common_code;
                $itemUserId = $itemUser->id;
                if ($tempUserCommonCode != $commonCode) {
                    DebugUtil::log("$commonCode 二次确认同意入住");
                    $itemUserName = $itemUser->name;
                    $itemConfirm = $itemUser->confirm;
                    if ($itemConfirm == 0) {
                        throw new MyException(Code::FAIL, "$itemUserName 未同意同住!");
                    }
                    TeamRoomUserService::getInstance()->updateTeamUserConfirm($itemUserId, $confirm);
                }
            }
//            $this->setTeamUserRoom($shopId, $teamId, $shareCode, $commonCode);
        } else {
            foreach ($users as $itemUser) {
                DebugUtil::log("confirmSame: 1");
                $tempUserCommonCode = $itemUser->common_code;
                $itemUserId = $itemUser->id;
                DebugUtil::log("$commonCode 二次确认拒绝入住");
                //不同意清除
                $qr = TeamRoomQrService::getInstance()->getTeamRoomQrByCode($shareCode);
                if ($tempUserCommonCode != $commonCode) {
                    TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $itemUserId, 'confirm' => 0, 'share' => 2, 'share_code' => null, 'bill_id' => null]);
                } else {
                    TeamRoomUserService::getInstance()->editTeamRoomUser(['id' => $itemUserId, 'share_code' => null]);
                }
                if ($qr) {
                    TeamRoomQrService::getInstance()->editTeamRoomQr(['id' => $qr->id, 'status' => 4]);
                }
            }
        }
    }

    public function getTeamPrice($teamId)
    {
        $billList = RoomBillService::getInstance()->getBillListByTeamId($teamId);
        $bill = $billList[0];
        $roomTypeId = $bill->room_type_id;
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $maxUserCount = $roomType->max_user_count;
        $billId = $bill->id;
        $enterTimePlan = $bill->enter_time_plan;
        $priceList = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateSimpleList($billId, null);
        $prices = [];
        $totalPrice = 0;
        $cashPledge = 0;
        $dateList = [];
        foreach ($priceList as $itemPrice) {
            $date = $itemPrice->date;
            if (in_array($date, $dateList)) {
                continue;
            }
            $tempPrice = floatval(sprintf("%.2f", $itemPrice->room_price / $maxUserCount));
            $totalPrice += $tempPrice;
            $cashPledge = floatval(sprintf("%.2f", $bill->cash_pledge / $maxUserCount));
            $prices[] = ['date' => $itemPrice->date,
                'room_price' => $tempPrice,
                'cash_pledge' => $cashPledge];
        }
        $roomPrice = $totalPrice;
        $cash_pledge = $cashPledge;
        return array($prices, $roomPrice, $cash_pledge);
    }

    public function teamCheckInNotify($requestId, $code)
    {
        DebugUtil::log("teamCheckInNotify -1 $requestId");
        $hookResult = HookResultService::getInstance()->getHookResultByRequestId($requestId);
//        DebugUtil::log(json_encode($hookResult));
        $billData = $hookResult->param;
        $users = $billData['users'];
        $billId = $billData['id'];
        $shopId = $billData['shop_id'];
        $cacheKey = $billData['cache_key'];
        $teamId = $billData['team_id'];
        $isMain = 0;
        if ($code == 0) {
            //成功，订单转入住，添加入住人
            DebugUtil::log("cacheKey: $cacheKey");
            if ($teamId == 0) {
                return;
            }
            $currentCommonCode = null;
            foreach ($users as $itemUser) {
                $itemUserId = $itemUser['id'];
                $tempIsMain = isset($itemUser['is_main']) ? $itemUser['is_main'] : 0;
                $team_confirm = isset($itemUser['team_confirm']) ? $itemUser['team_confirm'] : 0;
                if ($team_confirm == 0) {
                    $commonCode = $itemUser['common_code'];
                    $tempCacheKey = md5($commonCode);
                    DebugUtil::log("tempCacheKey: $tempCacheKey");
                    if ($tempCacheKey == $cacheKey) {
                        $currentCommonCode = $commonCode;
                        DebugUtil::log("teamCheckInNotify 3_2 $commonCode");
                        RoomBillUserService::getInstance()->editRoomBillUser(['id' => $itemUserId, 'user_status' => 1]);
                        $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
                        $teamUserName = $teamUser->name;
                        $teamUserId = $teamUser->id;
                        $isMain = $tempIsMain;
                        TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 1);
                        RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "团队入住 $teamUserName", '入住', RoomLogType::STAY, $billId, $shopId);
                    }
                }
            }
            DebugUtil::log("teamCheckInNotify 4");
            $enterTimePlan = $billData['enter_time_plan'];
            $leaveTimePlan = $billData['leave_time_plan'];
            $hookBillStatus = $billData['bill_status'];
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $billStatus = $bill->bill_status;
            $roomId = $bill->room_id;
            $room = RoomService::getInstance()->getRoomByIdFromCache($roomId);
            $roomTypeId = $room->room_type_id;
            DebugUtil::log("teamCheckInNotify 5");
            if ($billStatus == RoomBillStatus::TO_BE_STAY && $isMain == 1) {
                DebugUtil::log("teamCheckInNotify 6");
                //更新房态
                RoomStatusRecordService::getInstance()->checkRoom($shopId, $billId, $roomTypeId, $roomId, $enterTimePlan, $leaveTimePlan, true);
                RoomStatusRecordService::getInstance()->bookToStay($shopId, $billId, $roomId, $roomId, $enterTimePlan, $leaveTimePlan, RoomRecordStatus::STAYING);
                //房价日期
                //更新订单状态
                RoomBillUtil::getInstance()->bookToStay($billId, $roomTypeId, $roomId);
            }
        } else {
            $currentCommonCode = null;
            foreach ($users as $itemUser) {
                $team_confirm = isset($itemUser['team_confirm']) ? $itemUser['team_confirm'] : 0;
                $itemUserId = $itemUser['id'];
                $commonCode = $itemUser['common_code'];
                $tempCacheKey = md5($commonCode);
                if ($tempCacheKey == $cacheKey) {
                    $currentCommonCode = $commonCode;
                    $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
                    $teamUserId = $teamUser->id;
                    TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 2);
                }
            }


            $selectBillIdKey = self::$SELECT_USER_KEY . $billId;
            if (CacheManager::has($selectBillIdKey)) {
                $selectUsers = CacheManager::getCacheValue($selectBillIdKey);
                $selectCount = count($selectUsers);
                DebugUtil::log("$selectBillIdKey: $selectCount");
                if ($selectCount > 0) {
                    foreach ($selectUsers as $itemIndex => $selectUser) {
                        $tempCommonCode = $selectUser['common_code'];
                        if ($tempCommonCode == $currentCommonCode) {
                            unset($selectUsers[$itemIndex]);
                            break;
                        }
                    }
                    CacheManager::setCacheValue($selectBillIdKey, $selectUsers, null, null, 5);
                }
            }
        }
    }

    public function notifyError($requestId)
    {
        $hookResult = HookResultService::getInstance()->getHookResultByRequestId($requestId);
        $billData = $hookResult->param;
        $users = $billData['users'];
        $cacheKey = $billData['cache_key'];
        $teamId = $billData['team_id'];
        foreach ($users as $itemUser) {
            $team_confirm = isset($itemUser['team_confirm']) ? $itemUser['team_confirm'] : 0;
            if ($team_confirm == 0) {
                $commonCode = $itemUser['common_code'];
                $tempCacheKey = md5($commonCode);
                DebugUtil::log("tempCacheKey: $tempCacheKey");
                if ($tempCacheKey == $cacheKey) {
                    $currentCommonCode = $commonCode;
                    DebugUtil::log("teamCheckInNotify 3_2 $commonCode");
                    $teamUser = TeamRoomUserService::getInstance()->getRoomUserByCommonCode($teamId, $commonCode);
                    $teamUserId = $teamUser->id;
                    TeamRoomUserService::getInstance()->updateTeamRoomUserConTeamConfirm($teamUserId, 2);
                }
            }
        }
    }

    public function apiGetTeamInfo($teamId)
    {
        $team = TeamRoomService::getInstance()->getTeamRoomByIdFromCache($teamId);
        if (empty($team)) {
            throw new MyException(Code::FAIL, '团队不存在');
        }
        $users = TeamRoomUserService::getInstance()->getList($teamId, null, null, null);
        $team->users = $users;
        return $team;
    }


    /**
     * 团队指定二维码
     * @param $shopIdZong
     * @param $shopId
     * @param $teamCode
     * @param $roomList
     * @return array|bool|\PDOStatement|string|\think\Collection
     * @throws MyException
     * @throws \OSS\Core\OssException
     */
    public function createTeamQr($shopIdZong, $shopId, $teamCode, $roomList)
    {
        $newList = [];
        $team = TeamRoomService::getInstance()->getTeamByTeamCode($shopId, $teamCode);
        $teamId = $team->id;
        foreach ($roomList as $roomNumber) {
            $room = RoomService::getInstance()->getRoomByRoomNumber($shopId, $roomNumber);
            $roomId = $room->id;
            $itemRoomNumber = $room->room_number;
            if (in_array($itemRoomNumber, $roomList)) {
                $bill = RoomBillService::getInstance()->getRoomBillByTeamIdAndRoomId($teamId, $roomId);
                $billId = $bill->id;
                $oldQr = TeamRoomSelectQrService::getInstance()->getTeamRoomSelectQrByBillId($teamId, $billId);
                if (empty($oldQr)) {
                    $url = WxQrUtil::getInstance()->createTeamRoomIdQr($shopIdZong, $shopId, $teamId, $billId);
                    TeamRoomSelectQrService::getInstance()->addTeamRoomSelectQr($teamId, $billId, $url);
                }
            }
        }
        $list = TeamRoomSelectQrService::getInstance()->getTeamRoomSelectQrList($teamId, null, 1, null, null);
        foreach ($list as $item) {
            $roomNumter = $item->room_number;
            if (in_array($roomNumter, $roomList)) {
                $newList[] = $item;
            }
        }
        return $newList;
    }

    /**
     * 检查房间是否可用
     * @param $shopId
     * @param $roomId
     * @return bool
     */
    private function roomUsable($shopId, $roomId): bool
    {
        $cacheKey = "api_room_status__" . $shopId . "_" . $roomId;
        if (!CacheManager::has($cacheKey)) {
            DebugUtil::log("roomUsable: 1");
            return true;
        }
        if (CacheManager::getCacheValue($cacheKey) == 1) {
            DebugUtil::log("roomUsable: 2");
            return true;
        }
        return false;
    }


}