<?php


namespace app\common\service\hook;


use app\common\returnConfig\Code;
use app\common\service\active\ActiveService;
use app\common\service\distribution\DistributionBillService;
use app\common\service\finance\SysAccountService;
use app\common\service\hardware\baidu\BdDeviceService;
use app\common\service\hardware\LockService;
use app\common\service\intermediary\IntermediaryCommissionRecordService;
use app\common\service\intermediary\IntermediaryRoomPriceServiceService;
use app\common\service\message\MessageUtil;
use app\common\service\room\BreakfastCouponService;
use app\common\service\room\RoomBillCarService;
use app\common\service\room\RoomBillConnectService;
use app\common\service\room\RoomBillDetailService;
use app\common\service\room\RoomBillDetailTypeService;
use app\common\service\room\RoomBillLogService;
use app\common\service\room\RoomBillPriceDateService;
use app\common\service\room\RoomBillQrService;
use app\common\service\room\RoomBillService;
use app\common\service\room\RoomBillUserService;
use app\common\service\room\RoomBillUtil;
use app\common\service\room\RoomPriceServiceService;
use app\common\service\room\RoomSaleTypeService;
use app\common\service\room\RoomService;
use app\common\service\room\RoomTypeService;
use app\common\service\shop\ShopService;
use app\common\service\sms\AlismsService;
use app\common\service\sms\AlismsTemplateService;
use app\common\service\sysSetting\BillSourceService;
use app\common\service\user\MemberAndRightInterestService;
use app\common\service\user\MemberUtilService;
use app\common\service\user\UserCommonService;
use app\common\service\user\UserWxService;
use app\common\sysConfig\MyException;
use app\common\utils\CacheManager;
use app\common\utils\dataDictionary\ActiveCondition;
use app\common\utils\dataDictionary\BdDeviceType;
use app\common\utils\dataDictionary\BdSoundUse;
use app\common\utils\dataDictionary\BdSoundUseDetail;
use app\common\utils\dataDictionary\BillSource;
use app\common\utils\dataDictionary\BillType;
use app\common\utils\dataDictionary\CacheTag;
use app\common\utils\dataDictionary\MessageType;
use app\common\utils\dataDictionary\RoomBillHook;
use app\common\utils\dataDictionary\RoomBillStatus;
use app\common\utils\dataDictionary\RoomLogLevel;
use app\common\utils\dataDictionary\RoomLogType;
use app\common\utils\dataDictionary\SmsTemplate;
use app\common\utils\DebugUtil;
use app\common\utils\StringUtil;
use think\Exception;

class RoomHookService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
//判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
//返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    private static $USER = "USER";
    private static $ROOM = "ROOM";
    private static $DETAIL = "DETAIL";
    private static $PRICE = "PRICE";
    private static $CAR = "CAR";
    private static $DATA_ALL = ["USER", "ROOM", "DETAIL", "PRICE", "CAR_NUMBER"];


    private function getBaseData($billId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $roomTypeId = $bill->room_type_id;
        $billSourceId = $bill->bill_source;
        $billSource = BillSourceService::getInstance()->getBillSourceById($billSourceId);
        $billSourceName = $billSource->source_name;
        $billSourceSign = $billSource->sign;
        $bill->bill_source_name = $billSourceName;
        $bill->bill_source_sign = $billSourceSign;
        $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
        $roomTypeName = $roomType->name;
        $roomTypeCode = $roomType->room_type_code;
        $bill->room_type_name = $roomTypeName;
        $bill->room_type_code = $roomTypeCode;
        $roomSaleType = RoomSaleTypeService::getInstance()->getRoomSaleTypeById($bill->sale_type_id);
        $bill->room_sale_type_name = $roomSaleType->name;
        $bill->room_sale_type_sign = $roomSaleType->sign;
        $bill->room_sale_type_unit = $roomSaleType->unit;
        $connectBill = RoomBillConnectService::getInstance()->getRoomBillConnectListByBillId($billId);
        $ids = [];
        $connectDetails = null;
        if ($connectBill) {
            $connect_code = $connectBill->connect_code;
            $connectBills = RoomBillConnectService::getInstance()->getRoomBillConnectListByConnectCode($connect_code, null);
            foreach ($connectBills as $connectBill) {
                $ids[] = $connectBill->bill_id;
            }
            $connectDetails = RoomBillDetailService::getInstance()->getDetailList($ids, null, null, null, null, 1,null,null);
        }
        $bill->connect_details = $connectDetails;
        return $bill;
    }


    /**
     * 未配置是跳过，节省时间
     * @param $shopId
     * @param $sign
     * @return bool
     */
    private function checkSetting($shopId, $sign)
    {
        $flag = true;
        $hookSetting = HookService::getInstance()->getHookBySign($shopId, $sign);
        if (empty($hookSetting)) {
            $flag = false;
        }
        if ($hookSetting->status == 0) {
            $flag = false;
        }
        return $flag;
    }

    public function getHookData($bill, $types)
    {
        $billId = $bill->id;
        $intermediaryId = $bill->intermediary_id;
        $roomId = $bill->room_id;
        $billCode = $bill->bill_code;
        $users = [];
        if (in_array("USER", $types)) {
            $usersList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
//            DebugUtil::log(json_encode($usersList));
            foreach ($usersList as $itemUser) {
                if (!StringUtil::checkEmpty($itemUser->name) &&
                    !StringUtil::checkEmpty($itemUser->identification_image)) {
                    array_push($users, $itemUser);
                }
            }
        }
        $bill->users = $users;
        $room = null;
        if (in_array("ROOM", $types)) {
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
            }
        }
        $bill->room = $room;
        $details = [];
        if (in_array("DETAIL", $types)) {
            $details = RoomBillDetailService::getInstance()->getDetailList($billId, null, null, 1, null, 1,null, null);
            foreach ($details as $item) {
                $room_bill_type_id = $item->room_bill_type_id;
                $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeById($room_bill_type_id);
                $item->room_bill_type_sign = $detailType->sign;
                $accountId = $item->account_id;
                if ($accountId) {
                    $account = SysAccountService::getInstance()->getSysAccountById($item->account_id);
                    $item->account_name = $account->account_name;
                    $item->account_sign = $account->sign;
                    $item->account_type = $account->type;
                } else {
                    $item->account_name = "";
                    $item->account_sign = "";
                    $item->account_type = "";
                }
                $item->bill_code = $billCode;
            }
        }
        $bill->details = $details;
        $roomDatePrice = [];
        if (in_array("PRICE", $types)) {
            $roomDatePrice = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($billId, null);
            foreach ($roomDatePrice as $price) {
                $breakfastCouponList = [];
                $roomPriceId = $price->room_service_id;
                if ($intermediaryId) {
                    $service = IntermediaryRoomPriceServiceService::getInstance()->getIntermediaryRoomPriceServiceById($roomPriceId);
                } else {
                    $service = RoomPriceServiceService::getInstance()->getRoomPriceServiceById($roomPriceId);
                }
                $serviceName = $service->service_name;
                $price->service_name = $serviceName;
                $breakfastCoupons = $service->breakfast_coupon;
                if ($breakfastCoupons) {
                    foreach ($breakfastCoupons as $breakfastCouponRecord) {
                        $breakfastCoupon = BreakfastCouponService::getInstance()->getBreakfastCouponById($breakfastCouponRecord['coupon_id']);
                        $breakfastCouponCount = $breakfastCouponRecord['count'];
                        for ($i = 0; $i < $breakfastCouponCount; $i++) {
                            array_push($breakfastCouponList, $breakfastCoupon);
                        }
                    }
                }
                $price->breakfast_coupons = $breakfastCouponList;
            }
        }
        $bill->price = $roomDatePrice;
        $car = [];
        if (in_array("CAR", $types)) {
            $car = RoomBillCarService::getInstance()->getRoomBillCarByBillId($billId);
        }
        $bill->car = $car;
        return $bill;
    }

    public function getTeamHookData($bill, $types)
    {
        $billId = $bill->id;
        $intermediaryId = $bill->intermediary_id;
        $roomId = $bill->room_id;
        $billCode = $bill->bill_code;
        $users = [];
        if (in_array("USER", $types)) {
            $usersList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
            foreach ($usersList as $itemUser) {
                if (!StringUtil::checkEmpty($itemUser->name)) {
                    array_push($users, $itemUser);
                }
            }
        }
        $bill->users = $users;
        $room = null;
        if (in_array("ROOM", $types)) {
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
            }
        }
        $bill->room = $room;
        $details = [];
        if (in_array("DETAIL", $types)) {
            $details = RoomBillDetailService::getInstance()->getDetailList($billId, null, null, 1, null, 1,null, null);
            foreach ($details as $item) {
                $room_bill_type_id = $item->room_bill_type_id;
                $detailType = RoomBillDetailTypeService::getInstance()->getRoomBillDetailTypeById($room_bill_type_id);
                $item->room_bill_type_sign = $detailType->sign;
                $accountId = $item->account_id;
                if ($accountId) {
                    $account = SysAccountService::getInstance()->getSysAccountById($item->account_id);
                    $item->account_name = $account->account_name;
                    $item->account_sign = $account->sign;
                    $item->account_type = $account->type;
                } else {
                    $item->account_name = "";
                    $item->account_sign = "";
                    $item->account_type = "";
                }
                $item->bill_code = $billCode;
            }
        }
        $bill->details = $details;
        $roomDatePrice = [];
        if (in_array("PRICE", $types)) {
            $roomDatePrice = RoomBillPriceDateService::getInstance()->getRoomBillPriceDateList($billId, null);
            foreach ($roomDatePrice as $price) {
                $breakfastCouponList = [];
                $roomPriceId = $price->room_service_id;
                if ($intermediaryId) {
                    $service = IntermediaryRoomPriceServiceService::getInstance()->getIntermediaryRoomPriceServiceById($roomPriceId);
                } else {
                    $service = RoomPriceServiceService::getInstance()->getRoomPriceServiceById($roomPriceId);
                }
                $serviceName = $service->service_name;
                $price->service_name = $serviceName;
                $breakfastCoupons = $service->breakfast_coupon;
                if ($breakfastCoupons) {
                    foreach ($breakfastCoupons as $breakfastCouponRecord) {
                        $breakfastCoupon = BreakfastCouponService::getInstance()->getBreakfastCouponById($breakfastCouponRecord['coupon_id']);
                        $breakfastCouponCount = $breakfastCouponRecord['count'];
                        for ($i = 0; $i < $breakfastCouponCount; $i++) {
                            array_push($breakfastCouponList, $breakfastCoupon);
                        }
                    }
                }
                $price->breakfast_coupons = $breakfastCouponList;
            }
        }
        $bill->price = $roomDatePrice;
        $car = [];
        if (in_array("CAR", $types)) {
            $car = RoomBillCarService::getInstance()->getRoomBillCarByBillId($billId);
        }
        $bill->car = $car;
        return $bill;
    }

    /**
     * 预定回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminBookRoom($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;

            $billCode = $bill->bill_code;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room = $room->room_number;
            } else {
                $roomTypeId = $bill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $room = $roomType->name;
            }

            $aliSms = AlismsService::getInstance()->getAlisms($shopId);
            if (empty($aliSms)) {
                $aliSms = AlismsService::getInstance()->getAlisms($shopIdZong);
            }
            $enterCodeTemplate = AlismsTemplateService::getInstance()->getTemplateByShopIdAndSign($shopId, SmsTemplate::ROOM_ENTER_CODE);
            $confirmTemplate = AlismsTemplateService::getInstance()->getTemplateByShopIdAndSign($shopId, SmsTemplate::ROOM_BILL_CONFIRM);
            if (empty($template) || $template->use_status == 0 || $template->status != 1) {
                return ["Code" => "ERROR", "Message" => "短信发送未开启或者未审核通过"];
            };
            $sign = null;
            if (!empty($enterCodeTemplate) && $enterCodeTemplate->use_status == 1 && $enterCodeTemplate->status != 1) {
                //预订成功：订单：${no}，预订${hotel}的${room}入住时间${date}，入住码${code}酒店地址：${add}，联系电话${tel}。
                $sign = SmsTemplate::ROOM_ENTER_CODE;
                $qr = RoomBillQrService::getInstance()->addRoomBillQrCode($shopIdZong, $shopId, $billId, 1, 0, 0);
                $enterCode = $qr->code;
                $param = ['no' => $billCode, 'hotel' => $shopName, 'room' => $room, 'date' => $date, 'add' => $address, 'tel' => $link_phone, 'code' => $enterCode];
            } elseif (!empty($confirmTemplate) && $confirmTemplate->use_status == 1 && $confirmTemplate->status != 1) {
                //预订成功：订单编号${no}；预订 ${hotel}的${room}，入住时间${date}；酒店地址：${add}，联系电话${tel}。
                $sign = SmsTemplate::ROOM_BILL_CONFIRM;
                $param = ['no' => $billCode, 'hotel' => $shopName, 'room' => $room, 'date' => $date, 'add' => $address, 'tel' => $link_phone];
            }
            if ($sign) {
                $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, $sign);
                if ($smsRes) {
                    if ($sign == SmsTemplate::ROOM_ENTER_CODE) {
                        $content = "发送入住码短信 : $phone" . $smsRes["Message"];
                    } else {
                        $content = "发送预定短信 : $phone" . $smsRes["Message"];
                    }
                    RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                        $content, '短信', RoomLogType::OTHER, $billId);
                }
            } else {
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                    "发送预定短信 : $phone 短信发送未开启或者未审核通过", '短信', RoomLogType::OTHER, $billId, $shopId);
            }
            if (!$this->checkSetting($shopId, RoomBillHook::BOOK_ROOM)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER, self::$ROOM, self::$PRICE]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::BOOK_ROOM, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }

    }


    /**
     * 入住回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminStayRoom($billId, $adminId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $phone = $bill->link_phone;
        $shopId = $bill->shop_id;
        $billCode = $bill->bill_code;
        $commonCode = $bill->common_code;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        try {
            //门锁，短信
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $currentTime = time();
            $room = RoomService::getInstance()->getRoomById($roomId);
            $room = $room->room_number;
            if (!empty($phone)) {
                //预订成功：订单编号${no}；预订 ${hotel}的${room}，入住时间${date}；酒店地址：${add}，联系电话${tel}。
                $param1 = ['no' => $billCode, 'hotel' => $shopName, 'room' => $room, 'date' => $date, 'add' => $address, 'tel' => $link_phone];
                $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param1, SmsTemplate::ROOM_BILL_CONFIRM);
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "发送入住短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId);
            }
            //门锁
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan, 'check_in');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    if (strlen($itemPhone) == 11) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "发送入住短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId);
                        }
                    }
                }
            }
            if ($this->checkSetting($shopId, RoomBillHook::ADD_ROOM_ORDER)) {
                $data = $this->getBaseData($billId);
                $hookData = $this->getHookData($data, [self::$USER, self::$ROOM, self::$PRICE]);
                HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::ADD_ROOM_ORDER, $hookData);
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        try {
            ActiveService::getInstance()->joinActive($shopIdZong, $shopId, ActiveCondition::BOOK_ROOM, $commonCode, BillType::BOOK_ROOM, $billId);
        } catch (Exception $exception) {
            if ($exception instanceof MyException) {
                DebugUtil::log("参与活动失败：" . $exception->msg, 'error');
            } else {
                DebugUtil::log("参与活动失败：" . $exception->getTraceAsString(), 'error');
            }
            DebugUtil::log("参与活动失败：" . $billId, 'error');
        }
    }


    /**
     * 预定转入住回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminBookToStayRoom($billId, $adminId)
    {
        $bill = RoomBillService::getInstance()->getBillById($billId);
        $commonCode = $bill->common_code;
        $phone = $bill->link_phone;
        $shopId = $bill->shop_id;
        $billCode = $bill->bill_code;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        try {
            //门锁，短信
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $currentTime = time();
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $room = RoomService::getInstance()->getRoomById($roomId);
            $room = $room->room_number;
            //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
            //门锁
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, $phone, $currentTime, $leaveTimePlan, 'check_in');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    if (strlen($itemPhone) == 11) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                "发送入住短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                        }
                    }
                }
            }
            if ($this->checkSetting($shopId, RoomBillHook::BOOK_TO_STAY)) {
                $data = $this->getBaseData($billId);
                $hookData = $this->getHookData($data, []);
                HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::BOOK_TO_STAY, $hookData);
            }

        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        try {
            ActiveService::getInstance()->joinActive($shopIdZong, $shopId, ActiveCondition::BOOK_ROOM, $commonCode, BillType::BOOK_ROOM, $billId);
        } catch (Exception $exception) {
            DebugUtil::log("参与活动失败：" . $billId);
            if ($exception instanceof MyException) {
                DebugUtil::log("参与活动失败：" . $exception->msg, 'error');
            } else {
                DebugUtil::log("参与活动失败：" . $exception->getTraceAsString(), 'error');
            }
        }

    }


    /**
     * 排房回调
     * @param $billId
     * @param $adminId
     * @return bool|void
     */
    public function arrangedRoom($billId, $adminId)
    {
        $flag = false;
        try {
            DebugUtil::log("arrangedRoomHook");
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $checkinType = $bill->checkin_type;
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::ARRANGED_ROOM)) {
                $flag = true;
                return $flag;
            }

            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$ROOM, self::$DETAIL]);
            $result = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::ARRANGED_ROOM, $hookData);
            if ($result['code'] == 0) {
                $data = $result['data'];
                $resData = [];
                if (!empty($resData)) {
                    $resData['id'] = $billId;
                    RoomBillService::getInstance()->editRoomBill($resData);
                }
                $flag = true;
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        return $flag;
    }

    /**
     * 换房回调
     * @param $billId
     * @param $oldRoomId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminChangeRoom($billId, $oldRoomId, $adminId)
    {
        try {
            //门锁，短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $billCode = $bill->bill_code;
            $billStatus = $bill->bill_status;
            if ($billStatus != RoomBillStatus::STAYING && $billStatus != RoomBillStatus::TO_CHECK_OUT) {
                return;
            }
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;

            $currentTime = time();
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $room = RoomService::getInstance()->getRoomById($roomId);
            $room = $room->room_number;
            //门锁
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, $phone, $currentTime, $leaveTimePlan, 'change_room');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    $userStatus = $itemUser->user_status;
                    if (strlen($itemPhone) == 11 && $userStatus == 1) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                "发送换房短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                        }
                    }
                }
            }
            if (!$this->checkSetting($shopId, RoomBillHook::CHANGE_ROOM)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            $res = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::CHANGE_ROOM, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 确认订单回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminConfirmRoomBill($billId, $adminId)
    {
        try {
            //短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $billCode = $bill->bill_code;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room = $room->room_number;
            } else {
                $roomTypeId = $bill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $room = $roomType->name;
            }
            //预订成功：订单编号${no}；预订 ${hotel}的${room}，入住时间${date}；酒店地址：${add}，联系电话${tel}。
            $param = ['no' => $billCode, 'hotel' => $shopName, 'room' => $room, 'date' => $date, 'add' => $address, 'tel' => $link_phone];
            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, SmsTemplate::ROOM_BILL_CONFIRM);
            if ($smsRes) {
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                    "发送确认订单短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
            }

            if (!$this->checkSetting($shopId, RoomBillHook::CONFIRM_ROOM_BILL)) {
                return;
            }
            $rollback = false;
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$ROOM, self::$PRICE, self::$DETAIL]);
            $result = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::CONFIRM_ROOM_BILL, $hookData);
            if ($result['code'] == 0) {
                $rollbackStep = 0;
                $resData = [];
                $resultData = $result['data'];
                if (isset($resultData['order_id']) && $resultData['order_id']) {
                    $resData['other_bill_code'] = $resultData['order_id'];
                }
                if (isset($resultData['accnt']) && $resultData['accnt']) {
                    $resData['accnt'] = $resultData['accnt'];
                }
                if (!empty($resData)) {
                    $resData['id'] = $billId;
                    RoomBillService::getInstance()->editRoomBill($resData);
                }
                if ($this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_DETAIL)) {
                    $hookData = $this->getHookData($data, [self::$DETAIL]);
                    if (isset($hookData['details']) && !empty($hookData['details'])) {
                        $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_DETAIL, $hookData);
                        if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                            $rollback = true;
                            $rollbackStep = 1;
                        }
                    }
                }
                DebugUtil::log("rollbackStep: $rollbackStep", "error");
            }
            $this->checkHookResult($rollback);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 取消回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminCancelRoomBill($billId, $adminId)
    {
        try {
            DebugUtil::log("adminCancelRoomBill: $billId");
            //短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $billCode = $bill->bill_code;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room = $room->room_number;
            } else {
                $roomTypeId = $bill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $room = $roomType->name;
            }
            //尊敬的客户，因客房短缺，您于${date}在${hotel}预订的${room}已被取消，所付费用将原路退还，给您造成不便，我们深感抱歉。
            $param = ['date' => $date, 'hotel' => $shopName, 'room' => $room];
            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, SmsTemplate::ROOM_BILL_REFUSE);
            if ($smsRes) {
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                    "发送取消短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
            }
            if (!$this->checkSetting($shopId, RoomBillHook::CANCEL_ROOM_BILL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::CANCEL_ROOM_BILL, $hookData);
        } catch (Exception $exception) {
            DebugUtil::log("adminCancelRoomBillError: $billId");
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log($exception->getTraceAsString(), 'error');
            $this->checkException($exception);
        }
    }

    /**
     * 退房回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminCheckOut($billId, $adminId)
    {
        DebugUtil::log("adminCheckOutHook: $billId", 'debug');
        try {
            //门锁，短信
            //短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            $billStatus = $bill->bill_status;
            $shareUserId = $bill->share_user_id;
            $bill_source_id = $bill->bill_source;
//            $isMember = MemberUtilService::getInstance()->isMember($commonCode);
            //佣金
            IntermediaryCommissionRecordService::getInstance()->calculateCommission($shopId, BillType::BOOK_ROOM, $billId);
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
//		$shopName = $shop->shop_name;
            MemberUtilService::getInstance()->calculateRoomBillPointAndGrowth(BillType::BOOK_ROOM, $billId, $commonCode);
            //分销
            try {
                DistributionBillService::getInstance()->addRoomBill($shopIdZong, $shopId, $billId);
            } catch (MyException $e) {
                DebugUtil::log($e->msg, "error");
            }
            LockService::getInstance()->checkOut($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            RoomBillUtil::getInstance()->clearStayingBills($shopId);
            DebugUtil::log("roomXFFX");
            //小程序下单才能分享
            $billSource = BillSourceService::getInstance()->getBillSourceById($bill_source_id);
            $billSourceSign = $billSource->sign;
            if ($billSourceSign == BillSource::WX) {
                if ($shareUserId) {
                    $wxUser = UserWxService::getInstance()->getUserById($shareUserId);
                    if ($wxUser) {
                        $commonCode = $wxUser->common_code;
                    }
                }
                $this->roomXFFX($shopId, $commonCode, $billId);
            }

//		$address = $shop->address;
//		$link_phone = $shop->link_phone;
//		$roomId = $bill->room_id;
//		$date = date('Y-m-d H:i', $bill->enter_time_plan);
//		if ($roomId) {
//			$room = RoomService::getInstance()->getRoomById($roomId);
//			$room = $room->room_number;
//		} else {
//			$roomTypeId = $bill->room_type_id;
//			$roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
//			$room = $roomType->name;
//		}
            //尊敬的客户：您在${hotelname}的${roomNum}房间即将在${date}退房，请到及时办理续房或退房。
//		$param = ['date' => $date, 'hotelname' => $shopName, 'roomNum' => $room];
//		$smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, SmsTemplate::ROOM_CHECK_OUT);
//		RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "发送退房短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId);

            if (!$this->checkSetting($shopId, RoomBillHook::CHECK_OUT)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::CHECK_OUT, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    /**
     * 消费返现
     * @param $shopIdZong
     * @param $shopId
     * @param $commonCode
     * @param $billId
     * @return void
     */
    private function roomXFFX($shopId, $commonCode, $billId)
    {
        $user = UserCommonService::getInstance()->getUserCommonByCommonCode($commonCode);
        $shopIdZong = $user->shop_id_zong;
        $gradeId = $user->grade;
        $settingValue = MemberAndRightInterestService::getInstance()->getXFFXRightValue($shopIdZong, $gradeId);
        if ($settingValue <= 0) {
            return;
        }
        $realAmount = RoomBillDetailService::getInstance()->getRoomBillDetailRealAmount($billId, null, null, null);
        $xffxAmount = sprintf("%.2f", $realAmount * $settingValue / 100);
        DebugUtil::log("消费返现：$xffxAmount = $realAmount * $settingValue / 100");
        if ($xffxAmount > 0) {
            UserCommonService::getInstance()->xffxUpdateBalance($shopId, $commonCode, $xffxAmount, $billId, BillType::BOOK_ROOM, "订房");
        }
    }

    /**
     * 走结回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminNoClearCheckOut($billId, $adminId)
    {
        try {

            //门锁，短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $enterTimePlan = $bill->enter_time;
            $leaveTimePlan = $bill->leave_time;
//		$billCode = $bill->bill_code;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
//		$shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
//		$address = $shop->address;
//		$link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room = $room->room_number;
            } else {
                $roomTypeId = $bill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $room = $roomType->name;
            }
            LockService::getInstance()->checkOut($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            //尊敬的客户：您在${hotelname}的${roomNum}房间即将在${date}退房，请到及时办理续房或退房。
            $param = ['date' => $date, 'hotelname' => $shopName, 'roomNum' => $room];
            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, SmsTemplate::ROOM_CHECK_OUT);
            if ($smsRes) {
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "发送退房短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId);
            }
            if (!$this->checkSetting($shopId, RoomBillHook::NO_CLEAR_CHECK_OUT)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::NO_CLEAR_CHECK_OUT, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 续房回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function adminExtendRoom($billId, $adminId)
    {
        try {

            //门锁，短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;

            $billCode = $bill->bill_code;
            $billStatus = $bill->bill_status;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $room = RoomService::getInstance()->getRoomById($roomId);
            $currentTime = time();
            $room = $room->room_number;
            //门锁
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, $phone, time(), $leaveTimePlan, 'extend_room');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    $userStatus = $itemUser->user_status;
                    if (strlen($itemPhone) == 11 && $userStatus == 1) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                        }
                    }
                }
            }
            if (!$this->checkSetting($shopId, RoomBillHook::EXTEND_ROOM)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$ROOM]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::EXTEND_ROOM, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 同意取消回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function agreeCancelRoomBill($billId, $adminId)
    {
        try {

            //门锁，短信
            //短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $billCode = $bill->bill_code;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            if ($roomId) {
                $room = RoomService::getInstance()->getRoomById($roomId);
                $room = $room->room_number;
            } else {
                $roomTypeId = $bill->room_type_id;
                $roomType = RoomTypeService::getInstance()->getRoomTypeById($roomTypeId);
                $room = $roomType->name;
            }
            //尊敬的客户，因客房短缺，您于${date}在${hotel}预订的${room}已被取消，所付费用将原路退还，给您造成不便，我们深感抱歉。
            $param = ['date' => $date, 'hotel' => $shopName, 'room' => $room];
            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $phone, $param, SmsTemplate::ROOM_BILL_REFUSE);
            if ($smsRes) {
                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL, "发送取消短信 : $phone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId);
            }
            if (!$this->checkSetting($shopId, RoomBillHook::AGREE_CANCEL_ROOM_BILL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::AGREE_CANCEL_ROOM_BILL, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    /**
     * 设置预定未到为noshow
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function toNoShow($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::NO_SHOW)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::NO_SHOW, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 拒绝退房
     * @param $billId
     * @param $adminId
     */
    public function refuseCheckOut($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::REFUSE_CHECK_OUT)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::REFUSE_CHECK_OUT, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 拒绝取消定案
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function refuseCancelRoomBill($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::REFUSE_CANCEL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::REFUSE_CANCEL, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 编辑联系人
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function updateLinkInfo($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_LINK_INFO)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, []);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_LINK_INFO, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    public function addRoomUser($billId, $userId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->addUserInfo($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan, $userId);
            if (!$this->checkSetting($shopId, RoomBillHook::ADD_ROOM_USER)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::ADD_ROOM_USER, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    public function delRoomUser($billId, $userId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->delUser($shopId, $billId, $roomId, $userId);
            if (!$this->checkSetting($shopId, RoomBillHook::DEL_ROOM_USER)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::DEL_ROOM_USER, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    public function roomUserLeave($billId, $userId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->updateUserInfo($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            if (!$this->checkSetting($shopId, RoomBillHook::LEAVE_ROOM_USER)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::LEAVE_ROOM_USER, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    /**
     * 编辑入住人
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function updateRoomUserInfo($billId, $checkInType)
    {
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
//            if ($bill->team_id){
//                sleep(8);
//                return;
//            }
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->updateUserInfo($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_USER)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER]);
            $md5Value = md5(json_encode($hookData));
            $md5Key = "updateRoomUserInfo_$billId";
            if (CacheManager::has($md5Key)) {
                $cacheValue = CacheManager::getCacheValue($md5Value);
                if ($cacheValue == $md5Value) {
                    return;
                }
            }
            $hookData['check_in_type'] = $checkInType;
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_USER, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
                $rollback = true;
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        $this->checkHookResult($rollback);
    }

    /**
     * 编辑房价
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function updateRoomPrice($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::WX_CANCEL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$DETAIL, self::$ROOM]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_CANCEL, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    /**
     * 编辑财务
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function updateRoomFund($billId, $adminId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_DETAIL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$DETAIL]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_DETAIL, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    public function updateCarNumber($billId)
    {
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            if (!$this->checkSetting($shopId, RoomBillHook::CAR_NUMBER)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$CAR]);
            HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::CAR_NUMBER, $hookData);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }


    /**
     * 微信订房回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxBookRoom($billId, $adminId)
    {
        $cacheKey = "wxBookRoom_$billId";
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $commonCode = $bill->common_code;
            $billStatus = $bill->bill_status;
            $checkinType = $bill->checkin_type;
            if (CacheManager::has($cacheKey, null, null)) {
                return;
            }
            $hookSetting = HookService::getInstance()->getHookBySign($shopId, RoomBillHook::WX_BOOK_ROOM);
            $hookId = $hookSetting->id;
            $hookRecord = HookResultService::getInstance()->getHookResultByHookId($shopId, $hookId, $billId);
            if ($hookRecord) {
                $result = $hookRecord->result;
                if (isset($result['code']) && $result['code'] == 0) {
                    return;
                }
            }
            if ($billStatus == RoomBillStatus::TO_BE_STAY) {
                $key = "auto_confirms_$shopId";
                if (CacheManager::has($key, CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId)) {
                    $autoConfirmList = CacheManager::getCacheValue($key, CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId);
                    $autoConfirmList[] = $billId;
                    CacheManager::setCacheValue($key, $autoConfirmList, CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId, 40);
                } else {
                    $autoConfirmList = [];
                    $autoConfirmList[] = $billId;
                    CacheManager::setCacheValue($key, $autoConfirmList, CacheTag::ROOM_BILL_AUTO_CONFIRM, $shopId, 40);
                }
            }
            RoomBillUtil::getInstance()->clearStayingBills($shopId);
            $userWx = UserWxService::getInstance()->getUserByCommonCode($commonCode);
            if (!empty($userWx)) {
                $userWxId = $userWx->id;
                UserWxService::getInstance()->updateLastShopId($userWxId, $shopId);
            }
            $billCode = $bill->bill_code;
            if ($checkinType == 1) {
                $title = "小程序订房";
                $enterDate = date('m-d H:i', $bill->enter_time_plan);
                $LeaveDate = date('m-d H:i', $bill->leave_time_plan);
                $content = "小程序订房预住时间 $enterDate/$LeaveDate $billCode  请及时处理！";
                $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
                MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_BOOKING_ROOM);
            }
            BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::ROOM_BILL, BdSoundUseDetail::WX_BOOK_ROOM);
            if (!$this->checkSetting($shopId, RoomBillHook::WX_BOOK_ROOM)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$ROOM, self::$PRICE, self::$DETAIL]);
            $result = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_BOOK_ROOM, $hookData);
            $rollbackStep = 0;
            if ($result['code'] == 0) {
                $resData = [];
                $resultData = $result['data'];
                if (isset($resultData['order_id']) && $resultData['order_id']) {
                    $resData['other_bill_code'] = $resultData['order_id'];
                }
                if (isset($resultData['accnt']) && $resultData['accnt']) {
                    $resData['accnt'] = $resultData['accnt'];
                }
                if (!empty($resData)) {
                    $resData['id'] = $billId;
                    RoomBillService::getInstance()->editRoomBill($resData);
                }
                if ($this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_USER)) {
                    $hookData = $this->getHookData($data, [self::$USER]);
                    if (isset($hookData['users']) && !empty($hookData['users'])) {
                        $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_USER, $hookData);
                        DebugUtil::log($hookResult);
                        if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                            $rollback = true;
                            $rollbackStep = 1;
                        }
                    }
                }
                if ($this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_DETAIL)) {
                    $hookData = $this->getHookData($data, [self::$DETAIL]);
                    if (isset($hookData['details']) && !empty($hookData['details'])) {
                        $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_DETAIL, $hookData);
                        if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                            $rollback = true;
                            $rollbackStep = 1;
                        }
                    }
                }
            }
            if ($rollback) {
                DebugUtil::log("rollbackStep: $rollbackStep", "error");
            }
        } catch (Exception $exception) {
            DebugUtil::log($exception->getMessage());
            $this->checkException($exception);
        }
        if (!$rollback) {
            CacheManager::setCacheValue($cacheKey, null, null, null, 60);
        }
//        $this->checkHookResult($rollback);

    }

    /**
     * 申请退房
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxCheckOutRoomBill($billId, $adminId)
    {
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;

            $title = "小程序申请退房";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->leave_time_plan);
            $content = "小程序订房预离时间 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_CHECK_OUT_ROOM);
            BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::ROOM_BILL, BdSoundUseDetail::WX_CHECK_OUT);
            if (!$this->checkSetting($shopId, RoomBillHook::WX_CHECK_OUT)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER, self::$ROOM]);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_CHECK_OUT, $hookData);

        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        $this->checkHookResult($rollback);
    }


    /**
     * 申请取消
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxCancelRoomBill($billId, $adminId)
    {
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            DebugUtil::log(json_encode($bill));
            $shopId = $bill->shop_id;
            $billStatus = $bill->bill_status;
            $title = "小程序取消订房订单";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->enter_time_plan);
            $content = "小程序取消订单 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_CANCEL_ROOM);
            if ($billStatus == RoomBillStatus::TO_BE_CONFIRMED) {
                //待确认订单未推送
                return;
            }
            if (!$this->checkSetting($shopId, RoomBillHook::WX_CANCEL)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$PRICE, self::$ROOM, self::$DETAIL, self::$USER, self::$CAR,]);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_CANCEL, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                $rollback = true;
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        $this->checkHookResult($rollback);
    }

    /**
     * 自助入住
     * @param $billId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxUserStay($billId, $checkInType = 'mini_program')
    {
        DebugUtil::log("$billId: $checkInType");
        $bill = RoomBillService::getInstance()->getBillById($billId);
        if (!$bill->room_id) {
            DebugUtil::log("billId: no roomId");
            return;
        }
        $phone = $bill->link_phone;
        $shopId = $bill->shop_id;
        $billCode = $bill->bill_code;
        $commonCode = $bill->common_code;
        $rollback = false;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        $shopName = $shop->shop_name;
        $roomId = $bill->room_id;
        try {

            $hookSetting = HookService::getInstance()->getHookBySign($shopId, RoomBillHook::WX_STAY);
            $hookId = $hookSetting->id;
            $hookRecord = HookResultService::getInstance()->getHookResultByHookId($shopId, $hookId, $billId);
            if ($hookRecord) {
                $result = $hookRecord->result;
                if (isset($result['code']) && $result['code'] == 0) {
                    DebugUtil::log("billId: no roomId");
                    return;
                }
            }

            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;
            $currentTime = time();
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $room = RoomService::getInstance()->getRoomById($roomId);
            if ($room) {
                $roomNumber = $room->room_number;
            } else {
                $roomNumber = "";
            }
            $title = "自助入住";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->enter_time_plan);
            $content = "自助入住 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_SELF_CHECK_IN);
            if (!empty($room)) {
                BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::ROOM_BILL, BdSoundUseDetail::SELF_STAYING);
                $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, null, time(), $leaveTimePlan, 'check_in');
                foreach ($passwords as $password) {
                    $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                    foreach ($userList as $itemUser) {
                        $itemPhone = $itemUser->phone;
                        $userStatus = $itemUser->user_status;
                        $send_password = $itemUser->send_password;
                        if (strlen($itemPhone) == 11 && $userStatus == 1 && $send_password == 0) {
                            //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                            $param2 = ['hotelname' => $shopName, 'roomNum' => $roomNumber, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                            if ($smsRes) {
                                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                    "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                            }
                        }
                    }
                }
            }
            if ($this->checkSetting($shopId, RoomBillHook::WX_STAY)) {
                DebugUtil::log("$billId:hook closed");
                $data = $this->getBaseData($billId);
                $data['check_in_type'] = $checkInType;
                $hookData = $this->getHookData($data, [self::$USER, self::$ROOM]);
//			DebugUtil::log(json_encode($hookData));
                $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_STAY, $hookData);
                if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                    DebugUtil::log($hookResult, 'error');
                    $rollback = true;
                }
                if (!$rollback && $this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_USER)) {
                    $hookData = $this->getHookData($data, [self::$USER]);
                    if (isset($hookData['users']) && !empty($hookData['users']) && count($hookData['users']) > 1) {
                        $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_USER, $hookData);
                        if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                            $rollback = true;
                        }
                    }
                }
                $this->checkHookResult($rollback);
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        try {
            ActiveService::getInstance()->joinActive($shopIdZong, $shopId, ActiveCondition::BOOK_ROOM, $commonCode, BillType::BOOK_ROOM, $billId);
        } catch (Exception $exception) {
            DebugUtil::log("参与活动失败：" . $billId);
            if ($exception instanceof MyException) {
                DebugUtil::log("参与活动失败：" . $exception->msg, 'error');
            } else {
                DebugUtil::log("参与活动失败：" . $exception->getTraceAsString(), 'error');
            }
        }
    }


    /**
     * 微信认证回调
     * @param $billId
     * @param $commonCode
     * @throws \app\common\sysConfig\MyException
     */
    public function wxAuthentication($billId, $commonCode)
    {
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $roomId = $bill->room_id;
            $leaveTimePlan = $bill->leave_time_plan;
            $enterTimePlan = time();
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopName = $shop->shop_name;
            $room = RoomService::getInstance()->getRoomById($roomId);
            $room = $room->room_number;
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, null, time(), $leaveTimePlan, 'check_in');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    $userStatus = $itemUser->user_status;
                    $send_password = $itemUser->send_password;
                    $itemUserCommonCode = $itemUser->common_code;
                    if (strlen($itemPhone) == 11 && $userStatus == 1 && $send_password == 0 && $itemUserCommonCode == $commonCode) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $enterTimePlan), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                        }
                    }
                }
            }
            RoomBillUtil::getInstance()->clearStayingBills($shopId);
            if (!$this->checkSetting($shopId, RoomBillHook::AUTHENTICATION)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER, self::$ROOM]);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::AUTHENTICATION, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                $rollback = true;
            }
            if ($this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_DETAIL)) {
                $hookDataDetail = $this->getHookData($data, [self::$DETAIL]);
                HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_DETAIL, $hookDataDetail);
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        $this->checkHookResult($rollback);
    }

    /**
     * 续房回调
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxExtendRoom($billId, $adminId)
    {
        $rollback = false;
        try {
            //门锁，短信
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;

            $billCode = $bill->bill_code;
            $billStatus = $bill->bill_status;
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $address = $shop->address;
            $link_phone = $shop->link_phone;
            $roomId = $bill->room_id;
            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $currentTime = time();
            $room = RoomService::getInstance()->getRoomById($roomId);
            $room = $room->room_number;

            $title = "小程序续房";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->enter_time_plan);
            $content = "小程序续房 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_EXTEND_ROOM);

            //门锁
            $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, $phone, time(), $leaveTimePlan, 'extend_room');
            foreach ($passwords as $password) {
                $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                foreach ($userList as $itemUser) {
                    $itemPhone = $itemUser->phone;
                    $userStatus = $itemUser->user_status;
                    if (strlen($itemPhone) == 11 && $userStatus == 1) {
                        //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                        $param2 = ['hotelname' => $shopName, 'roomNum' => $room, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                        $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                        if ($smsRes) {
                            RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                        }
                    }
                }
            }
            if (!$this->checkSetting($shopId, RoomBillHook::WX_EXTEND_ROOM)) {
                return;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$ROOM, self::$PRICE, self::$DETAIL]);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_EXTEND_ROOM, $hookData);
            if ($this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_DETAIL)) {
                $hookDataDetail = $this->getHookData($data, [self::$DETAIL]);
                $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_DETAIL, $hookDataDetail);
            }
            $this->checkHookResult($rollback);
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
    }

    public function wxTeamRoomBillBooking($billId)
    {
        DebugUtil::log("wxTeamRoomBillBooking: $billId");
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
//            if (!$bill->room_id) {
//                return false;
//            }
            $phone = $bill->link_phone;
            $shopId = $bill->shop_id;
            $billCode = $bill->bill_code;
            $hookSetting = HookService::getInstance()->getHookBySign($shopId, RoomBillHook::UPDATE_ROOM_USER_BOOKING);
            $hookId = $hookSetting->id;
            $hookRecord = HookResultService::getInstance()->getHookResultByHookId($shopId, $hookId, $billId);
            if ($hookRecord) {
                $result = $hookRecord->result;
                if (isset($result['code']) && $result['code'] == 0) {
                    return true;
                }
            }
            $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
            $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
            $shopName = $shop->shop_name;
            $roomId = $bill->room_id;
            $enterTimePlan = $bill->enter_time_plan;
            $currentTime = time();
            $leaveTimePlan = $bill->leave_time_plan;
            $date = date('Y-m-d H:i', $bill->enter_time_plan);
            $room = RoomService::getInstance()->getRoomById($roomId);
            if ($room) {
                $roomNumber = $room->room_number;
            } else {
                $roomNumber = "";
            }
            $title = "团队自助入住";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->enter_time_plan);
            $content = "团队自助入住 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_SELF_CHECK_IN);
            if (!empty($room)) {
                BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::ROOM_BILL, BdSoundUseDetail::SELF_STAYING);
                $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, null, time(), $leaveTimePlan, 'check_in');
                foreach ($passwords as $password) {
                    $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                    foreach ($userList as $itemUser) {
                        $itemPhone = $itemUser->phone;
                        $userStatus = $itemUser->user_status;
                        $send_password = $itemUser->send_password;
                        if (strlen($itemPhone) == 11 && $userStatus == 1 && $send_password == 0) {
                            //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                            $param2 = ['hotelname' => $shopName, 'roomNum' => $roomNumber, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                            if ($smsRes) {
                                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                    "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                            }
                        }
                    }
                }
            }
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_USER_BOOKING)) {
                return false;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getTeamHookData($data, [self::$USER, self::$ROOM]);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_USER_BOOKING, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
                $rollback = true;
            }
        } catch (Exception $exception) {
            $this->checkException($exception);
        }
        $this->checkHookResult($rollback);
        return false;
    }

    public function wxTeamRoomBillCheckIn($billId, $sendCommonCode, $checkInType)
    {
        DebugUtil::log("wxTeamRoomBillCheckIn: $billId");
        $cacheKey = "wx_user_stay_$billId";
        if (CacheManager::has($cacheKey)) {
            DebugUtil::log('wx_user_stay_' . $billId . ' 存在');
            return true;
        }
        CacheManager::setCacheValue($cacheKey, $billId, null, 5);
        $rollback = false;
        $bill = RoomBillService::getInstance()->getBillById($billId);
        if (!$bill->room_id) {
            DebugUtil::log("没有房间号");
            return false;
        }
        $phone = $bill->link_phone;
        $shopId = $bill->shop_id;
        $billCode = $bill->bill_code;
        $shop = ShopService::getInstance()->getShopByIdFromCache($shopId);
        $shopIdZong = $shop->shop_pid == 0 ? $shopId : $shop->shop_pid;
        $shopName = $shop->shop_name;
        try {

            $hookSetting = HookService::getInstance()->getHookBySign($shopId, RoomBillHook::WX_STAY);
            $hookId = $hookSetting->id;
            $hookRecord = HookResultService::getInstance()->getHookResultByHookId($shopId, $hookId, $billId);
            if ($hookRecord) {
                $result = $hookRecord->result;
                if (isset($result['code']) && $result['code'] == 0) {
                    DebugUtil::log("没有配置hook");
                    return true;
                }
            }

            $roomId = $bill->room_id;
            $enterTimePlan = $bill->enter_time_plan;
            $leaveTimePlan = $bill->leave_time_plan;
            $currentTime = time();
            $room = RoomService::getInstance()->getRoomById($roomId);
            if ($room) {
                $roomNumber = $room->room_number;
            } else {
                $roomNumber = "";
            }
            $title = "团队自助入住";
            $billCode = $bill->bill_code;
            $enterDate = date('Y-m-d H:i:s', $bill->enter_time_plan);
            $content = "团队自助入住 $enterDate $billCode  请及时处理！";
            $messageContent = ['bill_code' => $billCode, 'bill_id' => intval($billId), 'content' => $content];
            MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::WX_SELF_CHECK_IN);
            if (!empty($room)) {
                BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::ROOM_BILL, BdSoundUseDetail::SELF_STAYING);
                $passwords = LockService::getInstance()->createPassword($shopId, $billId, $roomId, null, time(), $leaveTimePlan, 'check_in');
                foreach ($passwords as $password) {
                    $userList = RoomBillUserService::getInstance()->getRoomBillUserList($billId);
                    foreach ($userList as $itemUser) {
                        $itemPhone = $itemUser->phone;
                        $userStatus = $itemUser->user_status;
                        $send_password = $itemUser->send_password;
                        if (strlen($itemPhone) == 11 && $userStatus == 1 && $send_password == 0) {
                            //尊敬的客户：您在${hotelname}的${roomNum}房间密码为${passoword}，有效期${startTime}至${endTime}。
                            $param2 = ['hotelname' => $shopName, 'roomNum' => $roomNumber, 'password' => $password, 'startTime' => date('Y-m-d H:i', $currentTime), 'endTime' => date('Y-m-d H:i', $leaveTimePlan)];
                            $smsRes = AlismsTemplateService::getInstance()->sendSms($shopId, $itemPhone, $param2, SmsTemplate::ADD_ROOM_PASSWORD);
                            if ($smsRes) {
                                RoomBillLogService::getInstance()->saveRoomLog(RoomLogLevel::NORMAL,
                                    "发送密码短信 : $itemPhone" . $smsRes["Message"], '短信', RoomLogType::OTHER, $billId, $shopId);
                            }
                        }
                    }
                }
            }
            if (!$this->checkSetting($shopId, RoomBillHook::WX_TEAM_STAY)) {
                DebugUtil::log("没有配置hook1");
                return false;
            }
            $data = $this->getBaseData($billId);
            $data['check_in_type'] = $checkInType;
            $hookData = $this->getTeamHookData($data, [self::$USER, self::$ROOM]);
            $hookData['bill_status'] = RoomBillStatus::STAYING;
            $commonMd5 = md5($sendCommonCode);
            $hookData['cache_key'] = $commonMd5;
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::WX_TEAM_STAY, $hookData);
            CacheManager::setCacheValue($cacheKey, $billId, null, null, 10);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
                $rollback = true;
                CacheManager::clearCacheValue($cacheKey);
            }
            if (!$rollback && $this->checkSetting($shopId, RoomBillHook::UPDATE_TEAM_ROOM_USER)) {
                $hookData = $this->getTeamHookData($data, [self::$USER]);
                if (isset($hookData['users']) && !empty($hookData['users']) && count($hookData['users']) > 1) {
                    foreach ($hookData['users'] as $itemUser) {
                        if ($itemUser['common_code'] != $sendCommonCode) {
                            $commonMd5 = md5($itemUser['common_code']);
                            $hookData['cache_key'] = $commonMd5;
                        }
                    }
                    if (count($hookData['users']) > 1) {
                        $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_TEAM_ROOM_USER, $hookData);
                        if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                            $rollback = true;
                            CacheManager::clearCacheValue($cacheKey);
                        }
                    }
                }
            }
            $hookCacheKey = "wx_user_stay_$billId" . $sendCommonCode;
            CacheManager::setCacheValue($hookCacheKey, $sendCommonCode, null, 60);
        } catch (Exception $exception) {
            DebugUtil::log("wxTeamRoomBillCheckIn推送失败：$billId", 'error');
//            $this->checkException($exception);
        }
//        $this->checkHookResult($rollback);
        try {
            ActiveService::getInstance()->joinActive($shopIdZong, $shopId, ActiveCondition::BOOK_ROOM, $sendCommonCode, BillType::BOOK_ROOM, $billId);
        } catch (Exception $exception) {
            DebugUtil::log("参与活动失败：" . $billId);
            if ($exception instanceof MyException) {
                DebugUtil::log("参与活动失败：" . $exception->msg, 'error');
            } else {
                DebugUtil::log("参与活动失败：" . $exception->getTraceAsString(), 'error');
            }
        }
        return false;
    }

    /**
     * 编辑入住人
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function updateTeamRoomUserInfo($billId, $sendCommonCode)
    {
        DebugUtil::log("updateTeamRoomUserInfoCommonCode: $sendCommonCode");
        DebugUtil::log("updateTeamRoomUserInfo: $billId");
        $rollback = false;
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->updateUserInfo($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_TEAM_ROOM_USER)) {
                DebugUtil::log("hook stop 1");
                return true;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getTeamHookData($data, [self::$USER, self::$ROOM]);
            $md5Value = md5(json_encode($hookData));
            $md5Key = "updateTeamRoomUserInfo_$billId";
            if (CacheManager::has($md5Key)) {
                $cacheValue = CacheManager::getCacheValue($md5Value);
                if ($cacheValue == $md5Value) {
                    DebugUtil::log("hook stop 2");
                    return true;
                }
            }
            $hookData['cache_key'] = md5($sendCommonCode);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_TEAM_ROOM_USER, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
                $rollback = true;
            }
            CacheManager::setCacheValue($md5Key, $md5Value, null, null, 10);
            $hookCacheKey = "wx_user_stay_$billId" . $commonCode;
            CacheManager::setCacheValue($hookCacheKey, $commonCode, null, 60);
        } catch (Exception $exception) {
            DebugUtil::log("updateTeamRoomUserInfo 推送失败：$billId", 'error');
//            $this->checkException($exception);
        }
//        $this->checkHookResult($rollback);
        return false;
    }

    /**
     * 编辑入住人
     * @param $billId
     * @param $adminId
     * @throws \app\common\sysConfig\MyException
     */
    public function wxTeamToCheckOut($billId, $sendCommonCode)
    {
        DebugUtil::log("wxTeamToCheckOut: $sendCommonCode $billId");
        try {
            $bill = RoomBillService::getInstance()->getBillById($billId);
            $shopId = $bill->shop_id;
            $phone = $bill->link_phone;
            $roomId = $bill->room_id;
            $commonCode = $bill->common_code;
            $enterTimePlan = $bill->enter_time ?? time();
            $leaveTimePlan = $bill->leave_time;
            LockService::getInstance()->updateUserInfo($shopId, $billId, $roomId, $phone, $enterTimePlan, $leaveTimePlan);
            if (!$this->checkSetting($shopId, RoomBillHook::UPDATE_ROOM_USER_TO_CHECK_OUT)) {
                DebugUtil::log("hook stop 1");
                return true;
            }
            $data = $this->getBaseData($billId);
            $hookData = $this->getHookData($data, [self::$USER, self::$ROOM]);
            $users = $hookData['users'];
            $tempUsers = [];
            foreach ($users as $user) {
                if ($user['common_code'] == $sendCommonCode) {
                    $tempUsers[] = $user;
                }
            }
            $hookData['users'] = $tempUsers;
            $hookData['cache_key'] = md5($sendCommonCode);
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::UPDATE_ROOM_USER_TO_CHECK_OUT, $hookData);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
                $rollback = true;
            }
        } catch (Exception $exception) {
            DebugUtil::log("wxTeamToCheckOut 推送失败：$billId", 'error');
//            $this->checkException($exception);
        }
//        $this->checkHookResult($rollback);
        return false;
    }

    public function machineUserInfoHook($shopId, $param)
    {
        DebugUtil::log("machineUserInfoHook");
        try {
            if (!$this->checkSetting($shopId, RoomBillHook::MACHINE_USER_INFO_HOOK)) {
                DebugUtil::log("hook stop 1");
                return true;
            }
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::MACHINE_USER_INFO_HOOK, $param);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
            }
        } catch (Exception $exception) {
            DebugUtil::log("machineUserInfoHook 推送失败", 'error');
        }
        return false;
    }

    public function teamRoomUpdateHook($shopId,$data)
    {
        DebugUtil::log("machineUserInfoHook");
        try {
            if (!$this->checkSetting($shopId, RoomBillHook::TEAM_UPDATE_HOOK)) {
                DebugUtil::log("hook stop 1");
                return true;
            }
            $hookResult = HookService::getInstance()->saveRoomBillHook($shopId, RoomBillHook::TEAM_UPDATE_HOOK, $data);
            if (!isset($hookResult['code']) || $hookResult["code"] != 0) {
                DebugUtil::log($hookResult, 'error');
            }
        } catch (Exception $exception) {
            DebugUtil::log("machineUserInfoHook 推送失败", 'error');
        }
        return false;
    }


    private function checkHookResult($rollback)
    {
        if ($rollback) {
            throw new MyException(Code::FAIL, "办理失败，请联系前台");
        }
    }

    /**
     * 核销早餐券
     * @param $shopId
     * @param $billId
     * @param $couponId
     * @throws MyException
     */
    public function verificationBreakfastCoupon($shopId, $billId, $couponId)
    {
        BdDeviceService::getInstance()->soundSendMessage($shopId, BdDeviceType::SOUND, BdSoundUse::BREAKFAST, BdSoundUseDetail::BREAKFAST);
    }

    public function checkException(Exception $exception)
    {
        DebugUtil::log("数据推送失败", 'error');
        DebugUtil::log($exception->getTraceAsString(), 'error');
        $throwException = false;
        if ($exception instanceof MyException) {
            $msg = $exception->msg;
            $code = $exception->code;
            DebugUtil::log($msg, 'error');
            $exceptionData = $exception->data;
            if ($exceptionData && isset($exceptionData['throw_exception']) && $exceptionData['throw_exception']) {
                throw new MyException(Code::FAIL, $msg);
            }
        } else {
            $msg = $exception->getMessage();
            DebugUtil::log($msg, 'error');
        }
    }

}