<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------


$sky_config = require(file_exists("../../../../sky_config.php")?'../../../../sky_config.php':'D:\\developProject\\php\\sky_config.php');
$sky_config['DB_PREFIX'] = 'hp_3_';
define('SKY_CONFIG', $sky_config);

return [

	// +----------------------------------------------------------------------
	// | 系统设置
	// +----------------------------------------------------------------------
	'APP_URL' => $sky_config['APP_URL'],
	'APP_ROOT' => $sky_config['APP_ROOT'],

	'DB_PREFIX' => $sky_config['DB_PREFIX'],
	'RESOURCE_PATH' => $sky_config['APP_ROOT'] . '/resource',
	'RESOURCE_URL' => $sky_config['APP_URL'] . '/resource',
	'CERT_PATH' => $sky_config['APP_ROOT'] . '/cert',
	"active_code" => isset($sky_config['active_code']) ? $sky_config['active_code'] : '',
	"sys_version" => "v3.5.14",
	"dev_env" => isset($sky_config['dev_env']) ? $sky_config['dev_env'] : '',


	// +----------------------------------------------------------------------
	// | 应用设置
	// +----------------------------------------------------------------------

	// 应用调试模式
	'app_debug' => false,
	// 应用Trace
	'app_trace' => false,
	// 应用模式状态
	'app_status' => '',
	// 是否支持多模块
	'app_multi_module' => true,
	// 入口自动绑定模块
	'auto_bind_module' => false,
	// 注册的根命名空间
	'root_namespace' => [],
	// 扩展函数文件
	'extra_file_list' => [THINK_PATH . 'helper' . EXT],
	// 默认输出类型
	'default_return_type' => 'json',
	// 默认AJAX 数据返回格式,可选json xml ...
	'default_ajax_return' => 'json',
	// 默认JSONP格式返回的处理方法
	'default_jsonp_handler' => 'jsonpReturn',
	// 默认JSONP处理方法
	'var_jsonp_handler' => 'callback',
	// 默认时区
	'default_timezone' => 'PRC',
	// 是否开启多语言
	'lang_switch_on' => true,
	// 默认全局过滤方法 用逗号分隔多个
	'default_filter' => '',
	// 默认语言
	'default_lang' => 'zh-cn',
	// 应用类库后缀
	'class_suffix' => false,
	// 控制器类后缀
	'controller_suffix' => false,

	// +----------------------------------------------------------------------
	// | 模块设置
	// +----------------------------------------------------------------------

	// 默认模块名
	'default_module' => 'index',
	// 禁止访问模块
	'deny_module_list' => ['common'],
	// 默认控制器名
	'default_controller' => 'Index',
	// 默认操作名
	'default_action' => 'index',
	// 默认验证器
	'default_validate' => '',
	// 默认的空控制器名
	'empty_controller' => 'Error',
	// 操作方法后缀
	'action_suffix' => '',
	// 自动搜索控制器
	'controller_auto_search' => false,

	// +----------------------------------------------------------------------
	// | URL设置
	// +----------------------------------------------------------------------

	// PATHINFO变量名 用于兼容模式
	'var_pathinfo' => 's',
	// 兼容PATH_INFO获取
	'pathinfo_fetch' => ['ORIG_PATH_INFO', 'REDIRECT_PATH_INFO', 'REDIRECT_URL'],
	// pathinfo分隔符
	'pathinfo_depr' => '/',
	// URL伪静态后缀
	'url_html_suffix' => 'html',
	// URL普通方式参数 用于自动生成
	'url_common_param' => false,
	// URL参数方式 0 按名称成对解析 1 按顺序解析
	'url_param_type' => 0,
	// 是否开启路由
	'url_route_on' => true,
	// 路由使用完整匹配
	'route_complete_match' => true,
	// 路由配置文件（支持配置多个）
	'route_config_file' => ['route'],
	// 是否开启路由解析缓存
	'route_check_cache' => false,
	// 是否强制使用路由
	'url_route_must' => false,
	// 域名部署
	'url_domain_deploy' => false,
	// 域名根，如thinkphp.cn
	'url_domain_root' => '',
	// 是否自动转换URL中的控制器和操作名
	'url_convert' => false,
	// 默认的访问控制器层
	'url_controller_layer' => 'controller',
	// 表单请求类型伪装变量
	'var_method' => '_method',
	// 表单ajax伪装变量
	'var_ajax' => '_ajax',
	// 表单pjax伪装变量
	'var_pjax' => '_pjax',
	// 是否开启请求缓存 true自动缓存 支持设置请求缓存规则
	'request_cache' => false,
	// 请求缓存有效期
	'request_cache_expire' => null,
	// 全局请求缓存排除规则
	'request_cache_except' => [],

	// +----------------------------------------------------------------------
	// | 模板设置
	// +----------------------------------------------------------------------


	'template' => [
		// 模板引擎类型 支持 php think 支持扩展
		'type' => 'Think',
		// 默认模板渲染规则 1 解析为小写+下划线 2 全部转换小写
		'auto_rule' => 1,
		// 模板路径
		'view_path' => '',
		// 模板后缀
		'view_suffix' => 'html',
		// 模板文件名分隔符
		'view_depr' => DS,
		// 模板引擎普通标签开始标记
		'tpl_begin' => '{',
		// 模板引擎普通标签结束标记
		'tpl_end' => '}',
		// 标签库标签开始标记
		'taglib_begin' => '{',
		// 标签库标签结束标记
		'taglib_end' => '}',
	],

	// 视图输出字符串内容替换
	'view_replace_str' => [],
	// 默认跳转页面对应的模板文件
	'dispatch_success_tmpl' => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',
	'dispatch_error_tmpl' => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',

	// +----------------------------------------------------------------------
	// | 异常及错误设置
	// +----------------------------------------------------------------------

	// 异常页面的模板文件
	'exception_tmpl' => THINK_PATH . 'tpl' . DS . 'think_exception.tpl',

	// 错误显示信息,非调试模式有效
	'error_message' => '页面错误！请稍后再试～',
	// 显示错误信息
	'show_error_msg' => false,
	// 异常处理handle类 留空使用 \think\exception\Handle
//    'exception_handle'       => '',
	'exception_handle' => '\\app\\common\\sysConfig\\Http',

	// +----------------------------------------------------------------------
	// | 日志设置
	// +----------------------------------------------------------------------

	'log' => [
		// 日志记录方式，内置 file socket 支持扩展
		'type' => 'File',
		// 日志保存目录
		'path' => LOG_PATH,
		// 日志记录级别
		'level' => ['debug', 'error','info'],
		'time_format' => 'Y-m-d H:i:s',
		'apart_level' => ['error','debug','info'],
	],

	// +----------------------------------------------------------------------
	// | Trace设置 开启 app_trace 后 有效
	// +----------------------------------------------------------------------
	'trace' => [
		// 内置Html Console 支持扩展
		'type' => 'Html',
	],

	// +----------------------------------------------------------------------
	// | 缓存设置
	// +----------------------------------------------------------------------

	'cache' => [
		// 使用复合缓存类型
		'type' => isset($sky_config['cache']) ? $sky_config['cache'] : "File",
        "db_prefix"=>isset($sky_config['cache_prefix']) ? $sky_config['cache_prefix'] : "",
		// 默认使用的缓存
		'default' => [
			// 驱动方式
			'type' => 'File',
			// 缓存保存目录
			'path' => CACHE_PATH,
		],
		// 文件缓存
		'file' => [
			// 驱动方式
			'type' => 'file',
			// 设置不同的缓存保存目录
			'path' => CACHE_PATH,
		],
		// redis缓存
		'redis' => [
			// 驱动方式
			'type' => 'redis',
			// 服务器地址
			'host' => '127.0.0.1',
		],
        'password' => isset($sky_config['redis_password']) ? $sky_config['redis_password'] : "",
    ],

	// +----------------------------------------------------------------------
	// | 会话设置
	// +----------------------------------------------------------------------

	'session' => [
		'id' => '',
		// SESSION_ID的提交变量,解决flash上传跨域
		'var_session_id' => '',
		// SESSION 前缀
		'prefix' => 'think',
		// 驱动方式 支持redis memcache memcached
		'type' => '',
		// 是否自动开启 SESSION
		'auto_start' => true,
	],

	// +----------------------------------------------------------------------
	// | Cookie设置
	// +----------------------------------------------------------------------
	'cookie' => [
		// cookie 名称前缀
		'prefix' => '',
		// cookie 保存时间
		'expire' => 0,
		// cookie 保存路径
		'path' => '/',
		// cookie 有效域名
		'domain' => '',
		//  cookie 启用安全传输
		'secure' => false,
		// httponly设置
		'httponly' => '',
		// 是否使用 setcookie
		'setcookie' => true,
	],

	//分页配置
	'paginate' => [
		'type' => 'bootstrap',
		'var_page' => 'page',
		'list_rows' => 15,
	],
];
