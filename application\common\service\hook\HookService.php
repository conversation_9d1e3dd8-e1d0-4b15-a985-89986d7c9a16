<?php


namespace app\common\service\hook;

use app\common\model\hook\Hook as HookModel;
use app\common\service\message\MessageUtil;
use app\common\service\shop\ShopService;
use app\common\service\thridSystem\ThirdSystemApiService;
use app\common\service\thridSystem\ThirdSystemService;
use app\common\utils\dataDictionary\HookType;
use app\common\utils\dataDictionary\IndependentMemberHook;
use app\common\utils\dataDictionary\MemberHook;
use app\common\utils\dataDictionary\MessageType;
use app\common\utils\dataDictionary\RoomBillHook;
use app\common\utils\dataDictionary\ThirdSystem;
use app\common\utils\dataDictionary\ThirdSystemApi;
use app\common\utils\dataDictionary\WxNotify;
use app\common\utils\DebugUtil;
use app\common\utils\request\RequestUtil;
use app\common\utils\StringUtil;
use think\Exception;
use think\Request;

class HookService
{
    private static $object = null;

    public function __construct()
    {
    }

    public static function getInstance()
    {
//判断类内部的静态属性是否存在对象
        if (!(self::$object instanceof self)) { //当前保存的内容不是当前类的对象
            self::$object = new self();
        }
//返回对象给外部
        return self::$object;
    }

    public function __clone()
    {
    }

    public function getHookList($shopId, $hookType, $page, $limit)
    {
        $model = new HookModel();
        $list = $model->getHookList($shopId, $hookType, $page, $limit);
        if ($page && $limit) {
            $count = $model->getHookCount($shopId, $hookType);
            return ['list' => $list, 'count' => $count];
        }
        return $list;
    }

    public function getHookById($id)
    {
        $model = new HookModel();
        return $model->getHookById($id);

    }

    public function getHookBySign($shopId, $sign)
    {
        $model = new HookModel();
        return $model->getHookBySign($shopId, $sign);
    }

    public function delHookById($id)
    {
        $model = new HookModel();
        return $model->delHookById($id);
    }

    public function updateHook($param)
    {
        if (isset($param['request_type'])) {
            $param['request_type'] = strtoupper($param['request_type']);
        }
        $this->editHook($param);
    }

    public function editHook($params)
    {
        $model = new HookModel();
        if (isset($params['id'])) {
            $model->editHook($params);
            $id = $params['id'];
        } else {
            $model->saveHook($params);
            $id = $model->id;
        }
        return $id;
    }

    public function initData($shopId)
    {
        $data = [
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::BOOK_TO_STAY, 'name' => '预定转入住回调', 'desc' => '预定转入住回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::ADD_ROOM_ORDER, 'name' => '入住回调', 'desc' => '添加入住回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::BOOK_ROOM, 'name' => '预定回调', 'desc' => '添加预定回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::ARRANGED_ROOM, 'name' => '排房回调', 'desc' => '排房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::CONFIRM_ROOM_BILL, 'name' => '确认订单回调', 'desc' => '确认预定订单回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::CANCEL_ROOM_BILL, 'name' => '取消订单回调', 'desc' => '取消订单回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::CHECK_OUT, 'name' => '退房回调', 'desc' => '退房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::AUTHENTICATION, 'name' => '小程序用户认证回调', 'desc' => '小程序用户认证身份证回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::EXTEND_ROOM, 'name' => '续房回调', 'desc' => '续房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::EDIT_USER_INFO, 'name' => '修改信息回调', 'desc' => '修改信息回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::CHANGE_ROOM, 'name' => '换房回调', 'desc' => '换房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::NO_CLEAR_CHECK_OUT, 'name' => '走结回调', 'desc' => '走结回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::AGREE_CANCEL_ROOM_BILL, 'name' => '同意取消回调', 'desc' => '同意取消回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::NO_SHOW, 'name' => 'noshow回调', 'desc' => 'noshow回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::REFUSE_CHECK_OUT, 'name' => '拒绝退房回调', 'desc' => '拒绝退房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::REFUSE_CANCEL, 'name' => '拒绝取消回调', 'desc' => '拒绝取消回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_BOOK_ROOM, 'name' => '微信订房回调', 'desc' => '微信订房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_CHECK_OUT, 'name' => '微信申请退房回调', 'desc' => '微信申请退房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_CANCEL, 'name' => '微信申请取消回调', 'desc' => '微信申请取消回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_LINK_INFO, 'name' => '更新入住人', 'desc' => '更新入住人'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::ADD_ROOM_USER, 'name' => '添加入住人', 'desc' => '添加入住人'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::DEL_ROOM_USER, 'name' => '删除入住人', 'desc' => '删除入住人'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::LEAVE_ROOM_USER, 'name' => '入住人离店', 'desc' => '入住人离店'],


            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_ROOM_USER, 'name' => '编辑入住人回调', 'desc' => '编辑入住人回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_ROOM_PRICE, 'name' => '编辑房价回调', 'desc' => '编辑房价回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_ROOM_DETAIL, 'name' => '编辑财务回调', 'desc' => '编辑财务回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_STAY, 'name' => '微信自助入住回调', 'desc' => '微信自助入住回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::CAR_NUMBER, 'name' => '录入车牌', 'desc' => '录入车牌'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_EXTEND_ROOM, 'name' => '微信续房回调', 'desc' => '微信续房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::WX_TEAM_STAY, 'name' => '微信团队入住回调', 'desc' => '微信团队入住回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_TEAM_ROOM_USER, 'name' => '团队添加同住人', 'desc' => '微信团队入住回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_ROOM_USER_BOOKING, 'name' => '微信团队预定回调', 'desc' => '微信团队预定回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::UPDATE_ROOM_USER_TO_CHECK_OUT, 'name' => '微信团队申请退房回调', 'desc' => '微信团队申请退房回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::MACHINE_USER_INFO_HOOK, 'name' => '推送自助机用户信息回调', 'desc' => '推送自助机用户信息回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::ROOM_BILL_HOOK, 'sign' => RoomBillHook::TEAM_UPDATE_HOOK, 'name' => '团队入住信息修改推送', 'desc' => '团队入住信息修改推送'],

            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MEMBER_RECHARGE_HOOK, 'name' => '通用会员充值回调', 'desc' => '通用会员充值回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MEMBER_RECHARGE_REFUND_HOOK, 'name' => '通用会员充值退款回调', 'desc' => '通用会员充值退款回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MEMBER_CONSUME_HOOK, 'name' => '通用会员消费回调', 'desc' => '通用会员消费回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MEMBER_CONSUME_REFUND_HOOK, 'name' => '通用会员消费退款回调', 'desc' => '通用会员消费退款回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MEMBER_GROW_UP, 'name' => '通用会员升级回调', 'desc' => '通用会员升级回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::MODIFY_AMOUNT, 'name' => '手动修改', 'desc' => '手动修改'],
            ['shop_id' => $shopId, 'hook_type' => HookType::MEMBER_HOOK, 'sign' => MemberHook::USER_REGISTER_MEMBER, 'name' => '用户注册推送', 'desc' => '用户注册推送'],

            ['shop_id' => $shopId, 'hook_type' => HookType::INDEPENDENT_MEMBER_HOOK, 'sign' => IndependentMemberHook::MEMBER_RECHARGE_HOOK, 'name' => '单店会员充值回调', 'desc' => '单店会员充值回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::INDEPENDENT_MEMBER_HOOK, 'sign' => IndependentMemberHook::MEMBER_RECHARGE_REFUND_HOOK, 'name' => '单店会员充值退款回调', 'desc' => '单店会员充值退款回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::INDEPENDENT_MEMBER_HOOK, 'sign' => IndependentMemberHook::MEMBER_CONSUME_HOOK, 'name' => '单店会员消费回调', 'desc' => '单店会员消费回调'],
            ['shop_id' => $shopId, 'hook_type' => HookType::INDEPENDENT_MEMBER_HOOK, 'sign' => IndependentMemberHook::MEMBER_CONSUME_REFUND_HOOK, 'name' => '单店会员消费退款回调', 'desc' => '单店会员消费退款回调'],
        ];
        foreach ($data as $datum) {
            $itemSign = $datum['sign'];
            $res = $this->getHookBySign($shopId, $itemSign);
            if (!empty($res)) {
                $this->editHook(['id' => $res->id, 'name' => $datum['name'], 'desc' => $datum['desc']]);
            } else {
                $this->editHook($datum);
            }
        }
    }

    /**
     * 发送
     * @param $shopId
     * @param $sign
     * @param $param
     * @return bool|string|void
     */
    public function saveRoomBillHook($shopId, $sign, $param)
    {
        try {
            $start_time = microtime(true);
            $hookSetting = $this->getHookBySign($shopId, $sign);
            $hookId = $hookSetting->id;
            if (empty($hookSetting)) {
                return;
            }
            if ($hookSetting->status == 0) {
                return;
            }
            $request = Request::instance();
            $hookUrl = explode('index.php/', $request->url(true))[0] . 'index.php/api/Hook/saveHookResult';
            $requestParam = [
                'hook_param' => $param,
                'shop_id' => $shopId,
                'hook_id' => $hookId,
            ];
            $hookRes = RequestUtil::getInstance()->requestData($hookUrl, "POST", $requestParam, true);
            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 4);
            DebugUtil::log($hookRes, "debug");
            DebugUtil::log("$sign 耗时：$execution_time", "debug");
            return json_decode($hookRes, true);
        } catch (Exception $exception) {
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($exception->getMessage(), 'error');
        }
    }

    /**
     * 报错hook信息和结果
     * @param $shopId
     * @param $hookId
     * @param $param
     * @return bool|mixed|string
     */
    public function saveHookResult($shopId, $hookId, $param)
    {
        try {
            $hookSetting = $this->getHookById($hookId);
            $hookName = $hookSetting->name;
            $hookType = $hookSetting->hook_type;
            $url = $hookSetting->url;
            $sign = $hookSetting->sign;
            $requestType = $hookSetting->request_type;
            $appKey = $hookSetting->app_key;
            $shop = ShopService::getInstance()->getShopById($shopId);
            $hotelCode = $shop->hotel_code;
            $requestId = StringUtil::getRandomStrs(32, true, true, true, false);
            $param['timestamp'] = time();
            $param['request_id'] = $requestId;
            $param['hotel_code'] = $hotelCode;
            $paramSign = StringUtil::MakeSign(json_decode(json_encode($param), true), $appKey);
            $param['sign'] = $paramSign;
            $billId = isset($param['id'])?$param['id']:0;
            $start_time = microtime(true);
            $hookRes = RequestUtil::getInstance()->requestData($url, $requestType, $param, true);
            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 4);
            DebugUtil::log("$sign 耗时：$execution_time", "debug");
//			DebugUtil::log($url, 'debug');
//			DebugUtil::log(json_encode($param), 'debug');
//			DebugUtil::log($hookRes, 'debug');
            $hookRes = json_decode($hookRes, true);
            $id = HookResultService::getInstance()->addHookResult($shopId, $hookId, $url, $requestId, $param, $billId, $hookRes);
            $title = "消息推送";
            if ($hookType == "ROOM_BILL_HOOK") {
                $billCode = isset($param['bill_code'])?$param['bill_code']:null;
                if (!isset($hookRes['code'])) {
                    $this->sendErrorMsg($requestId, $shopId, $hotelCode, $billCode, $hookRes);
                    $content = "$hookName 消息推送失败  请及时处理！";
                    $messageContent = ['bill_code' => $billCode, 'bill_id' => $billId, 'content' => $content, 'hook_result_id' => $id];
                    MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::HOOK_MESSAGE);
                } else {
                    if ($hookRes['code'] != 0) {
                        $this->sendErrorMsg($requestId, $shopId, $hotelCode, $billCode, $hookRes);
                        $errorMsg = $hookRes['msg'];
                        $content = "$hookName 消息推送失败: $errorMsg   请及时处理！";
                        $messageContent = ['bill_code' => $billCode, 'bill_id' => $billId, 'content' => $content, 'hook_result_id' => $id];
                        MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::HOOK_MESSAGE);
                    }
                }
            } else {
                if (!isset($hookRes['code']) || $hookRes['code'] != 0) {
                    $content = "$hookName 消息推送失败  请及时处理！";
                    $messageContent = ['content' => $content];
                    MessageUtil::getInstance()->publishMessage($shopId, $title, $messageContent, MessageType::HOOK_MESSAGE);
                }
            }
            return $hookRes;
        } catch (Exception $exception) {
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($exception->getMessage(), 'error');
            return ['code' => 0, 'msg' => "success"];
        }
    }

    /**
     * 发送错误信息
     * @param $requestId
     * @param $shopId
     * @param $hotelCode
     * @param $result
     * @param $billCode
     * @return void|null
     */
    private function sendErrorMsg($requestId, $shopId, $hotelCode, $billCode, $result)
    {
        try {
            $thirdSystem = ThirdSystemService::getInstance()->getCurrentThirdSystem($shopId);
            $sign = $thirdSystem->sign;
            if ($sign == ThirdSystem::JINGFEI) {
                return;
            }
            $thirdSystemId = $thirdSystem->id;
            $api = ThirdSystemApiService::getInstance()->getThirdSystemApiBySign($thirdSystemId, ThirdSystemApi::SEND_ERROR_MSG);
            $key = $api->key;
            $apiStatus = $api->status;
            $url = $api->api_url;
            if ($apiStatus == 0) {
                return null;
            }
            $params = [];
            $params['timestamp'] = time();
            $params['app_secret'] = $key;
            $params['bill_code'] = $billCode;
            $params['hotel_code'] = $hotelCode;
            $params['content'] = json_encode($result);
            $params['request_id'] = $requestId;
            $requestType = $api->request_type;
            $res = RequestUtil::getInstance()->requestData($url, $requestType, $params, true);
            DebugUtil::log($url);
            DebugUtil::log(json_encode($params));
            DebugUtil::log($res);
        } catch (Exception $exception) {
            DebugUtil::log($exception->getTraceAsString(), 'error');
            DebugUtil::log($exception->getMessage(), 'error');
            DebugUtil::log("hook错误信息反馈失败", 'error');
        }
    }


}